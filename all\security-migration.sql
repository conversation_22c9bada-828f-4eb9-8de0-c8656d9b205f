-- Security Enhancement Migration Script
-- Run this script to update the database for enhanced security features

-- 1. Update user table to support longer password hashes
ALTER TABLE `user` MODIFY COLUMN `password` VARCHAR(255) NOT NULL;

-- 2. Add indexes for better performance on frequently queried columns
ALTER TABLE `user` ADD INDEX `idx_email` (`email`);
ALTER TABLE `user` ADD INDEX `idx_under_userid` (`under_userid`);
ALTER TABLE `tree` ADD INDEX `idx_userid` (`userid`);
ALTER TABLE `tree` ADD INDEX `idx_sponsor_id` (`sponsor_id`);
ALTER TABLE `income` ADD INDEX `idx_userid` (`userid`);
ALTER TABLE `pin_list` ADD INDEX `idx_userid_status` (`userid`, `status`);
ALTER TABLE `join_requests` ADD INDEX `idx_email_status` (`email`, `status`);
ALTER TABLE `join_requests` ADD INDEX `idx_otp_expiry` (`otp_expiry`);

-- 3. Create rate limiting table for <PERSON><PERSON> requests
CREATE TABLE IF NOT EXISTS `rate_limits` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `identifier` varchar(100) NOT NULL COMMENT 'IP address or user identifier',
  `action` varchar(50) NOT NULL COMMENT 'Action being rate limited (e.g., otp_request)',
  `attempts` int(11) NOT NULL DEFAULT 1,
  `window_start` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `blocked_until` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_rate_limit` (`identifier`, `action`),
  KEY `idx_window_start` (`window_start`),
  KEY `idx_blocked_until` (`blocked_until`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 4. Create CSRF tokens table
CREATE TABLE IF NOT EXISTS `csrf_tokens` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `token` varchar(64) NOT NULL,
  `user_session` varchar(128) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `expires_at` timestamp NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `token` (`token`),
  KEY `idx_user_session` (`user_session`),
  KEY `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 5. Create error logs table for centralized logging
CREATE TABLE IF NOT EXISTS `error_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `level` enum('ERROR','WARNING','INFO','DEBUG') NOT NULL DEFAULT 'ERROR',
  `message` text NOT NULL,
  `file` varchar(255) DEFAULT NULL,
  `line` int(11) DEFAULT NULL,
  `user_id` varchar(100) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `context` json DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_level` (`level`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 6. Create email/SMS queue table for asynchronous processing
CREATE TABLE IF NOT EXISTS `message_queue` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` enum('email','sms','whatsapp') NOT NULL,
  `recipient` varchar(255) NOT NULL,
  `subject` varchar(255) DEFAULT NULL,
  `message` text NOT NULL,
  `priority` tinyint(4) NOT NULL DEFAULT 5 COMMENT '1=highest, 10=lowest',
  `status` enum('pending','processing','sent','failed','cancelled') NOT NULL DEFAULT 'pending',
  `attempts` int(11) NOT NULL DEFAULT 0,
  `max_attempts` int(11) NOT NULL DEFAULT 3,
  `scheduled_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `processed_at` timestamp NULL DEFAULT NULL,
  `error_message` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_status_priority` (`status`, `priority`),
  KEY `idx_scheduled_at` (`scheduled_at`),
  KEY `idx_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 7. Update join_requests table to include rate limiting fields
ALTER TABLE `join_requests` 
ADD COLUMN `ip_address` varchar(45) DEFAULT NULL AFTER `status`,
ADD COLUMN `user_agent` text DEFAULT NULL AFTER `ip_address`;

-- 8. Add completion token expiry to join_requests
ALTER TABLE `join_requests` 
ADD COLUMN `completion_token_expiry` timestamp NULL DEFAULT NULL AFTER `completion_token`;

-- 9. Create audit log table for tracking important actions
CREATE TABLE IF NOT EXISTS `audit_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(100) DEFAULT NULL,
  `action` varchar(100) NOT NULL,
  `table_name` varchar(100) DEFAULT NULL,
  `record_id` varchar(100) DEFAULT NULL,
  `old_values` json DEFAULT NULL,
  `new_values` json DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 10. Add session management table for better security
CREATE TABLE IF NOT EXISTS `user_sessions` (
  `id` varchar(128) NOT NULL,
  `user_id` varchar(100) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_activity` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `expires_at` timestamp NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `idx_last_activity` (`last_activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
