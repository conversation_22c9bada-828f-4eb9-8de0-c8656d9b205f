<?php
/**
 * Enhanced Pin Request System with Wise Payment Integration
 * Modernizes the pin request process with automated payment processing
 */

require('php-includes/connect.php');
include('php-includes/check-login.php');
include('php-includes/wise-api.php');

$email = $_SESSION['userid'];
$product_amount = 300; // R300 per pin

// Handle pin request with Wise payment
if(isset($_POST['request_pins'])) {
    $amount = mysqli_real_escape_string($con, $_POST['amount']);
    $payment_method = mysqli_real_escape_string($con, $_POST['payment_method']);
    
    if($amount >= $product_amount && $amount % $product_amount == 0) {
        $no_of_pins = $amount / $product_amount;
        
        if($payment_method == 'wise') {
            // Create Wise payment request
            $wise = new WiseAPI();
            $payment_data = [
                'amount' => $amount,
                'currency' => 'ZAR',
                'reference' => 'PIN_REQUEST_' . $email . '_' . time(),
                'description' => "Pin purchase: $no_of_pins pins for MLM recruitment"
            ];
            
            $payment_result = $wise->createPaymentRequest($payment_data);
            
            if($payment_result['success']) {
                // Store pending pin request
                mysqli_query($con, "INSERT INTO pin_request_enhanced 
                    (email, amount, no_of_pins, payment_method, wise_payment_id, status, date) 
                    VALUES ('$email', '$amount', '$no_of_pins', 'wise', '{$payment_result['payment_id']}', 'pending_payment', NOW())");
                
                // Redirect to Wise payment page
                header("Location: " . $payment_result['payment_url']);
                exit;
            } else {
                $error_message = "Failed to create Wise payment: " . $payment_result['error'];
            }
        } else {
            // Manual payment - traditional flow
            mysqli_query($con, "INSERT INTO pin_request 
                (email, amount, date) 
                VALUES ('$email', '$amount', NOW())");
            
            $success_message = "Pin request submitted successfully. Admin will process your request.";
        }
    } else {
        $error_message = "Amount must be a multiple of R$product_amount";
    }
}

// Get user's pin requests
$pin_requests = mysqli_query($con, "
    SELECT pr.*, pre.payment_method, pre.wise_payment_id, pre.no_of_pins
    FROM pin_request pr
    LEFT JOIN pin_request_enhanced pre ON pr.email = pre.email AND pr.amount = pre.amount
    WHERE pr.email='$email' 
    ORDER BY pr.id DESC
");

// Get user's available pins
$available_pins = mysqli_query($con, "
    SELECT * FROM pin_list 
    WHERE userid='$email' AND status='open' 
    ORDER BY id DESC
");

$available_pin_count = mysqli_num_rows($available_pins);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>MLM Website - Enhanced Pin Request</title>
    
    <!-- Bootstrap Core CSS -->
    <link href="vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="vendor/metisMenu/metisMenu.min.css" rel="stylesheet">
    <link href="dist/css/sb-admin-2.css" rel="stylesheet">
    <link href="vendor/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css">
</head>

<body>
    <div id="wrapper">
        <!-- Navigation -->
        <?php include('php-includes/menu.php'); ?>

        <!-- Page Content -->
        <div id="page-wrapper">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-lg-12">
                        <h1 class="page-header">Enhanced Pin Request System</h1>
                    </div>
                </div>

                <?php if(isset($success_message)): ?>
                <div class="alert alert-success">
                    <i class="fa fa-check-circle"></i> <?php echo $success_message; ?>
                </div>
                <?php endif; ?>

                <?php if(isset($error_message)): ?>
                <div class="alert alert-danger">
                    <i class="fa fa-exclamation-circle"></i> <?php echo $error_message; ?>
                </div>
                <?php endif; ?>

                <!-- Pin Status Dashboard -->
                <div class="row">
                    <div class="col-lg-3 col-md-6">
                        <div class="panel panel-primary">
                            <div class="panel-heading">
                                <div class="row">
                                    <div class="col-xs-3">
                                        <i class="fa fa-key fa-5x"></i>
                                    </div>
                                    <div class="col-xs-9 text-right">
                                        <div class="huge"><?php echo $available_pin_count; ?></div>
                                        <div>Available Pins</div>
                                    </div>
                                </div>
                            </div>
                            <div class="panel-footer">
                                <span class="pull-left">Ready to use</span>
                                <div class="clearfix"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="panel panel-green">
                            <div class="panel-heading">
                                <div class="row">
                                    <div class="col-xs-3">
                                        <i class="fa fa-money fa-5x"></i>
                                    </div>
                                    <div class="col-xs-9 text-right">
                                        <div class="huge">R<?php echo $product_amount; ?></div>
                                        <div>Per Pin</div>
                                    </div>
                                </div>
                            </div>
                            <div class="panel-footer">
                                <span class="pull-left">Current price</span>
                                <div class="clearfix"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pin Request Form -->
                <div class="row">
                    <div class="col-lg-6">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h4><i class="fa fa-shopping-cart"></i> Request New Pins</h4>
                            </div>
                            <div class="panel-body">
                                <form method="post">
                                    <div class="form-group">
                                        <label>Number of Pins</label>
                                        <select name="pins" class="form-control" id="pin-selector" required>
                                            <option value="">Select number of pins</option>
                                            <option value="1">1 Pin - R300</option>
                                            <option value="2">2 Pins - R600</option>
                                            <option value="5">5 Pins - R1,500</option>
                                            <option value="10">10 Pins - R3,000</option>
                                            <option value="20">20 Pins - R6,000</option>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label>Total Amount</label>
                                        <input type="number" name="amount" id="amount" class="form-control" readonly required>
                                    </div>

                                    <div class="form-group">
                                        <label>Payment Method</label>
                                        <div class="radio">
                                            <label>
                                                <input type="radio" name="payment_method" value="wise" checked>
                                                <i class="fa fa-credit-card"></i> Wise Payment (Instant Processing)
                                            </label>
                                        </div>
                                        <div class="radio">
                                            <label>
                                                <input type="radio" name="payment_method" value="manual">
                                                <i class="fa fa-user"></i> Manual Payment (Admin Approval Required)
                                            </label>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <button type="submit" name="request_pins" class="btn btn-primary btn-lg">
                                            <i class="fa fa-shopping-cart"></i> Request Pins
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="panel panel-info">
                            <div class="panel-heading">
                                <h4><i class="fa fa-info-circle"></i> How Pin System Works</h4>
                            </div>
                            <div class="panel-body">
                                <h5>1. Purchase Pins</h5>
                                <p>Buy pins at R<?php echo $product_amount; ?> each to recruit new members into your network.</p>

                                <h5>2. Use for Recruitment</h5>
                                <p>Each pin allows you to sponsor one new member. Pins are single-use only.</p>

                                <h5>3. Earn Commissions</h5>
                                <p>When you recruit someone, you earn commissions from their network growth.</p>

                                <h5>4. Payment Options</h5>
                                <ul>
                                    <li><strong>Wise Payment:</strong> Instant processing, immediate pin availability</li>
                                    <li><strong>Manual Payment:</strong> Traditional method, requires admin approval</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pin Request History -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h4><i class="fa fa-history"></i> Pin Request History</h4>
                            </div>
                            <div class="panel-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Date</th>
                                                <th>Amount</th>
                                                <th>Pins</th>
                                                <th>Payment Method</th>
                                                <th>Status</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php while($request = mysqli_fetch_assoc($pin_requests)): ?>
                                            <tr>
                                                <td><?php echo date('M j, Y', strtotime($request['date'])); ?></td>
                                                <td>R<?php echo number_format($request['amount']); ?></td>
                                                <td><?php echo $request['no_of_pins'] ?? ($request['amount'] / $product_amount); ?></td>
                                                <td>
                                                    <?php if($request['payment_method'] == 'wise'): ?>
                                                    <span class="label label-info">Wise Payment</span>
                                                    <?php else: ?>
                                                    <span class="label label-default">Manual</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    $statusClass = $request['status'] == 'close' ? 'success' : 'warning';
                                                    $statusText = $request['status'] == 'close' ? 'Completed' : 'Pending';
                                                    ?>
                                                    <span class="label label-<?php echo $statusClass; ?>">
                                                        <?php echo $statusText; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if($request['wise_payment_id'] && $request['status'] != 'close'): ?>
                                                    <a href="check-payment-status.php?id=<?php echo $request['wise_payment_id']; ?>" 
                                                       class="btn btn-sm btn-info">Check Status</a>
                                                    <?php else: ?>
                                                    -
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                            <?php endwhile; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Available Pins -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="panel panel-success">
                            <div class="panel-heading">
                                <h4><i class="fa fa-key"></i> Your Available Pins</h4>
                            </div>
                            <div class="panel-body">
                                <?php if($available_pin_count > 0): ?>
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Pin Number</th>
                                                <th>Status</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php while($pin = mysqli_fetch_assoc($available_pins)): ?>
                                            <tr>
                                                <td><code><?php echo $pin['pin']; ?></code></td>
                                                <td><span class="label label-success">Available</span></td>
                                                <td>
                                                    <a href="join-sponsor.php?pin=<?php echo $pin['pin']; ?>" 
                                                       class="btn btn-sm btn-primary">Use for Recruitment</a>
                                                </td>
                                            </tr>
                                            <?php endwhile; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <?php else: ?>
                                <div class="alert alert-info">
                                    <i class="fa fa-info-circle"></i> You have no available pins. Request new pins above to start recruiting.
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="vendor/jquery/jquery.min.js"></script>
    <script src="vendor/bootstrap/js/bootstrap.min.js"></script>
    <script src="vendor/metisMenu/metisMenu.min.js"></script>
    <script src="dist/js/sb-admin-2.js"></script>

    <script>
    // Calculate amount based on pin selection
    $('#pin-selector').change(function() {
        var pins = $(this).val();
        var amount = pins * <?php echo $product_amount; ?>;
        $('#amount').val(amount);
    });
    </script>
</body>
</html>
