<?php
/**
 * Database Setup and Verification Script
 * This script helps create and verify the MLM database setup
 */

// Database configuration
$db_host = "localhost";
$db_user = "root";
$db_pass = "";
$db_name = "mlm";
$db_port = 3308; // Using 3308 since 3306 is occupied by another server

echo "<h2>MLM Database Setup</h2>";

// Function to test connection
function testConnection($host, $user, $pass, $port) {
    $connection = @mysqli_connect($host, $user, $pass, '', $port);
    return $connection;
}

// Find working MySQL port
$working_port = null;
$working_connection = null;

foreach ([3308, 3306, 3307] as $port) {
    echo "<p>Testing MySQL connection on port $port...</p>";
    $test_conn = testConnection($db_host, $db_user, $db_pass, $port);
    
    if ($test_conn) {
        echo "<p style='color: green;'>✅ MySQL is running on port $port</p>";
        $working_port = $port;
        $working_connection = $test_conn;
        break;
    } else {
        echo "<p style='color: red;'>❌ No MySQL on port $port</p>";
    }
}

if (!$working_connection) {
    echo "<div style='background: #ffebee; border: 1px solid #f44336; padding: 20px; margin: 20px; border-radius: 5px;'>";
    echo "<h3 style='color: #d32f2f;'>❌ MySQL Not Running</h3>";
    echo "<p>Please start MySQL in XAMPP Control Panel and refresh this page.</p>";
    echo "<ol>";
    echo "<li>Open XAMPP Control Panel</li>";
    echo "<li>Click 'Start' next to MySQL</li>";
    echo "<li>Wait for green 'Running' status</li>";
    echo "<li>Refresh this page</li>";
    echo "</ol>";
    echo "</div>";
    exit;
}

echo "<h3>✅ MySQL Connection Successful</h3>";
echo "<p>Using port: $working_port</p>";

// Check if MLM database exists
$db_exists = false;
$result = mysqli_query($working_connection, "SHOW DATABASES LIKE '$db_name'");
if (mysqli_num_rows($result) > 0) {
    echo "<p style='color: green;'>✅ Database '$db_name' already exists</p>";
    $db_exists = true;
} else {
    echo "<p style='color: orange;'>⚠️ Database '$db_name' does not exist</p>";
}

// Create database if it doesn't exist
if (!$db_exists) {
    echo "<h3>Creating Database...</h3>";
    $create_db_sql = "CREATE DATABASE $db_name CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
    
    if (mysqli_query($working_connection, $create_db_sql)) {
        echo "<p style='color: green;'>✅ Database '$db_name' created successfully</p>";
        $db_exists = true;
    } else {
        echo "<p style='color: red;'>❌ Failed to create database: " . mysqli_error($working_connection) . "</p>";
    }
}

// Connect to the MLM database
if ($db_exists) {
    mysqli_close($working_connection);
    $mlm_connection = mysqli_connect($db_host, $db_user, $db_pass, $db_name, $working_port);
    
    if ($mlm_connection) {
        echo "<p style='color: green;'>✅ Connected to MLM database</p>";
        
        // Check for existing tables
        echo "<h3>Checking Database Tables...</h3>";
        
        $required_tables = [
            'user' => 'User accounts',
            'tree' => 'MLM tree structure',
            'income' => 'User income tracking',
            'pin_list' => 'PIN management',
            'stitch_config' => 'Stitch API configuration',
            'stitch_recipients' => 'User payout accounts',
            'stitch_payout_batches' => 'Payout batch tracking',
            'stitch_payouts' => 'Individual payouts',
            'commission_queue' => 'Commission processing queue'
        ];
        
        $missing_tables = [];
        
        foreach ($required_tables as $table => $description) {
            $table_check = mysqli_query($mlm_connection, "SHOW TABLES LIKE '$table'");
            if (mysqli_num_rows($table_check) > 0) {
                echo "<p style='color: green;'>✅ Table '$table' exists ($description)</p>";
            } else {
                echo "<p style='color: red;'>❌ Table '$table' missing ($description)</p>";
                $missing_tables[] = $table;
            }
        }
        
        if (!empty($missing_tables)) {
            echo "<div style='background: #fff3cd; border: 1px solid #ffc107; padding: 20px; margin: 20px; border-radius: 5px;'>";
            echo "<h3 style='color: #856404;'>⚠️ Missing Tables Detected</h3>";
            echo "<p>You need to import the database schema. Missing tables:</p>";
            echo "<ul>";
            foreach ($missing_tables as $table) {
                echo "<li>$table</li>";
            }
            echo "</ul>";
            echo "<h4>Next Steps:</h4>";
            echo "<ol>";
            echo "<li>Import your existing MLM database schema</li>";
            echo "<li>Run the Stitch tables SQL: <code>database/stitch_tables.sql</code></li>";
            echo "<li>Refresh this page to verify</li>";
            echo "</ol>";
            echo "</div>";
        } else {
            echo "<div style='background: #d4edda; border: 1px solid #28a745; padding: 20px; margin: 20px; border-radius: 5px;'>";
            echo "<h3 style='color: #155724;'>🎉 Database Setup Complete!</h3>";
            echo "<p>All required tables are present. Your MLM system is ready to use.</p>";
            echo "</div>";
        }
        
        mysqli_close($mlm_connection);
    }
}

// Update connect.php files with working port
if ($working_port) {
    echo "<h3>Updating Configuration Files...</h3>";
    
    $connect_files = [
        'php-includes/connect.php',
        'admin/php-includes/connect.php'
    ];
    
    foreach ($connect_files as $file) {
        if (file_exists($file)) {
            $content = file_get_contents($file);
            $updated_content = preg_replace('/\$db_port = "[0-9]+";/', '$db_port = "' . $working_port . '";', $content);
            
            if (file_put_contents($file, $updated_content)) {
                echo "<p style='color: green;'>✅ Updated $file with port $working_port</p>";
            } else {
                echo "<p style='color: red;'>❌ Failed to update $file</p>";
            }
        }
    }
}

echo "<h3>Summary</h3>";
echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 20px; margin: 20px; border-radius: 5px;'>";
echo "<p><strong>Database Host:</strong> $db_host</p>";
echo "<p><strong>Database Port:</strong> $working_port</p>";
echo "<p><strong>Database Name:</strong> $db_name</p>";
echo "<p><strong>Status:</strong> " . ($db_exists ? "Ready" : "Needs Setup") . "</p>";
echo "</div>";

if ($db_exists && empty($missing_tables)) {
    echo "<h3>🚀 Ready to Proceed</h3>";
    echo "<p>Your database is ready! You can now:</p>";
    echo "<ul>";
    echo "<li><a href='index.php'>Access the main application</a></li>";
    echo "<li><a href='admin/login.php'>Access the admin panel</a></li>";
    echo "<li><a href='setup-stitch-config.php'>Configure Stitch Payouts</a></li>";
    echo "</ul>";
}
?>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    background: #f8f9fa;
}
h2, h3 { 
    color: #333; 
    border-bottom: 2px solid #007bff;
    padding-bottom: 5px;
}
code { 
    background: #f0f0f0; 
    padding: 2px 4px; 
    border-radius: 3px; 
    font-family: 'Courier New', monospace;
}
a {
    color: #007bff;
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
</style>
