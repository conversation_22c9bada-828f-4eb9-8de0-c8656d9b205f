<?php
/**
 * Secure Database Utility Class
 * Provides secure database operations using prepared statements
 */
class SecureDatabase {
    
    private static $con;
    
    /**
     * Initialize with database connection
     * 
     * @param mysqli $connection Database connection
     */
    public static function init($connection) {
        self::$con = $connection;
    }
    
    /**
     * Execute a prepared SELECT query
     * 
     * @param string $query SQL query with placeholders
     * @param array $params Parameters for the query
     * @param string $types Parameter types (e.g., 'ssi' for string, string, int)
     * @return mysqli_result|false Query result or false on failure
     */
    public static function select($query, $params = [], $types = '') {
        if (!self::$con) {
            throw new Exception("Database not initialized");
        }
        
        try {
            if (empty($params)) {
                $result = self::$con->query($query);
                if (!$result) {
                    ErrorHandler::logError("Query failed: " . self::$con->error, ['query' => $query]);
                    return false;
                }
                return $result;
            }
            
            $stmt = self::$con->prepare($query);
            if (!$stmt) {
                ErrorHandler::logError("Prepare failed: " . self::$con->error, ['query' => $query]);
                return false;
            }
            
            if (!empty($params)) {
                if (empty($types)) {
                    $types = str_repeat('s', count($params)); // Default to string
                }
                $stmt->bind_param($types, ...$params);
            }
            
            $stmt->execute();
            $result = $stmt->get_result();
            $stmt->close();
            
            return $result;
        } catch (Exception $e) {
            ErrorHandler::logError("Database select error: " . $e->getMessage(), [
                'query' => $query,
                'params' => $params
            ]);
            return false;
        }
    }
    
    /**
     * Execute a prepared INSERT query
     * 
     * @param string $query SQL query with placeholders
     * @param array $params Parameters for the query
     * @param string $types Parameter types
     * @return int|false Insert ID or false on failure
     */
    public static function insert($query, $params = [], $types = '') {
        if (!self::$con) {
            throw new Exception("Database not initialized");
        }
        
        try {
            $stmt = self::$con->prepare($query);
            if (!$stmt) {
                ErrorHandler::logError("Prepare failed: " . self::$con->error, ['query' => $query]);
                return false;
            }
            
            if (!empty($params)) {
                if (empty($types)) {
                    $types = str_repeat('s', count($params));
                }
                $stmt->bind_param($types, ...$params);
            }
            
            $result = $stmt->execute();
            $insert_id = self::$con->insert_id;
            $stmt->close();
            
            if (!$result) {
                ErrorHandler::logError("Insert failed: " . self::$con->error, [
                    'query' => $query,
                    'params' => $params
                ]);
                return false;
            }
            
            return $insert_id;
        } catch (Exception $e) {
            ErrorHandler::logError("Database insert error: " . $e->getMessage(), [
                'query' => $query,
                'params' => $params
            ]);
            return false;
        }
    }
    
    /**
     * Execute a prepared UPDATE query
     * 
     * @param string $query SQL query with placeholders
     * @param array $params Parameters for the query
     * @param string $types Parameter types
     * @return int|false Number of affected rows or false on failure
     */
    public static function update($query, $params = [], $types = '') {
        if (!self::$con) {
            throw new Exception("Database not initialized");
        }
        
        try {
            $stmt = self::$con->prepare($query);
            if (!$stmt) {
                ErrorHandler::logError("Prepare failed: " . self::$con->error, ['query' => $query]);
                return false;
            }
            
            if (!empty($params)) {
                if (empty($types)) {
                    $types = str_repeat('s', count($params));
                }
                $stmt->bind_param($types, ...$params);
            }
            
            $result = $stmt->execute();
            $affected_rows = $stmt->affected_rows;
            $stmt->close();
            
            if (!$result) {
                ErrorHandler::logError("Update failed: " . self::$con->error, [
                    'query' => $query,
                    'params' => $params
                ]);
                return false;
            }
            
            return $affected_rows;
        } catch (Exception $e) {
            ErrorHandler::logError("Database update error: " . $e->getMessage(), [
                'query' => $query,
                'params' => $params
            ]);
            return false;
        }
    }
    
    /**
     * Execute a prepared DELETE query
     * 
     * @param string $query SQL query with placeholders
     * @param array $params Parameters for the query
     * @param string $types Parameter types
     * @return int|false Number of affected rows or false on failure
     */
    public static function delete($query, $params = [], $types = '') {
        if (!self::$con) {
            throw new Exception("Database not initialized");
        }
        
        try {
            $stmt = self::$con->prepare($query);
            if (!$stmt) {
                ErrorHandler::logError("Prepare failed: " . self::$con->error, ['query' => $query]);
                return false;
            }
            
            if (!empty($params)) {
                if (empty($types)) {
                    $types = str_repeat('s', count($params));
                }
                $stmt->bind_param($types, ...$params);
            }
            
            $result = $stmt->execute();
            $affected_rows = $stmt->affected_rows;
            $stmt->close();
            
            if (!$result) {
                ErrorHandler::logError("Delete failed: " . self::$con->error, [
                    'query' => $query,
                    'params' => $params
                ]);
                return false;
            }
            
            return $affected_rows;
        } catch (Exception $e) {
            ErrorHandler::logError("Database delete error: " . $e->getMessage(), [
                'query' => $query,
                'params' => $params
            ]);
            return false;
        }
    }
    
    /**
     * Execute a transaction with multiple queries
     * 
     * @param callable $callback Function containing the transaction logic
     * @return mixed Result of the callback or false on failure
     */
    public static function transaction($callback) {
        if (!self::$con) {
            throw new Exception("Database not initialized");
        }
        
        try {
            self::$con->autocommit(false);
            self::$con->begin_transaction();
            
            $result = $callback();
            
            if ($result === false) {
                self::$con->rollback();
                ErrorHandler::logError("Transaction rolled back due to callback returning false");
                return false;
            }
            
            self::$con->commit();
            self::$con->autocommit(true);
            
            return $result;
        } catch (Exception $e) {
            self::$con->rollback();
            self::$con->autocommit(true);
            ErrorHandler::logError("Transaction failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get a single row from a query
     * 
     * @param string $query SQL query
     * @param array $params Parameters
     * @param string $types Parameter types
     * @return array|null Single row or null if not found
     */
    public static function getRow($query, $params = [], $types = '') {
        $result = self::select($query, $params, $types);
        if (!$result) {
            return null;
        }
        
        $row = $result->fetch_assoc();
        $result->free();
        
        return $row;
    }
    
    /**
     * Get all rows from a query
     * 
     * @param string $query SQL query
     * @param array $params Parameters
     * @param string $types Parameter types
     * @return array Array of rows
     */
    public static function getRows($query, $params = [], $types = '') {
        $result = self::select($query, $params, $types);
        if (!$result) {
            return [];
        }
        
        $rows = [];
        while ($row = $result->fetch_assoc()) {
            $rows[] = $row;
        }
        $result->free();
        
        return $rows;
    }
    
    /**
     * Get a single value from a query
     * 
     * @param string $query SQL query
     * @param array $params Parameters
     * @param string $types Parameter types
     * @return mixed Single value or null if not found
     */
    public static function getValue($query, $params = [], $types = '') {
        $result = self::select($query, $params, $types);
        if (!$result) {
            return null;
        }
        
        $row = $result->fetch_row();
        $result->free();
        
        return $row ? $row[0] : null;
    }
    
    /**
     * Check if a record exists
     * 
     * @param string $table Table name
     * @param array $conditions Conditions as key-value pairs
     * @return bool True if record exists
     */
    public static function exists($table, $conditions) {
        $where_parts = [];
        $params = [];
        
        foreach ($conditions as $column => $value) {
            $where_parts[] = "`$column` = ?";
            $params[] = $value;
        }
        
        $query = "SELECT 1 FROM `$table` WHERE " . implode(' AND ', $where_parts) . " LIMIT 1";
        $result = self::getValue($query, $params);
        
        return $result !== null;
    }
    
    /**
     * Escape output for HTML display (XSS prevention)
     * 
     * @param string $value Value to escape
     * @return string Escaped value
     */
    public static function escapeOutput($value) {
        return htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
    }
}
?>
