{"name": "mlm-system/binary-plan", "description": "MLM Website Using PHP - Binary Plan with Enhanced Security", "type": "project", "license": "MIT", "authors": [{"name": "MLM System Developer", "email": "<EMAIL>"}], "require": {"php": ">=7.4", "phpmailer/phpmailer": "^6.8", "monolog/monolog": "^3.0", "vlucas/phpdotenv": "^5.5", "ramsey/uuid": "^4.7"}, "require-dev": {"phpunit/phpunit": "^9.6", "squizlabs/php_codesniffer": "^3.7"}, "autoload": {"psr-4": {"MLMSystem\\": "src/", "MLMSystem\\Security\\": "php-includes/security/", "MLMSystem\\Database\\": "php-includes/database/", "MLMSystem\\Queue\\": "php-includes/queue/"}, "files": ["php-includes/functions.php"]}, "autoload-dev": {"psr-4": {"MLMSystem\\Tests\\": "tests/"}}, "scripts": {"test": "phpunit", "cs-check": "phpcs --standard=PSR12 src/ php-includes/", "cs-fix": "phpcbf --standard=PSR12 src/ php-includes/", "post-install-cmd": ["@php -r \"copy('.env.example', '.env');\""], "queue-process": "php process-message-queue.php", "setup-database": "php setup-database.php"}, "config": {"optimize-autoloader": true, "sort-packages": true}, "minimum-stability": "stable", "prefer-stable": true}