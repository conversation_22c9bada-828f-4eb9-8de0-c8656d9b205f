<?php
// Enhanced security and session management
session_start();
require_once 'php-includes/config.php';
require_once 'php-includes/test-connect.php';
require_once 'php-includes/check-login.php';
require_once 'php-includes/secure-database.php';
require_once 'php-includes/csrf-protection.php';
require_once 'php-includes/rate-limiter.php';

// Initialize components
SecureDatabase::init($con);
CSRFProtection::init($con);
RateLimiter::init($con);

$userid = $_SESSION['userid'];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="Invite New Member - Ubuntu Wealth Network">
    <title>Invite New Member - Ubuntu Wealth Network</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Ubuntu:wght@300;400;500;700&family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Mobile CSS -->
    <link href="dist/css/ubuntu-mobile.css" rel="stylesheet">
    
    <style>
        .invite-hero {
            background: linear-gradient(135deg, var(--sa-green) 0%, var(--sa-blue) 100%);
            color: white;
            padding: 2rem 1rem;
            text-align: center;
            margin-bottom: 1rem;
        }
        
        .invite-hero h1 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .invite-hero p {
            opacity: 0.9;
            margin-bottom: 0;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 1rem 0;
            padding: 0 1rem;
        }
        
        .step {
            flex: 1;
            text-align: center;
            position: relative;
            max-width: 120px;
        }
        
        .step-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            color: var(--text-light);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 0.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .step.active .step-circle {
            background: var(--sa-green);
            color: white;
        }
        
        .step.completed .step-circle {
            background: var(--success-green);
            color: white;
        }
        
        .step-label {
            font-size: 0.8rem;
            color: var(--text-light);
        }
        
        .step.active .step-label {
            color: var(--sa-green);
            font-weight: 600;
        }
        
        .step-connector {
            position: absolute;
            top: 20px;
            left: 50%;
            width: 100%;
            height: 2px;
            background: #e9ecef;
            z-index: -1;
        }
        
        .step:last-child .step-connector {
            display: none;
        }
        
        .form-section {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin: 0 1rem 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .form-section h3 {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .commission-preview {
            background: linear-gradient(135deg, var(--warm-white) 0%, #ffffff 100%);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem;
            border: 2px solid var(--sa-gold);
        }
        
        .commission-level {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .commission-level:last-child {
            border-bottom: none;
        }
        
        .level-badge {
            background: var(--sa-green);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .amount {
            font-weight: 700;
            color: var(--success-green);
        }
        
        .otp-section {
            display: none;
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
            border-radius: 15px;
            padding: 1.5rem;
            margin: 1rem;
            text-align: center;
            border: 2px solid var(--success-green);
        }
        
        .otp-input {
            font-size: 2rem;
            text-align: center;
            letter-spacing: 0.5rem;
            font-weight: 700;
            border: 2px solid var(--sa-green);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            max-width: 200px;
        }
        
        .success-message {
            background: linear-gradient(135deg, var(--success-green) 0%, #20c997 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin: 1rem;
            text-align: center;
            display: none;
        }
        
        .mobile-submit-btn {
            background: linear-gradient(45deg, var(--sa-green) 0%, var(--sa-blue) 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            width: 100%;
            margin: 1rem 0;
            transition: all 0.3s ease;
        }
        
        .mobile-submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .mobile-submit-btn:disabled {
            opacity: 0.6;
            transform: none;
        }
        
        .help-text {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            font-size: 0.9rem;
            color: var(--text-light);
        }
        
        .help-text i {
            color: var(--sa-blue);
            margin-right: 0.5rem;
        }
    </style>
</head>

<body>
    <!-- Mobile Header -->
    <div class="mobile-header">
        <div class="d-flex align-items-center">
            <a href="home-new.php" class="text-white me-3" style="text-decoration: none;">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div>
                <h1>Invite New Member</h1>
                <div class="user-info">Build your Ubuntu network</div>
            </div>
        </div>
    </div>

    <!-- Hero Section -->
    <div class="invite-hero">
        <h1><i class="fas fa-users me-2"></i>Grow Your Network</h1>
        <p>Help someone start their journey to financial freedom</p>
    </div>

    <!-- Step Indicator -->
    <div class="step-indicator">
        <div class="step active" id="step1">
            <div class="step-circle">1</div>
            <div class="step-label">Details</div>
            <div class="step-connector"></div>
        </div>
        <div class="step" id="step2">
            <div class="step-circle">2</div>
            <div class="step-label">Verify</div>
            <div class="step-connector"></div>
        </div>
        <div class="step" id="step3">
            <div class="step-circle">3</div>
            <div class="step-label">Complete</div>
        </div>
    </div>

    <!-- Commission Preview -->
    <div class="commission-preview">
        <h4 class="text-center mb-3" style="color: var(--sa-green);">
            <i class="fas fa-coins me-2"></i>Your Commission Potential
        </h4>
        <div class="commission-level">
            <div>
                <span class="level-badge">Level 1</span>
                <small class="d-block text-muted">Direct referral</small>
            </div>
            <div class="amount">R1,000</div>
        </div>
        <div class="commission-level">
            <div>
                <span class="level-badge">Level 2</span>
                <small class="d-block text-muted">When they refer</small>
            </div>
            <div class="amount">R5,000</div>
        </div>
        <div class="commission-level">
            <div>
                <span class="level-badge">Level 3+</span>
                <small class="d-block text-muted">Network growth</small>
            </div>
            <div class="amount">Up to R625,000</div>
        </div>
    </div>

    <!-- Registration Form -->
    <form id="inviteForm" method="post" action="join-sponsor.php">
        <?php echo CSRFProtection::getTokenField(); ?>
        
        <div class="form-section" id="detailsSection">
            <h3>
                <i class="fas fa-user-plus"></i>
                New Member Details
            </h3>
            
            <div class="mb-3">
                <label class="mobile-form-label">
                    <i class="fas fa-key me-2"></i>Registration Pin
                </label>
                <input type="text" name="pin" class="mobile-form-control" 
                       placeholder="Enter your registration pin" required>
                <div class="help-text">
                    <i class="fas fa-info-circle"></i>
                    Use one of your available pins to register the new member
                </div>
            </div>
            
            <div class="mb-3">
                <label class="mobile-form-label">
                    <i class="fas fa-envelope me-2"></i>Email Address
                </label>
                <input type="email" name="email" class="mobile-form-control" 
                       placeholder="<EMAIL>" required>
            </div>
            
            <div class="mb-3">
                <label class="mobile-form-label">
                    <i class="fas fa-phone me-2"></i>Mobile Number
                </label>
                <input type="tel" name="mobile" class="mobile-form-control" 
                       placeholder="+27 82 123 4567" required>
                <div class="help-text">
                    <i class="fas fa-sms me-2"></i>
                    OTP will be sent to this number via WhatsApp or SMS
                </div>
            </div>
            
            <input type="hidden" name="under_userid" value="<?php echo SecureDatabase::escapeOutput($userid); ?>">
            
            <div class="mb-3">
                <label class="mobile-form-label">
                    <i class="fas fa-sitemap me-2"></i>Matrix Position
                </label>
                <select name="matrix_position" class="mobile-form-control" required>
                    <option value="">Select position (1-5)</option>
                    <option value="1">Position 1</option>
                    <option value="2">Position 2</option>
                    <option value="3">Position 3</option>
                    <option value="4">Position 4</option>
                    <option value="5">Position 5</option>
                </select>
                <div class="help-text">
                    <i class="fas fa-lightbulb me-2"></i>
                    Choose an available position in your 5×5 matrix
                </div>
            </div>
            
            <button type="submit" name="initiate_join" class="mobile-submit-btn">
                <i class="fas fa-paper-plane me-2"></i>
                Send Invitation
            </button>
        </div>
    </form>

    <!-- OTP Verification Section -->
    <div class="otp-section" id="otpSection">
        <h3 style="color: var(--success-green); margin-bottom: 1rem;">
            <i class="fas fa-shield-alt me-2"></i>
            OTP Verification
        </h3>
        <p style="margin-bottom: 1.5rem;">
            An OTP has been sent to the new member's mobile number.
            Ask them for the 6-digit code to complete registration.
        </p>

        <form method="post" action="join-sponsor.php">
            <?php echo CSRFProtection::getTokenField(); ?>
            <input type="text" name="otp" class="otp-input"
                   placeholder="000000" maxlength="6" required>
            <button type="submit" name="verify_otp" class="mobile-submit-btn">
                <i class="fas fa-check-circle me-2"></i>
                Verify & Complete Registration
            </button>
        </form>

        <div class="help-text mt-3">
            <i class="fas fa-clock me-2"></i>
            OTP expires in 15 minutes. If not received, check WhatsApp or SMS.
        </div>
    </div>

    <!-- Success Message -->
    <div class="success-message" id="successMessage">
        <h3><i class="fas fa-check-circle me-2"></i>Registration Complete!</h3>
        <p>The new member has been successfully added to your network.
           They will receive an email with login instructions.</p>
        <a href="home-new.php" class="btn btn-light mt-3">
            <i class="fas fa-home me-2"></i>
            Back to Dashboard
        </a>
    </div>

    <!-- Mobile Navigation -->
    <div class="mobile-nav d-flex">
        <a href="home-new.php" class="mobile-nav-item">
            <i class="fas fa-home"></i>
            <span>Home</span>
        </a>
        <a href="join-sponsor-mobile.php" class="mobile-nav-item active">
            <i class="fas fa-user-plus"></i>
            <span>Invite</span>
        </a>
        <a href="income.php" class="mobile-nav-item">
            <i class="fas fa-chart-line"></i>
            <span>Income</span>
        </a>
        <a href="tree.php" class="mobile-nav-item">
            <i class="fas fa-sitemap"></i>
            <span>Matrix</span>
        </a>
        <a href="profile.php" class="mobile-nav-item">
            <i class="fas fa-user"></i>
            <span>Profile</span>
        </a>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Form handling and step progression
        document.getElementById('inviteForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // Validate form
            const form = this;
            const formData = new FormData(form);

            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';
            submitBtn.disabled = true;

            // Simulate API call (replace with actual form submission)
            setTimeout(() => {
                // Update step indicator
                document.getElementById('step1').classList.remove('active');
                document.getElementById('step1').classList.add('completed');
                document.getElementById('step2').classList.add('active');

                // Hide form and show OTP section
                document.getElementById('detailsSection').style.display = 'none';
                document.getElementById('otpSection').style.display = 'block';

                // Reset button
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;

                // Scroll to OTP section
                document.getElementById('otpSection').scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });
            }, 2000);
        });

        // OTP input formatting
        const otpInput = document.querySelector('.otp-input');
        if (otpInput) {
            otpInput.addEventListener('input', function(e) {
                // Only allow numbers
                this.value = this.value.replace(/[^0-9]/g, '');

                // Auto-submit when 6 digits entered
                if (this.value.length === 6) {
                    // Add visual feedback
                    this.style.borderColor = 'var(--success-green)';
                    this.style.backgroundColor = '#e8f5e8';
                }
            });
        }

        // Matrix position availability check
        const positionSelect = document.querySelector('select[name="matrix_position"]');
        if (positionSelect) {
            positionSelect.addEventListener('change', function() {
                // In a real implementation, this would check position availability
                // For now, just provide visual feedback
                if (this.value) {
                    this.style.borderColor = 'var(--success-green)';
                }
            });
        }

        // Mobile form enhancements
        document.querySelectorAll('.mobile-form-control').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
                this.parentElement.style.transition = 'transform 0.2s ease';
            });

            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });

        // Phone number formatting
        const phoneInput = document.querySelector('input[name="mobile"]');
        if (phoneInput) {
            phoneInput.addEventListener('input', function(e) {
                let value = this.value.replace(/\D/g, '');

                // Add South African country code if not present
                if (value.length > 0 && !value.startsWith('27')) {
                    if (value.startsWith('0')) {
                        value = '27' + value.substring(1);
                    } else if (!value.startsWith('27')) {
                        value = '27' + value;
                    }
                }

                // Format as +27 XX XXX XXXX
                if (value.length >= 2) {
                    value = '+' + value.substring(0, 2) + ' ' +
                           value.substring(2, 4) + ' ' +
                           value.substring(4, 7) + ' ' +
                           value.substring(7, 11);
                }

                this.value = value;
            });
        }

        // Success message handling
        if (window.location.search.includes('success=1')) {
            // Update step indicator
            document.getElementById('step2').classList.remove('active');
            document.getElementById('step2').classList.add('completed');
            document.getElementById('step3').classList.add('active');
            document.getElementById('step3').classList.add('completed');

            // Show success message
            document.getElementById('detailsSection').style.display = 'none';
            document.getElementById('otpSection').style.display = 'none';
            document.getElementById('successMessage').style.display = 'block';

            // Confetti effect (simple)
            setTimeout(() => {
                for (let i = 0; i < 50; i++) {
                    createConfetti();
                }
            }, 500);
        }

        function createConfetti() {
            const confetti = document.createElement('div');
            confetti.style.cssText = `
                position: fixed;
                width: 10px;
                height: 10px;
                background: var(--sa-gold);
                top: -10px;
                left: ${Math.random() * 100}vw;
                z-index: 9999;
                border-radius: 50%;
                pointer-events: none;
                animation: confetti-fall 3s linear forwards;
            `;

            document.body.appendChild(confetti);

            setTimeout(() => {
                confetti.remove();
            }, 3000);
        }

        // Add confetti animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes confetti-fall {
                to {
                    transform: translateY(100vh) rotate(360deg);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // Prevent zoom on iOS when focusing inputs
        document.addEventListener('touchstart', function() {}, true);
    </script>
</body>
</html>
