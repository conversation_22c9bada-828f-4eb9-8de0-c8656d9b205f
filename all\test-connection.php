<?php
/**
 * Main MySQL Connection Test
 */

echo "<h2>MLM System - MySQL Connection Test</h2>";

// Test different ports (3308 first since 3306 is occupied)
$ports_to_test = [3308, 3306, 3307];
$connection_found = false;
$working_port = null;

foreach ($ports_to_test as $port) {
    echo "<h3>Testing Port $port</h3>";
    
    $connection = @mysqli_connect('localhost', 'root', '', '', $port);
    
    if ($connection) {
        echo "<p style='color: green; font-weight: bold;'>✅ SUCCESS! MySQL is running on port $port</p>";
        
        // Test if we can create/access the MLM database
        $create_db = mysqli_query($connection, "CREATE DATABASE IF NOT EXISTS mlm");
        if ($create_db) {
            echo "<p style='color: green;'>✅ MLM database is accessible</p>";
            
            // Test connection to MLM database
            mysqli_close($connection);
            $mlm_connection = @mysqli_connect('localhost', 'root', '', 'mlm', $port);
            
            if ($mlm_connection) {
                echo "<p style='color: green;'>✅ Connected to MLM database successfully</p>";
                
                $working_port = $port;
                mysqli_close($mlm_connection);
                $connection_found = true;
                break;
            }
        }
        
        mysqli_close($connection);
    } else {
        echo "<p style='color: red;'>❌ No connection on port $port</p>";
    }
}

// Update connect.php files if we found a working connection
if ($connection_found && $working_port) {
    echo "<h3>Updating Configuration Files</h3>";
    
    $files_to_update = [
        'php-includes/connect.php',
        'admin/php-includes/connect.php'
    ];
    
    foreach ($files_to_update as $file) {
        if (file_exists($file)) {
            $content = file_get_contents($file);
            $updated_content = preg_replace('/\$db_port = "[0-9]+";/', '$db_port = "' . $working_port . '";', $content);
            
            if (file_put_contents($file, $updated_content)) {
                echo "<p style='color: blue;'>🔧 Updated $file with port $working_port</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ Could not update $file</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ File $file not found</p>";
        }
    }
}

if (!$connection_found) {
    echo "<div style='background: #ffebee; border: 1px solid #f44336; padding: 20px; margin: 20px; border-radius: 5px;'>";
    echo "<h3 style='color: #d32f2f;'>❌ MySQL Not Running</h3>";
    echo "<p><strong>XAMPP MySQL is not started. Please follow these steps:</strong></p>";
    
    echo "<h4>Step 1: Start XAMPP MySQL</h4>";
    echo "<ol>";
    echo "<li><strong>Open XAMPP Control Panel</strong> (Run as Administrator)</li>";
    echo "<li><strong>Look for MySQL row</strong> - should show 'Stopped'</li>";
    echo "<li><strong>Click 'Start' button</strong> next to MySQL</li>";
    echo "<li><strong>Wait for green 'Running' status</strong></li>";
    echo "<li><strong>Refresh this page</strong></li>";
    echo "</ol>";
    
    echo "<h4>Step 2: If MySQL Won't Start</h4>";
    echo "<ul>";
    echo "<li><strong>Port Conflict:</strong> Another service using port 3306</li>";
    echo "<li><strong>Change Port:</strong> In XAMPP, Config → my.ini → change port to 3308</li>";
    echo "<li><strong>Restart XAMPP:</strong> Stop all services, close XAMPP, restart as Admin</li>";
    echo "<li><strong>Check Logs:</strong> XAMPP Control Panel → Logs button next to MySQL</li>";
    echo "</ul>";
    
    echo "<h4>Step 3: Alternative Solutions</h4>";
    echo "<ul>";
    echo "<li><strong>Windows Services:</strong> Check if MySQL service is running</li>";
    echo "<li><strong>Firewall:</strong> Allow MySQL through Windows Firewall</li>";
    echo "<li><strong>Antivirus:</strong> Temporarily disable antivirus</li>";
    echo "<li><strong>Reinstall:</strong> Reinstall XAMPP if all else fails</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #d4edda; border: 1px solid #28a745; padding: 20px; margin: 20px; border-radius: 5px;'>";
    echo "<h3 style='color: #155724;'>🎉 Connection Successful!</h3>";
    echo "<p>MySQL is now working on port <strong>$working_port</strong>. Your MLM system is ready!</p>";
    
    echo "<h4>Next Steps:</h4>";
    echo "<ul>";
    echo "<li><a href='index.php'>Access Main Application</a></li>";
    echo "<li><a href='admin/login.php'>Access Admin Panel</a></li>";
    echo "<li><a href='setup-database.php'>Setup Database Tables</a></li>";
    echo "<li><a href='setup-stitch-config.php'>Configure Stitch Payouts</a></li>";
    echo "</ul>";
    echo "</div>";
}

echo "<h3>Troubleshooting Commands</h3>";
echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px;'>";
echo "<p><strong>Check if MySQL is running:</strong></p>";
echo "<code>netstat -ano | findstr :3306</code><br><br>";
echo "<p><strong>Kill process using port 3306:</strong></p>";
echo "<code>taskkill /PID [PID_NUMBER] /F</code><br><br>";
echo "<p><strong>Start MySQL manually:</strong></p>";
echo "<code>net start mysql</code>";
echo "</div>";
?>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    background: #f8f9fa;
}
h2, h3 { 
    color: #333; 
    border-bottom: 2px solid #007bff;
    padding-bottom: 5px;
}
code {
    background: #f0f0f0;
    padding: 5px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}
a {
    color: #007bff;
    text-decoration: none;
    font-weight: bold;
}
a:hover {
    text-decoration: underline;
}
</style>
