<?php
/**
 * Security Features Test Script
 * Run this script to test the implemented security features
 */

// Include required files
require_once __DIR__ . '/php-includes/connect.php';
require_once __DIR__ . '/php-includes/config.php';
require_once __DIR__ . '/php-includes/error-handler.php';
require_once __DIR__ . '/php-includes/secure-database.php';
require_once __DIR__ . '/php-includes/password-security.php';
require_once __DIR__ . '/php-includes/csrf-protection.php';
require_once __DIR__ . '/php-includes/rate-limiter.php';
require_once __DIR__ . '/php-includes/message-queue.php';

// Initialize components
ErrorHandler::init($con, ['display_errors' => true]);
SecureDatabase::init($con);
CSRFProtection::init($con);
RateLimiter::init($con);
MessageQueue::init($con);

echo "=== MLM System Security Features Test ===\n\n";

$tests_passed = 0;
$tests_failed = 0;

// Test 1: Password Security
echo "1. Testing Password Security...\n";
try {
    $test_password = "TestPassword123!";
    $weak_password = "123456";
    
    // Test password validation
    $validation = PasswordSecurity::validatePasswordStrength($test_password);
    if ($validation['valid']) {
        echo "   ✓ Strong password validation passed\n";
        $tests_passed++;
    } else {
        echo "   ✗ Strong password validation failed\n";
        $tests_failed++;
    }
    
    $weak_validation = PasswordSecurity::validatePasswordStrength($weak_password);
    if (!$weak_validation['valid']) {
        echo "   ✓ Weak password correctly rejected\n";
        $tests_passed++;
    } else {
        echo "   ✗ Weak password incorrectly accepted\n";
        $tests_failed++;
    }
    
    // Test password hashing
    $hash = PasswordSecurity::hashPassword($test_password);
    if (PasswordSecurity::verifyPassword($test_password, $hash)) {
        echo "   ✓ Password hashing and verification working\n";
        $tests_passed++;
    } else {
        echo "   ✗ Password hashing/verification failed\n";
        $tests_failed++;
    }
    
} catch (Exception $e) {
    echo "   ✗ Password security test failed: " . $e->getMessage() . "\n";
    $tests_failed++;
}

// Test 2: Database Security
echo "\n2. Testing Database Security...\n";
try {
    // Test prepared statements
    $test_email = 'test_' . time() . '@example.com';
    $result = SecureDatabase::insert(
        "INSERT INTO join_requests (email, pin, mobile, under_userid, matrix_position, otp, otp_expiry, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
        [$test_email, '123456', '+1234567890', 'test_user', 1, '123456', date('Y-m-d H:i:s', time() + 900), 'pending']
    );
    
    if ($result !== false) {
        echo "   ✓ Prepared statement insert working\n";
        $tests_passed++;
        
        // Test select
        $row = SecureDatabase::getRow("SELECT * FROM join_requests WHERE email = ?", [$test_email]);
        if ($row && $row['email'] === $test_email) {
            echo "   ✓ Prepared statement select working\n";
            $tests_passed++;
        } else {
            echo "   ✗ Prepared statement select failed\n";
            $tests_failed++;
        }
        
        // Cleanup
        SecureDatabase::delete("DELETE FROM join_requests WHERE email = ?", [$test_email]);
    } else {
        echo "   ✗ Prepared statement insert failed\n";
        $tests_failed++;
    }
    
} catch (Exception $e) {
    echo "   ✗ Database security test failed: " . $e->getMessage() . "\n";
    $tests_failed++;
}

// Test 3: CSRF Protection
echo "\n3. Testing CSRF Protection...\n";
try {
    session_start();
    $token = CSRFProtection::generateToken();
    if (!empty($token)) {
        echo "   ✓ CSRF token generation working\n";
        $tests_passed++;
        
        if (CSRFProtection::validateToken($token)) {
            echo "   ✓ CSRF token validation working\n";
            $tests_passed++;
        } else {
            echo "   ✗ CSRF token validation failed\n";
            $tests_failed++;
        }
    } else {
        echo "   ✗ CSRF token generation failed\n";
        $tests_failed++;
    }
    
} catch (Exception $e) {
    echo "   ✗ CSRF protection test failed: " . $e->getMessage() . "\n";
    $tests_failed++;
}

// Test 4: Rate Limiting
echo "\n4. Testing Rate Limiting...\n";
try {
    $test_ip = '*************';
    
    // Test rate limit check
    $check = RateLimiter::checkLimit($test_ip, 'otp_request');
    if (isset($check['allowed'])) {
        echo "   ✓ Rate limit check working\n";
        $tests_passed++;
        
        // Record attempt
        if (RateLimiter::recordAttempt($test_ip, 'otp_request')) {
            echo "   ✓ Rate limit recording working\n";
            $tests_passed++;
        } else {
            echo "   ✗ Rate limit recording failed\n";
            $tests_failed++;
        }
    } else {
        echo "   ✗ Rate limit check failed\n";
        $tests_failed++;
    }
    
} catch (Exception $e) {
    echo "   ✗ Rate limiting test failed: " . $e->getMessage() . "\n";
    $tests_failed++;
}

// Test 5: Message Queue
echo "\n5. Testing Message Queue...\n";
try {
    $queue_id = MessageQueue::queueEmail('<EMAIL>', 'Test Subject', 'Test Message', 5);
    if ($queue_id !== false) {
        echo "   ✓ Email queuing working\n";
        $tests_passed++;
        
        $messages = MessageQueue::getPendingMessages(1);
        if (!empty($messages)) {
            echo "   ✓ Queue retrieval working\n";
            $tests_passed++;
            
            // Mark as sent and cleanup
            MessageQueue::markAsSent($queue_id);
        } else {
            echo "   ✗ Queue retrieval failed\n";
            $tests_failed++;
        }
    } else {
        echo "   ✗ Email queuing failed\n";
        $tests_failed++;
    }
    
} catch (Exception $e) {
    echo "   ✗ Message queue test failed: " . $e->getMessage() . "\n";
    $tests_failed++;
}

// Test 6: Error Handling
echo "\n6. Testing Error Handling...\n";
try {
    ErrorHandler::logInfo("Test info message", ['test' => true]);
    ErrorHandler::logWarning("Test warning message", ['test' => true]);
    ErrorHandler::logError("Test error message", ['test' => true]);
    
    echo "   ✓ Error logging working\n";
    $tests_passed++;
    
} catch (Exception $e) {
    echo "   ✗ Error handling test failed: " . $e->getMessage() . "\n";
    $tests_failed++;
}

// Test 7: Configuration
echo "\n7. Testing Configuration...\n";
try {
    $db_host = Config::get('database.host');
    $app_env = Config::get('app.env');
    
    if (!empty($db_host) && !empty($app_env)) {
        echo "   ✓ Configuration loading working\n";
        $tests_passed++;
    } else {
        echo "   ✗ Configuration loading failed\n";
        $tests_failed++;
    }
    
} catch (Exception $e) {
    echo "   ✗ Configuration test failed: " . $e->getMessage() . "\n";
    $tests_failed++;
}

// Summary
echo "\n=== Test Results ===\n";
echo "Tests Passed: $tests_passed\n";
echo "Tests Failed: $tests_failed\n";
echo "Total Tests: " . ($tests_passed + $tests_failed) . "\n";

if ($tests_failed === 0) {
    echo "\n🎉 All security features are working correctly!\n";
    exit(0);
} else {
    echo "\n⚠️  Some tests failed. Please check the implementation.\n";
    exit(1);
}
?>
