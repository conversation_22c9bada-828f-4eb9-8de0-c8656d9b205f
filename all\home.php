<?php
include('php-includes/check-login.php');
include('php-includes/connect.php');
$userid = $_SESSION['userid'];
?>
<!DOCTYPE html>
<html lang="en">

<head>

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>Mlml Website  - Home</title>

    <!-- Bootstrap Core CSS -->
    <link href="vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">

    <!-- MetisMenu CSS -->
    <link href="vendor/metisMenu/metisMenu.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="dist/css/sb-admin-2.css" rel="stylesheet">

    <!-- Custom Fonts -->
    <link href="vendor/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css">



</head>

<body>

    <div id="wrapper">

        <!-- Navigation -->
        <?php include('php-includes/menu.php'); ?>

        <!-- Page Content -->
        <div id="page-wrapper">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-lg-12">
                        <h1 class="page-header">User Home</h1>
                    </div>
                    <!-- /.col-lg-12 -->
                </div>
                <!-- /.row -->
                <div class="row">
                	 <?php
						$query = mysqli_query($con,"select * from income where userid='$userid'");
						$result = mysqli_fetch_array($query);

						// Get tree data
						$tree_query = mysqli_query($con,"select * from tree where userid='$userid'");
						$tree_result = mysqli_fetch_array($tree_query);
						$matrix_count = $tree_result['matrix_count'];
						$matrix_level = $result['matrix_level'];
					?>
                	<div class="col-lg-3">
                    	<div class="panel panel-info">
                        	<div class="panel-heading">
                            	<h4 class="panel-title">Monthly Income</h4>
                            </div>
                            <div class="panel-body">
                            	R <?php echo $result['month_bal']; ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3">
                    	<div class="panel panel-success">
                        	<div class="panel-heading">
                            	<h4 class="panel-title">Current Balance</h4>
                            </div>
                            <div class="panel-body">
                            	R <?php echo $result['current_bal']; ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3">
                    	<div class="panel panel-danger">
                        	<div class="panel-heading">
                            	<h4 class="panel-title">Total Income</h4>
                            </div>
                            <div class="panel-body">
                            	R <?php echo $result['total_bal']; ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3">
                    	<div class="panel panel-warning">
                        	<div class="panel-heading">
                            	<h4 class="panel-title">Available Pin</h4>
                            </div>
                            <div class="panel-body">
                            	<?php
								echo mysqli_num_rows(mysqli_query($con,"select * from pin_list where userid='$userid' and status='open'"));
								?>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-6">
                        <div class="panel panel-primary">
                            <div class="panel-heading">
                                <h4 class="panel-title">5×5 Matrix Status</h4>
                            </div>
                            <div class="panel-body">
                                <p><strong>Matrix Level:</strong> <?php echo $matrix_level; ?></p>
                                <p><strong>Total Downline:</strong> <?php echo $matrix_count; ?></p>
                                <p><strong>Matrix Earnings:</strong> R <?php echo $result['matrix_earnings']; ?></p>

                                <?php
                                // Get next level requirements
                                $next_level = $matrix_level + 1;
                                $next_level_members = 0;

                                if($next_level == 1) {
                                    $next_level_members = 5;
                                } else if($next_level == 2) {
                                    $next_level_members = 30;
                                } else if($next_level == 3) {
                                    $next_level_members = 155;
                                } else if($next_level == 4) {
                                    $next_level_members = 780;
                                } else if($next_level == 5) {
                                    $next_level_members = 3905;
                                }

                                if($next_level <= 5) {
                                    $members_needed = $next_level_members - $matrix_count;
                                    echo "<p><strong>Next Level:</strong> Need $members_needed more members to reach Level $next_level</p>";

                                    // Get commission for next level
                                    $comm_query = mysqli_query($con, "SELECT commission_amount FROM matrix_commission WHERE level='$next_level'");
                                    $comm_result = mysqli_fetch_array($comm_query);
                                    $next_commission = $comm_result['commission_amount'];

                                    echo "<p><strong>Next Level Commission:</strong> R " . number_format($next_commission) . "</p>";
                                } else {
                                    echo "<p><strong>Status:</strong> You have reached the maximum matrix level!</p>";
                                }
                                ?>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="panel panel-info">
                            <div class="panel-heading">
                                <h4 class="panel-title">Loan & Maintenance Status</h4>
                            </div>
                            <div class="panel-body">
                                <?php
                                // Get loan information
$loan_query = mysqli_query($con, "SELECT * FROM loan WHERE userid='$userid'");
                                $loan_result = mysqli_fetch_array($loan_query);
                                $loan_status = (is_array($loan_result) && isset($loan_result['status'])) ? $loan_result['status'] : 'N/A';
                                $loan_remaining = (is_array($loan_result) && isset($loan_result['remaining_amount'])) ? $loan_result['remaining_amount'] : '0';

                                echo "<p><strong>Loan Status:</strong> " . ucfirst($loan_status) . "</p>";
                                echo "<p><strong>Remaining Amount:</strong> R " . $loan_remaining . "</p>";
                                echo "<p><strong>Loan Repaid:</strong> R " . ($result ? $result['loan_repaid'] : '0') . "</p>";
                                echo "<p><strong>Maintenance Paid:</strong> R " . ($result ? $result['maintenance_paid'] : '0') . "</p>";

                                // Get latest maintenance fee payment
                                $maint_query = mysqli_query($con, "SELECT * FROM maintenance_fee WHERE userid='$userid' ORDER BY month DESC LIMIT 1");
                                if(mysqli_num_rows($maint_query) > 0) {
                                    $maint_result = mysqli_fetch_array($maint_query);
                                    $last_month = $maint_result['month'];
                                    $last_payment = $maint_result['date_paid'];

                                    echo "<p><strong>Last Maintenance Payment:</strong> Month $last_month on $last_payment</p>";
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- /.container-fluid -->
        </div>
        <!-- /#page-wrapper -->

    </div>
    <!-- /#wrapper -->

    <!-- jQuery -->
    <script src="vendor/jquery/jquery.min.js"></script>

    <!-- Bootstrap Core JavaScript -->
    <script src="vendor/bootstrap/js/bootstrap.min.js"></script>

    <!-- Metis Menu Plugin JavaScript -->
    <script src="vendor/metisMenu/metisMenu.min.js"></script>

    <!-- Custom Theme JavaScript -->
    <script src="dist/js/sb-admin-2.js"></script>

</body>

</html>
