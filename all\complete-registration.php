<?php
include('php-includes/connect.php');
include('php-includes/wise-api.php');

// Initialize Wise API
$wise = new WiseAPI();

// Check if token is provided
if(!isset($_GET['token']) || empty($_GET['token'])) {
    header('Location: index.php');
    exit;
}

$token = mysqli_real_escape_string($con, $_GET['token']);

// Verify token and get user details
$query = mysqli_query($con, "SELECT * FROM join_requests WHERE completion_token='$token' AND status='verified'");

if(mysqli_num_rows($query) == 0) {
    // Invalid or expired token
    $error_message = "Invalid or expired registration link. Please contact your sponsor.";
} else {
    $request = mysqli_fetch_assoc($query);
    $email = $request['email'];
    
    // Check if token is expired (24 hours)
    $token_created = strtotime($request['otp_expiry']); // Using OTP expiry as token creation time
    $current_time = time();
    $token_expiry = $token_created + (24 * 60 * 60); // 24 hours
    
    if($current_time > $token_expiry) {
        $error_message = "This registration link has expired. Please contact your sponsor to initiate the registration again.";
    }
    
    // Check if user has already completed registration
    $user_query = mysqli_query($con, "SELECT * FROM user WHERE email='$email'");
    $user = mysqli_fetch_assoc($user_query);
    
    if(isset($user['address']) && !empty($user['address']) && isset($user['account']) && !empty($user['account'])) {
        $already_completed = true;
    }
}

// Handle form submission
if(isset($_POST['complete_registration'])) {
    $email = mysqli_real_escape_string($con, $_POST['email']);
    $password = mysqli_real_escape_string($con, $_POST['password']);
    $confirm_password = mysqli_real_escape_string($con, $_POST['confirm_password']);
    $first_name = mysqli_real_escape_string($con, $_POST['first_name']);
    $last_name = mysqli_real_escape_string($con, $_POST['last_name']);
    $address = mysqli_real_escape_string($con, $_POST['address']);
    $city = mysqli_real_escape_string($con, $_POST['city']);
    $postal_code = mysqli_real_escape_string($con, $_POST['postal_code']);
    $country = mysqli_real_escape_string($con, $_POST['country']);
    
    // Validate password
    if($password != $confirm_password) {
        $error_message = "Passwords do not match.";
    } elseif(strlen($password) < 6) {
        $error_message = "Password must be at least 6 characters long.";
    } else {
        // Update user details
        $update = mysqli_query($con, "UPDATE user SET 
            password='$password', 
            address='$address, $city, $postal_code, $country' 
            WHERE email='$email'");
        
        if($update) {
            // Create Wise recipient account (for demonstration - in production, use actual Wise API)
            $accountDetails = [
                'currency' => 'USD', // Default currency
                'type' => 'bank_account',
                'accountHolderName' => $first_name . ' ' . $last_name,
                'details' => [
                    'address' => [
                        'country' => $country,
                        'city' => $city,
                        'postCode' => $postal_code,
                        'firstLine' => $address
                    ]
                ]
            ];
            
            // For demonstration, we'll simulate a successful Wise recipient creation
            $recipient_id = 'demo_' . uniqid();
            
            // Store recipient details
            $check_recipient = mysqli_query($con, "SELECT * FROM wise_recipients WHERE userid='$email'");
            
            if(mysqli_num_rows($check_recipient) > 0) {
                // Update existing recipient
                mysqli_query($con, "UPDATE wise_recipients SET 
                    recipient_id='$recipient_id',
                    currency='USD',
                    account_details='" . json_encode($accountDetails) . "'
                    WHERE userid='$email'");
            } else {
                // Insert new recipient
                mysqli_query($con, "INSERT INTO wise_recipients 
                    (userid, recipient_id, currency, account_details) 
                    VALUES ('$email', '$recipient_id', 'USD', '" . json_encode($accountDetails) . "')");
            }
            
            // Update join request status
            mysqli_query($con, "UPDATE join_requests SET status='completed' WHERE email='$email'");
            
            // Set success message
            $success_message = "Registration completed successfully! You can now log in to your account.";
            
            // Redirect to welcome page
            header("Location: welcome.php?email=" . urlencode($email));
            exit;
        } else {
            $error_message = "Failed to update user details. Please try again.";
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>MLM Website - Complete Registration</title>

    <!-- Bootstrap Core CSS -->
    <link href="vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">

    <!-- MetisMenu CSS -->
    <link href="vendor/metisMenu/metisMenu.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="dist/css/sb-admin-2.css" rel="stylesheet">

    <!-- Custom Fonts -->
    <link href="vendor/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css">
</head>

<body>
    <div class="container">
        <div class="row">
            <div class="col-md-8 col-md-offset-2">
                <div class="login-panel panel panel-primary" style="margin-top: 50px;">
                    <div class="panel-heading">
                        <h3 class="panel-title"><i class="fa fa-user-plus"></i> Complete Your Registration</h3>
                    </div>
                    <div class="panel-body">
                        <?php if(isset($error_message)): ?>
                        <div class="alert alert-danger">
                            <i class="fa fa-exclamation-circle"></i> <?php echo $error_message; ?>
                        </div>
                        <?php elseif(isset($already_completed)): ?>
                        <div class="alert alert-info">
                            <i class="fa fa-info-circle"></i> You have already completed your registration. <a href="login.php">Click here to login</a>.
                        </div>
                        <?php elseif(isset($success_message)): ?>
                        <div class="alert alert-success">
                            <i class="fa fa-check-circle"></i> <?php echo $success_message; ?>
                            <p>You will be redirected to the login page in a few seconds...</p>
                        </div>
                        <script>
                            setTimeout(function() {
                                window.location.href = "login.php";
                            }, 5000);
                        </script>
                        <?php else: ?>
                        <div class="alert alert-info">
                            <i class="fa fa-info-circle"></i> Welcome to our MLM platform! Please complete your registration by providing the following information.
                        </div>
                        
                        <form method="post" action="">
                            <input type="hidden" name="email" value="<?php echo $email; ?>">
                            
                            <h4><i class="fa fa-lock"></i> Account Security</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Email</label>
                                        <input type="email" class="form-control" value="<?php echo $email; ?>" readonly>
                                        <p class="help-block">This is your login username</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Mobile</label>
                                        <input type="text" class="form-control" value="<?php echo $request['mobile']; ?>" readonly>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Password</label>
                                        <input type="password" name="password" class="form-control" required>
                                        <p class="help-block">At least 6 characters</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Confirm Password</label>
                                        <input type="password" name="confirm_password" class="form-control" required>
                                    </div>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <h4><i class="fa fa-user"></i> Personal Information</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>First Name</label>
                                        <input type="text" name="first_name" class="form-control" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Last Name</label>
                                        <input type="text" name="last_name" class="form-control" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label>Address</label>
                                <input type="text" name="address" class="form-control" required>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>City</label>
                                        <input type="text" name="city" class="form-control" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Postal Code</label>
                                        <input type="text" name="postal_code" class="form-control" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label>Country</label>
                                <select name="country" class="form-control" required>
                                    <option value="">Select Country</option>
                                    <option value="US">United States</option>
                                    <option value="GB">United Kingdom</option>
                                    <option value="CA">Canada</option>
                                    <option value="AU">Australia</option>
                                    <option value="ZA">South Africa</option>
                                    <option value="DE">Germany</option>
                                    <option value="FR">France</option>
                                </select>
                            </div>
                            
                            <div class="alert alert-warning">
                                <i class="fa fa-info-circle"></i> <strong>Note:</strong> Your banking details will be collected securely through the Wise payment platform after registration.
                            </div>
                            
                            <div class="form-group">
                                <button type="submit" name="complete_registration" class="btn btn-lg btn-success btn-block">
                                    <i class="fa fa-check-circle"></i> Complete Registration
                                </button>
                            </div>
                        </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="vendor/jquery/jquery.min.js"></script>

    <!-- Bootstrap Core JavaScript -->
    <script src="vendor/bootstrap/js/bootstrap.min.js"></script>

    <!-- Metis Menu Plugin JavaScript -->
    <script src="vendor/metisMenu/metisMenu.min.js"></script>

    <!-- Custom Theme JavaScript -->
    <script src="dist/js/sb-admin-2.js"></script>
</body>
</html>
