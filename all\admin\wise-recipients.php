<?php
include('php-includes/check-login.php');
require('php-includes/connect.php');
include('../php-includes/wise-api.php');

// Initialize Wise API
$wise = new WiseAPI();

// Handle form submission for adding/updating recipient
if(isset($_POST['save_recipient'])) {
    $userid = mysqli_real_escape_string($con, $_POST['userid']);
    $currency = mysqli_real_escape_string($con, $_POST['currency']);
    $account_number = mysqli_real_escape_string($con, $_POST['account_number']);
    $account_type = mysqli_real_escape_string($con, $_POST['account_type']);
    $bank_code = mysqli_real_escape_string($con, $_POST['bank_code']);
    $first_name = mysqli_real_escape_string($con, $_POST['first_name']);
    $last_name = mysqli_real_escape_string($con, $_POST['last_name']);
    $address_line1 = mysqli_real_escape_string($con, $_POST['address_line1']);
    $city = mysqli_real_escape_string($con, $_POST['city']);
    $postal_code = mysqli_real_escape_string($con, $_POST['postal_code']);
    $country = mysqli_real_escape_string($con, $_POST['country']);
    
    // Check if user exists
    $user_query = mysqli_query($con, "SELECT * FROM user WHERE email='$userid'");
    if(mysqli_num_rows($user_query) == 0) {
        $error_message = "User with email $userid does not exist.";
    } else {
        // Prepare recipient account details
        $accountDetails = [
            'currency' => $currency,
            'type' => 'bank_account',
            'profile' => $wise->profileId,
            'accountHolderName' => $first_name . ' ' . $last_name,
            'details' => [
                'accountNumber' => $account_number,
                'accountType' => $account_type,
                'bankCode' => $bank_code,
                'address' => [
                    'country' => $country,
                    'city' => $city,
                    'postCode' => $postal_code,
                    'firstLine' => $address_line1
                ]
            ]
        ];
        
        // For demonstration purposes, we'll just store the account details
        // In a real implementation, you would call the Wise API to create the recipient
        // $response = $wise->createRecipient($accountDetails);
        
        // For now, we'll simulate a successful response
        $recipient_id = 'demo_' . uniqid();
        
        // Check if recipient already exists for this user
        $check_query = mysqli_query($con, "SELECT * FROM wise_recipients WHERE userid='$userid'");
        
        if(mysqli_num_rows($check_query) > 0) {
            // Update existing recipient
            $update = mysqli_query($con, "UPDATE wise_recipients SET 
                recipient_id = '$recipient_id',
                currency = '$currency',
                account_details = '" . json_encode($accountDetails) . "'
                WHERE userid = '$userid'");
                
            if($update) {
                $success_message = "Recipient account updated successfully.";
            } else {
                $error_message = "Error updating recipient: " . mysqli_error($con);
            }
        } else {
            // Insert new recipient
            $insert = mysqli_query($con, "INSERT INTO wise_recipients 
                (userid, recipient_id, currency, account_details) 
                VALUES ('$userid', '$recipient_id', '$currency', '" . json_encode($accountDetails) . "')");
                
            if($insert) {
                $success_message = "Recipient account added successfully.";
            } else {
                $error_message = "Error adding recipient: " . mysqli_error($con);
            }
        }
    }
}

// Handle recipient deletion
if(isset($_GET['delete']) && isset($_GET['userid'])) {
    $userid = mysqli_real_escape_string($con, $_GET['userid']);
    
    $delete = mysqli_query($con, "DELETE FROM wise_recipients WHERE userid='$userid'");
    
    if($delete) {
        $success_message = "Recipient deleted successfully.";
    } else {
        $error_message = "Error deleting recipient: " . mysqli_error($con);
    }
}

// Get all recipients
$recipients_query = mysqli_query($con, "SELECT r.*, u.mobile, u.address 
                                       FROM wise_recipients r 
                                       JOIN user u ON r.userid = u.email 
                                       ORDER BY r.created_date DESC");
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>MLM Admin - Wise Recipients</title>

    <!-- Bootstrap Core CSS -->
    <link href="vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">

    <!-- MetisMenu CSS -->
    <link href="vendor/metisMenu/metisMenu.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="dist/css/sb-admin-2.css" rel="stylesheet">

    <!-- Custom Fonts -->
    <link href="vendor/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css">
</head>

<body>

    <div id="wrapper">

        <!-- Navigation -->
        <?php include('php-includes/menu.php'); ?>

        <!-- Page Content -->
        <div id="page-wrapper">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-lg-12">
                        <h1 class="page-header">Wise Recipients</h1>
                    </div>
                    <!-- /.col-lg-12 -->
                </div>
                <!-- /.row -->
                
                <?php if(isset($success_message)): ?>
                <div class="alert alert-success">
                    <?php echo $success_message; ?>
                </div>
                <?php endif; ?>
                
                <?php if(isset($error_message)): ?>
                <div class="alert alert-danger">
                    <?php echo $error_message; ?>
                </div>
                <?php endif; ?>
                
                <div class="row">
                    <div class="col-lg-12">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                Add/Edit Recipient
                            </div>
                            <div class="panel-body">
                                <form method="post" action="">
                                    <div class="row">
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label>User Email</label>
                                                <select class="form-control" name="userid" required>
                                                    <option value="">Select User</option>
                                                    <?php
                                                    $users_query = mysqli_query($con, "SELECT email FROM user ORDER BY email");
                                                    while($user = mysqli_fetch_assoc($users_query)) {
                                                        echo '<option value="' . $user['email'] . '">' . $user['email'] . '</option>';
                                                    }
                                                    ?>
                                                </select>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label>Currency</label>
                                                <select class="form-control" name="currency" required>
                                                    <option value="USD">USD</option>
                                                    <option value="EUR">EUR</option>
                                                    <option value="GBP">GBP</option>
                                                    <option value="CAD">CAD</option>
                                                    <option value="AUD">AUD</option>
                                                    <option value="ZAR">ZAR</option>
                                                </select>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label>Account Number</label>
                                                <input type="text" class="form-control" name="account_number" required>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label>Account Type</label>
                                                <select class="form-control" name="account_type">
                                                    <option value="checking">Checking</option>
                                                    <option value="savings">Savings</option>
                                                </select>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label>Bank Code / Routing Number</label>
                                                <input type="text" class="form-control" name="bank_code" required>
                                            </div>
                                        </div>
                                        
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label>First Name</label>
                                                <input type="text" class="form-control" name="first_name" required>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label>Last Name</label>
                                                <input type="text" class="form-control" name="last_name" required>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label>Address Line 1</label>
                                                <input type="text" class="form-control" name="address_line1" required>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label>City</label>
                                                <input type="text" class="form-control" name="city" required>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label>Postal Code</label>
                                                <input type="text" class="form-control" name="postal_code" required>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label>Country</label>
                                                <select class="form-control" name="country" required>
                                                    <option value="US">United States</option>
                                                    <option value="GB">United Kingdom</option>
                                                    <option value="CA">Canada</option>
                                                    <option value="AU">Australia</option>
                                                    <option value="ZA">South Africa</option>
                                                    <option value="DE">Germany</option>
                                                    <option value="FR">France</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <button type="submit" name="save_recipient" class="btn btn-primary">Save Recipient</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-lg-12">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                Recipient Accounts
                            </div>
                            <div class="panel-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th>User ID</th>
                                                <th>Recipient ID</th>
                                                <th>Currency</th>
                                                <th>Created Date</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                            if(mysqli_num_rows($recipients_query) > 0) {
                                                while($recipient = mysqli_fetch_assoc($recipients_query)) {
                                                    echo '<tr>';
                                                    echo '<td>' . $recipient['userid'] . '</td>';
                                                    echo '<td>' . $recipient['recipient_id'] . '</td>';
                                                    echo '<td>' . $recipient['currency'] . '</td>';
                                                    echo '<td>' . $recipient['created_date'] . '</td>';
                                                    echo '<td>
                                                        <a href="wise-recipients.php?delete=1&userid=' . $recipient['userid'] . '" 
                                                           class="btn btn-danger btn-xs" 
                                                           onclick="return confirm(\'Are you sure you want to delete this recipient?\');">
                                                            <i class="fa fa-trash"></i> Delete
                                                        </a>
                                                    </td>';
                                                    echo '</tr>';
                                                }
                                            } else {
                                                echo '<tr><td colspan="5" class="text-center">No recipients found</td></tr>';
                                            }
                                            ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- /.row -->
            </div>
            <!-- /.container-fluid -->
        </div>
        <!-- /#page-wrapper -->

    </div>
    <!-- /#wrapper -->

    <!-- jQuery -->
    <script src="vendor/jquery/jquery.min.js"></script>

    <!-- Bootstrap Core JavaScript -->
    <script src="vendor/bootstrap/js/bootstrap.min.js"></script>

    <!-- Metis Menu Plugin JavaScript -->
    <script src="vendor/metisMenu/metisMenu.min.js"></script>

    <!-- Custom Theme JavaScript -->
    <script src="dist/js/sb-admin-2.js"></script>

</body>

</html>
