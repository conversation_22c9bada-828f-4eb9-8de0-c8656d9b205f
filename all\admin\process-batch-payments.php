<?php
include('php-includes/check-login.php');
require('php-includes/connect.php');

if(isset($_POST['process_batch']) && isset($_POST['selected_users'])) {
    $processed = 0;
    $failed = 0;
    $date = date("Y-m-d");
    
    foreach($_POST['selected_users'] as $userid) {
        // Get current balance
        $query = mysqli_query($con,"select current_bal from income where userid='$userid'");
        $result = mysqli_fetch_array($query);
        $amount = $result['current_bal'];
        
        // Start transaction
        mysqli_begin_transaction($con);
        
        try {
            // Record payment
            mysqli_query($con,"insert into income_received(`userid`, `amount`, `date`) 
                             values('$userid', '$amount', '$date')");
            
            // Reset current balance
            mysqli_query($con,"update income set current_bal=0 
                             where userid='$userid'");
            
            mysqli_commit($con);
            $processed++;
        } catch (Exception $e) {
            mysqli_rollback($con);
            $failed++;
        }
    }
    
    echo '<script>alert("Processed ' . $processed . ' payments. Failed: ' . $failed . '");
          window.location.assign("income.php");</script>';
}
?>