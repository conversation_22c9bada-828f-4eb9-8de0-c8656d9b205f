<?php
/**
 * Rate Limiter Utility Class
 * Provides rate limiting functionality to prevent abuse
 */
class RateLimiter {
    
    private static $con;
    
    // Rate limiting configurations
    const CONFIGS = [
        'otp_request' => [
            'max_attempts' => 5,
            'window_minutes' => 15,
            'block_minutes' => 60
        ],
        'otp_verify' => [
            'max_attempts' => 10,
            'window_minutes' => 15,
            'block_minutes' => 30
        ],
        'login_attempt' => [
            'max_attempts' => 5,
            'window_minutes' => 15,
            'block_minutes' => 30
        ],
        'registration' => [
            'max_attempts' => 3,
            'window_minutes' => 60,
            'block_minutes' => 120
        ]
    ];
    
    /**
     * Initialize rate limiter with database connection
     * 
     * @param mysqli $connection Database connection
     */
    public static function init($connection) {
        self::$con = $connection;
        
        // Clean up old records periodically
        if (rand(1, 100) <= 10) { // 10% chance
            self::cleanupOldRecords();
        }
    }
    
    /**
     * Check if an action is rate limited
     * 
     * @param string $identifier Unique identifier (IP, email, etc.)
     * @param string $action Action being performed
     * @return array Result with 'allowed' boolean and additional info
     */
    public static function checkLimit($identifier, $action) {
        if (!self::$con) {
            throw new Exception("Rate Limiter not initialized");
        }
        
        if (!isset(self::CONFIGS[$action])) {
            throw new Exception("Unknown action: $action");
        }
        
        $config = self::CONFIGS[$action];
        $current_time = date('Y-m-d H:i:s');
        $window_start = date('Y-m-d H:i:s', time() - ($config['window_minutes'] * 60));
        
        // Check if currently blocked
        $stmt = self::$con->prepare("SELECT blocked_until FROM rate_limits WHERE identifier = ? AND action = ? AND blocked_until > ?");
        $stmt->bind_param("sss", $identifier, $action, $current_time);
        $stmt->execute();
        $result = $stmt->get_result();
        $stmt->close();
        
        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            return [
                'allowed' => false,
                'reason' => 'blocked',
                'blocked_until' => $row['blocked_until'],
                'retry_after' => strtotime($row['blocked_until']) - time()
            ];
        }
        
        // Get current attempts in the window
        $stmt = self::$con->prepare("SELECT attempts FROM rate_limits WHERE identifier = ? AND action = ? AND window_start > ?");
        $stmt->bind_param("sss", $identifier, $action, $window_start);
        $stmt->execute();
        $result = $stmt->get_result();
        $stmt->close();
        
        $current_attempts = 0;
        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $current_attempts = $row['attempts'];
        }
        
        if ($current_attempts >= $config['max_attempts']) {
            // Block the identifier
            $blocked_until = date('Y-m-d H:i:s', time() + ($config['block_minutes'] * 60));
            
            $stmt = self::$con->prepare("INSERT INTO rate_limits (identifier, action, attempts, window_start, blocked_until) 
                                        VALUES (?, ?, ?, ?, ?) 
                                        ON DUPLICATE KEY UPDATE 
                                        attempts = ?, blocked_until = ?");
            $stmt->bind_param("ssissis", $identifier, $action, $current_attempts, $current_time, $blocked_until, $current_attempts, $blocked_until);
            $stmt->execute();
            $stmt->close();
            
            return [
                'allowed' => false,
                'reason' => 'rate_limited',
                'attempts' => $current_attempts,
                'max_attempts' => $config['max_attempts'],
                'blocked_until' => $blocked_until,
                'retry_after' => $config['block_minutes'] * 60
            ];
        }
        
        return [
            'allowed' => true,
            'attempts' => $current_attempts,
            'max_attempts' => $config['max_attempts'],
            'remaining' => $config['max_attempts'] - $current_attempts
        ];
    }
    
    /**
     * Record an attempt for rate limiting
     * 
     * @param string $identifier Unique identifier
     * @param string $action Action being performed
     * @return bool True if recorded successfully
     */
    public static function recordAttempt($identifier, $action) {
        if (!self::$con) {
            throw new Exception("Rate Limiter not initialized");
        }
        
        if (!isset(self::CONFIGS[$action])) {
            throw new Exception("Unknown action: $action");
        }
        
        $current_time = date('Y-m-d H:i:s');
        $window_start = date('Y-m-d H:i:s', time() - (self::CONFIGS[$action]['window_minutes'] * 60));
        
        // Insert or update the rate limit record
        $stmt = self::$con->prepare("INSERT INTO rate_limits (identifier, action, attempts, window_start) 
                                    VALUES (?, ?, 1, ?) 
                                    ON DUPLICATE KEY UPDATE 
                                    attempts = CASE 
                                        WHEN window_start <= ? THEN attempts + 1 
                                        ELSE 1 
                                    END,
                                    window_start = CASE 
                                        WHEN window_start <= ? THEN window_start 
                                        ELSE ? 
                                    END");
        $stmt->bind_param("ssssss", $identifier, $action, $current_time, $window_start, $window_start, $current_time);
        $stmt->execute();
        $stmt->close();
        
        return true;
    }
    
    /**
     * Reset rate limit for an identifier and action
     * 
     * @param string $identifier Unique identifier
     * @param string $action Action to reset
     * @return bool True if reset successfully
     */
    public static function resetLimit($identifier, $action) {
        if (!self::$con) {
            throw new Exception("Rate Limiter not initialized");
        }
        
        $stmt = self::$con->prepare("DELETE FROM rate_limits WHERE identifier = ? AND action = ?");
        $stmt->bind_param("ss", $identifier, $action);
        $stmt->execute();
        $stmt->close();
        
        return true;
    }
    
    /**
     * Get rate limit status for an identifier and action
     * 
     * @param string $identifier Unique identifier
     * @param string $action Action to check
     * @return array Status information
     */
    public static function getStatus($identifier, $action) {
        if (!self::$con) {
            throw new Exception("Rate Limiter not initialized");
        }
        
        if (!isset(self::CONFIGS[$action])) {
            throw new Exception("Unknown action: $action");
        }
        
        $config = self::CONFIGS[$action];
        $current_time = date('Y-m-d H:i:s');
        $window_start = date('Y-m-d H:i:s', time() - ($config['window_minutes'] * 60));
        
        $stmt = self::$con->prepare("SELECT attempts, window_start, blocked_until FROM rate_limits WHERE identifier = ? AND action = ?");
        $stmt->bind_param("ss", $identifier, $action);
        $stmt->execute();
        $result = $stmt->get_result();
        $stmt->close();
        
        if ($result->num_rows === 0) {
            return [
                'attempts' => 0,
                'max_attempts' => $config['max_attempts'],
                'remaining' => $config['max_attempts'],
                'blocked' => false
            ];
        }
        
        $row = $result->fetch_assoc();
        $attempts = ($row['window_start'] > $window_start) ? $row['attempts'] : 0;
        $blocked = ($row['blocked_until'] && $row['blocked_until'] > $current_time);
        
        return [
            'attempts' => $attempts,
            'max_attempts' => $config['max_attempts'],
            'remaining' => max(0, $config['max_attempts'] - $attempts),
            'blocked' => $blocked,
            'blocked_until' => $row['blocked_until']
        ];
    }
    
    /**
     * Clean up old rate limit records
     */
    private static function cleanupOldRecords() {
        if (!self::$con) {
            return;
        }
        
        // Remove records older than 24 hours
        $cleanup_time = date('Y-m-d H:i:s', time() - (24 * 60 * 60));
        
        $stmt = self::$con->prepare("DELETE FROM rate_limits WHERE window_start < ? AND (blocked_until IS NULL OR blocked_until < ?)");
        $stmt->bind_param("ss", $cleanup_time, $cleanup_time);
        $stmt->execute();
        $stmt->close();
    }
    
    /**
     * Get client IP address (considering proxies)
     * 
     * @return string Client IP address
     */
    public static function getClientIP() {
        $ip_keys = ['HTTP_CF_CONNECTING_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR'];
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = explode(',', $ip)[0];
                }
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
}
?>
