# MLM System Testing Guide

This comprehensive guide explains how to safely test your MLM commission logic and payout processes without any real-world risk.

## 🎯 Testing Overview

The testing infrastructure provides:
- **Isolated Test Environment**: Separate test database to protect production data
- **Automated Data Generation**: Scripts to create realistic MLM matrix structures
- **Mock Payment Services**: Simulate Stitch payouts without real transactions
- **Comprehensive Verification**: Automated testing of commission calculations
- **Detailed Reporting**: Visual reports to verify system logic

## 🚀 Quick Start

### 1. Set Up Test Environment

```bash
# Create test database and apply schema
php setup-test-database.php

# Configure environment for testing
cp .env.example .env
# Edit .env and set TESTING_MODE=true
```

### 2. Generate Test Data

```bash
# Create 100 test users in a 5-level matrix
php test-seeder.php --users=100 --levels=5

# Or create a smaller test set
php test-seeder.php --users=50 --levels=3
```

### 3. Run MLM Simulation

```bash
# Run complete simulation with 10 new registrations
php run-simulation.php --registrations=10

# Run without payout simulation
php run-simulation.php --registrations=5 --no-payouts

# Use specific test run
php run-simulation.php --test-run=1 --registrations=20
```

### 4. Verify MLM Logic

```bash
# Comprehensive verification of commission logic
php verify-mlm-logic.php
```

### 5. Clean Up (Optional)

```bash
# Clean test data when done
php test-seeder.php --clean
```

## 📊 Understanding the Test Results

### Test Database Structure

The testing system creates additional tables:

- **`test_runs`**: Tracks each test execution
- **`mock_payouts`**: Logs simulated payment transactions
- **`test_matrix_snapshots`**: Captures matrix state for analysis

### Generated Reports

1. **Simulation Report** (`reports/simulation_report_*.html`)
   - User statistics and matrix analysis
   - Commission calculations summary
   - Payout simulation results
   - Visual charts and graphs

2. **Verification Report** (`reports/mlm_verification_*.json`)
   - Detailed test results in JSON format
   - Matrix structure validation
   - Commission calculation verification
   - Data consistency checks

## 🔍 What Gets Tested

### 1. Matrix Structure Integrity
- ✅ No orphaned users without tree entries
- ✅ No circular sponsor references
- ✅ Matrix position consistency
- ✅ Proper parent-child relationships

### 2. Commission Calculations
- ✅ Correct commission amounts per level
- ✅ All eligible upline members receive commissions
- ✅ Commission levels follow 5×5 matrix rules
- ✅ No duplicate or missing commissions

### 3. Upline Chain Logic
- ✅ Maximum 5 levels of upline commissions
- ✅ Proper upline chain traversal
- ✅ No broken chain references
- ✅ Correct sponsor relationships

### 4. Matrix Position Management
- ✅ No duplicate positions under same sponsor
- ✅ Valid position numbers (1-5)
- ✅ Position availability checking
- ✅ Matrix filling logic

### 5. Payout Processing
- ✅ Batch payout creation
- ✅ Success/failure handling
- ✅ Commission status updates
- ✅ Payment reconciliation

## 🛠️ Advanced Testing Scenarios

### Scenario 1: Deep Matrix Testing
```bash
# Create large matrix for stress testing
php test-seeder.php --users=500 --levels=5
php run-simulation.php --registrations=50
```

### Scenario 2: Commission Edge Cases
```bash
# Test with specific matrix configurations
php test-seeder.php --users=25 --levels=5  # Exactly 5 levels
php run-simulation.php --registrations=1   # Single new user
```

### Scenario 3: Payout Failure Simulation
```bash
# Test with high failure rate
# Edit mock-stitch-payouts.php to set lower success rate
php run-simulation.php --registrations=20
```

## 📈 Interpreting Results

### Commission Verification Checklist

1. **Level 1 Commissions**: R1,000 per new member
2. **Level 2 Commissions**: R5,000 when level 1 fills
3. **Level 3 Commissions**: R25,000 when level 2 fills
4. **Level 4 Commissions**: R125,000 when level 3 fills
5. **Level 5 Commissions**: R625,000 when level 4 fills

### Expected Matrix Behavior

- Each user can have maximum 5 direct downline members
- Commissions flow up to 5 levels maximum
- Matrix positions fill sequentially (1, 2, 3, 4, 5)
- New members trigger commissions for all eligible upline

### Payout Verification

- Mock payouts simulate 95% success rate by default
- Failed payouts should retry with exponential backoff
- Commission statuses update based on payout results
- Batch processing handles multiple payouts efficiently

## 🚨 Troubleshooting

### Common Issues

1. **"Insufficient test data" Error**
   ```bash
   # Solution: Run the seeder first
   php test-seeder.php --users=100
   ```

2. **Database Connection Errors**
   ```bash
   # Check if test database exists
   php setup-test-database.php
   ```

3. **No Available Sponsors**
   ```bash
   # Matrix might be full, clean and reseed
   php test-seeder.php --clean
   php test-seeder.php --users=50
   ```

4. **Commission Calculation Errors**
   ```bash
   # Verify commission configuration in .env
   # Run verification script for detailed analysis
   php verify-mlm-logic.php
   ```

### Debug Mode

Enable detailed logging by setting in `.env`:
```
APP_DEBUG=true
LOG_LEVEL=debug
```

## 🔒 Safety Features

### Production Protection
- Testing mode uses separate database (`mlm_test`)
- Mock services prevent real transactions
- All test data clearly marked with `@test.com` emails
- Production database remains untouched

### Data Isolation
- Test users have distinct email patterns
- Mock payouts logged separately
- Test runs tracked independently
- Easy cleanup of test data

## 📋 Testing Checklist

Before deploying to production:

- [ ] All verification tests pass
- [ ] Commission calculations are accurate
- [ ] Matrix structure is valid
- [ ] Payout simulation works correctly
- [ ] No data consistency issues
- [ ] Performance is acceptable
- [ ] Error handling works properly
- [ ] Reports generate successfully

## 🎓 Best Practices

1. **Regular Testing**: Run tests after any code changes
2. **Scenario Coverage**: Test various matrix configurations
3. **Performance Testing**: Use large datasets to test scalability
4. **Edge Case Testing**: Test boundary conditions and error scenarios
5. **Documentation**: Keep test results for compliance and auditing

## 📞 Support

If you encounter issues:

1. Check the generated reports for detailed error information
2. Review the error logs in `logs/` directory
3. Verify your configuration in `.env` file
4. Ensure all required tables exist in test database
5. Run `php test-security-features.php` to verify system health

---

**Remember**: This testing environment is completely safe and isolated from your production system. You can run tests as many times as needed without any risk to real data or transactions.
