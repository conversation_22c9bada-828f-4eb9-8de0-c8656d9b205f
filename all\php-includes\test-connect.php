<?php
/**
 * Test-Aware Database Connection
 * Automatically switches between production and test databases
 */

require_once __DIR__ . '/config.php';

// Get appropriate database configuration
$db_config = Config::getDatabaseConfig();

// Database configuration
$db_host = $db_config['host'];
$db_user = $db_config['user'];
$db_pass = $db_config['pass'];
$db_name = $db_config['name'];
$db_port = $db_config['port'];

// Attempt connection
$con = mysqli_connect($db_host, $db_user, $db_pass, $db_name, $db_port);

// Enhanced error handling
if (mysqli_connect_error()) {
    $error_msg = mysqli_connect_error();
    $db_type = Config::isTesting() ? 'TEST' : 'PRODUCTION';
    
    $error_details = "
    <div style='background: #ffebee; border: 1px solid #f44336; padding: 20px; margin: 20px; border-radius: 5px;'>
        <h3 style='color: #d32f2f; margin-top: 0;'>$db_type Database Connection Failed</h3>
        <p><strong>Error:</strong> $error_msg</p>
        <p><strong>Host:</strong> $db_host:$db_port</p>
        <p><strong>Database:</strong> $db_name</p>
        <p><strong>Mode:</strong> " . (Config::isTesting() ? 'Testing' : 'Production') . "</p>
        <h4>Troubleshooting Steps:</h4>
        <ol>
            <li>Ensure XAMPP is running (check XAMPP Control Panel)</li>
            <li>Start MySQL service in XAMPP</li>
            <li>Verify database '$db_name' exists in phpMyAdmin</li>
            <li>Check if port $db_port is correct (try 3306 or 3308)</li>
            <li>Run database-test.php to diagnose the issue</li>";
    
    if (Config::isTesting()) {
        $error_details .= "
            <li><strong>For Testing:</strong> Run 'php setup-test-database.php' to create test database</li>";
    }
    
    $error_details .= "
        </ol>
    </div>";

    die($error_details);
}

// Set charset for proper handling of special characters
mysqli_set_charset($con, "utf8mb4");

// Enable error reporting for mysqli
mysqli_report(MYSQLI_REPORT_ERROR | MYSQLI_REPORT_STRICT);

// Log connection info if in debug mode
if (Config::isDebug()) {
    $mode = Config::isTesting() ? 'TEST' : 'PRODUCTION';
    error_log("Database connected: $mode mode - $db_name@$db_host:$db_port");
}
?>
