<?php
	// Database configuration
	$db_host = "localhost";
	$db_user = "root";
	$db_pass = "";
	$db_name = "mlm";
	$db_port = "3308"; // Using port 3308 since 3306 is occupied by another server

	// Attempt connection
	$con = mysqli_connect($db_host, $db_user, $db_pass, $db_name, $db_port);

	// Enhanced error handling
	if (mysqli_connect_error()) {
		$error_msg = mysqli_connect_error();
		$error_details = "
		<div style='background: #ffebee; border: 1px solid #f44336; padding: 20px; margin: 20px; border-radius: 5px;'>
			<h3 style='color: #d32f2f; margin-top: 0;'>Database Connection Failed</h3>
			<p><strong>Error:</strong> $error_msg</p>
			<p><strong>Host:</strong> $db_host:$db_port</p>
			<p><strong>Database:</strong> $db_name</p>
			<h4>Troubleshooting Steps:</h4>
			<ol>
				<li>Ensure XAMPP is running (check XAMPP Control Panel)</li>
				<li>Start MySQL service in XAMPP</li>
				<li>Verify database '$db_name' exists in phpMyAdmin</li>
				<li>Check if port $db_port is correct (try 3306 or 3308)</li>
				<li>Run database-test.php to diagnose the issue</li>
			</ol>
		</div>";

		die($error_details);
	}

	// Set charset for proper handling of special characters
	mysqli_set_charset($con, "utf8mb4");
?>