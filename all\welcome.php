<?php
include('php-includes/connect.php');

// Check if email is provided
if(!isset($_GET['email']) || empty($_GET['email'])) {
    header('Location: index.php');
    exit;
}

$email = mysqli_real_escape_string($con, $_GET['email']);

// Get user details
$query = mysqli_query($con, "SELECT * FROM user WHERE email='$email'");

if(mysqli_num_rows($query) == 0) {
    header('Location: index.php');
    exit;
}

$user = mysqli_fetch_assoc($query);

// Get sponsor details
$sponsor_query = mysqli_query($con, "SELECT * FROM user WHERE email='{$user['under_userid']}'");
$sponsor = mysqli_fetch_assoc($sponsor_query);

// Get matrix position
$position = $user['matrix_position'];

// Get loan details
$loan_query = mysqli_query($con, "SELECT * FROM loan WHERE userid='$email'");
$loan = mysqli_fetch_assoc($loan_query);

// Auto-login the user
session_start();
$_SESSION['userid'] = $email;
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>MLM Website - Welcome</title>

    <!-- Bootstrap Core CSS -->
    <link href="vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">

    <!-- MetisMenu CSS -->
    <link href="vendor/metisMenu/metisMenu.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="dist/css/sb-admin-2.css" rel="stylesheet">

    <!-- Custom Fonts -->
    <link href="vendor/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css">
    
    <style>
        .welcome-header {
            background-color: #5cb85c;
            color: white;
            padding: 30px 0;
            margin-bottom: 30px;
            border-radius: 5px;
        }
        
        .feature-box {
            text-align: center;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 5px;
            background-color: #f9f9f9;
            min-height: 200px;
        }
        
        .feature-box i {
            font-size: 48px;
            margin-bottom: 15px;
            color: #337ab7;
        }
        
        .countdown {
            font-size: 24px;
            font-weight: bold;
            color: #d9534f;
            margin-top: 10px;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="row welcome-header">
            <div class="col-lg-12 text-center">
                <h1><i class="fa fa-check-circle"></i> Welcome to Our MLM Platform!</h1>
                <p class="lead">Your registration has been completed successfully.</p>
                <p>You will be redirected to your dashboard in <span id="countdown" class="countdown">10</span> seconds...</p>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="panel panel-primary">
                    <div class="panel-heading">
                        <h3 class="panel-title"><i class="fa fa-user"></i> Your Account Information</h3>
                    </div>
                    <div class="panel-body">
                        <table class="table table-bordered">
                            <tr>
                                <th>Username/Email:</th>
                                <td><?php echo $email; ?></td>
                            </tr>
                            <tr>
                                <th>Mobile:</th>
                                <td><?php echo $user['mobile']; ?></td>
                            </tr>
                            <tr>
                                <th>Sponsor:</th>
                                <td><?php echo $user['under_userid']; ?></td>
                            </tr>
                            <tr>
                                <th>Matrix Position:</th>
                                <td>Position <?php echo $position; ?></td>
                            </tr>
                            <tr>
                                <th>Join Date:</th>
                                <td><?php echo date('F j, Y', strtotime($user['join_date'])); ?></td>
                            </tr>
                        </table>
                        
                        <div class="alert alert-info">
                            <i class="fa fa-info-circle"></i> Your account is now active. You can start building your network by recruiting new members.
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="panel panel-success">
                    <div class="panel-heading">
                        <h3 class="panel-title"><i class="fa fa-money"></i> Financial Information</h3>
                    </div>
                    <div class="panel-body">
                        <h4>Loan Details</h4>
                        <table class="table table-bordered">
                            <tr>
                                <th>Loan Amount:</th>
                                <td>R <?php echo number_format($loan['loan_amount'], 2); ?></td>
                            </tr>
                            <tr>
                                <th>Monthly Payment:</th>
                                <td>R <?php echo number_format($loan['monthly_payment'], 2); ?></td>
                            </tr>
                            <tr>
                                <th>Start Date:</th>
                                <td><?php echo date('F j, Y', strtotime($loan['start_date'])); ?></td>
                            </tr>
                            <tr>
                                <th>Status:</th>
                                <td><span class="label label-primary"><?php echo ucfirst($loan['status']); ?></span></td>
                            </tr>
                        </table>
                        
                        <div class="alert alert-warning">
                            <i class="fa fa-exclamation-circle"></i> Please note that your first monthly maintenance fee of R900 has been paid. Subsequent monthly fees will be R2,000.
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-4">
                <div class="feature-box">
                    <i class="fa fa-sitemap"></i>
                    <h3>5×5 Matrix</h3>
                    <p>Build your network with our powerful 5×5 matrix structure. Earn commissions as your network grows.</p>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="feature-box">
                    <i class="fa fa-credit-card"></i>
                    <h3>Wise Payments</h3>
                    <p>Receive your earnings securely through Wise payment gateway. No need to share sensitive banking details.</p>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="feature-box">
                    <i class="fa fa-line-chart"></i>
                    <h3>Track Progress</h3>
                    <p>Monitor your network growth, earnings, and matrix level through your personalized dashboard.</p>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-lg-12 text-center" style="margin: 30px 0;">
                <a href="home.php" class="btn btn-primary btn-lg">
                    <i class="fa fa-dashboard"></i> Go to Dashboard Now
                </a>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="vendor/jquery/jquery.min.js"></script>

    <!-- Bootstrap Core JavaScript -->
    <script src="vendor/bootstrap/js/bootstrap.min.js"></script>

    <!-- Metis Menu Plugin JavaScript -->
    <script src="vendor/metisMenu/metisMenu.min.js"></script>

    <!-- Custom Theme JavaScript -->
    <script src="dist/js/sb-admin-2.js"></script>
    
    <script>
        // Countdown timer
        var seconds = 10;
        var countdownElement = document.getElementById('countdown');
        
        function updateCountdown() {
            seconds--;
            countdownElement.textContent = seconds;
            
            if(seconds <= 0) {
                window.location.href = 'home.php';
            } else {
                setTimeout(updateCountdown, 1000);
            }
        }
        
        // Start countdown
        setTimeout(updateCountdown, 1000);
    </script>
</body>
</html>
