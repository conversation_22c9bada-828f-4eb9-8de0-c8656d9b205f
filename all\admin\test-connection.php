<?php
/**
 * Quick MySQL Connection Test for Admin Panel
 */

echo "<h2>Admin Panel - MySQL Connection Test</h2>";

// Test different ports (3308 first since 3306 is occupied)
$ports_to_test = [3308, 3306, 3307];
$connection_found = false;

foreach ($ports_to_test as $port) {
    echo "<h3>Testing Port $port</h3>";
    
    $connection = @mysqli_connect('localhost', 'root', '', '', $port);
    
    if ($connection) {
        echo "<p style='color: green; font-weight: bold;'>✅ SUCCESS! MySQL is running on port $port</p>";
        
        // Test if we can create/access the MLM database
        $create_db = mysqli_query($connection, "CREATE DATABASE IF NOT EXISTS mlm");
        if ($create_db) {
            echo "<p style='color: green;'>✅ MLM database is accessible</p>";
            
            // Test connection to MLM database
            mysqli_close($connection);
            $mlm_connection = @mysqli_connect('localhost', 'root', '', 'mlm', $port);
            
            if ($mlm_connection) {
                echo "<p style='color: green;'>✅ Connected to MLM database successfully</p>";
                
                // Update the connect.php file with working port
                $connect_file = 'php-includes/connect.php';
                if (file_exists($connect_file)) {
                    $content = file_get_contents($connect_file);
                    $updated_content = preg_replace('/\$db_port = "[0-9]+";/', '$db_port = "' . $port . '";', $content);
                    file_put_contents($connect_file, $updated_content);
                    echo "<p style='color: blue;'>🔧 Updated connect.php with port $port</p>";
                }
                
                mysqli_close($mlm_connection);
                $connection_found = true;
                break;
            }
        }
        
        mysqli_close($connection);
    } else {
        echo "<p style='color: red;'>❌ No connection on port $port</p>";
    }
}

if (!$connection_found) {
    echo "<div style='background: #ffebee; border: 1px solid #f44336; padding: 20px; margin: 20px; border-radius: 5px;'>";
    echo "<h3 style='color: #d32f2f;'>❌ MySQL Not Running</h3>";
    echo "<p><strong>XAMPP MySQL is not started. Please follow these steps:</strong></p>";
    echo "<ol>";
    echo "<li><strong>Open XAMPP Control Panel</strong> (Run as Administrator if needed)</li>";
    echo "<li><strong>Click 'Start' next to MySQL</strong></li>";
    echo "<li><strong>Wait for green 'Running' status</strong></li>";
    echo "<li><strong>Refresh this page</strong></li>";
    echo "</ol>";
    
    echo "<h4>If MySQL won't start:</h4>";
    echo "<ul>";
    echo "<li>Check if another MySQL service is running</li>";
    echo "<li>Try changing MySQL port in XAMPP config</li>";
    echo "<li>Restart XAMPP completely</li>";
    echo "<li>Check XAMPP error logs</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #d4edda; border: 1px solid #28a745; padding: 20px; margin: 20px; border-radius: 5px;'>";
    echo "<h3 style='color: #155724;'>🎉 Connection Successful!</h3>";
    echo "<p>MySQL is now working. You can now access the admin panel.</p>";
    echo "<p><a href='login.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Admin Login</a></p>";
    echo "</div>";
}

echo "<h3>XAMPP Status Check</h3>";
echo "<p>If you're still having issues, check these:</p>";
echo "<ul>";
echo "<li><strong>XAMPP Control Panel:</strong> MySQL should show 'Running' in green</li>";
echo "<li><strong>Port conflicts:</strong> Another service might be using port 3306</li>";
echo "<li><strong>Windows Services:</strong> Check if MySQL service is running in Windows Services</li>";
echo "<li><strong>Firewall:</strong> Windows Firewall might be blocking MySQL</li>";
echo "</ul>";
?>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    background: #f8f9fa;
}
h2, h3 { 
    color: #333; 
    border-bottom: 2px solid #007bff;
    padding-bottom: 5px;
}
</style>
