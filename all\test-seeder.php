<?php
/**
 * MLM Test Data Seeder
 * Generates sample users and builds multi-level matrix structure for testing
 */

// Prevent web access
if (isset($_SERVER['HTTP_HOST'])) {
    die('This script can only be run from command line or by direct access.');
}

// Set testing mode
$_ENV['TESTING_MODE'] = 'true';

require_once __DIR__ . '/php-includes/config.php';
require_once __DIR__ . '/php-includes/test-connect.php';
require_once __DIR__ . '/php-includes/error-handler.php';
require_once __DIR__ . '/php-includes/secure-database.php';
require_once __DIR__ . '/php-includes/password-security.php';

// Initialize components
ErrorHandler::init($con, ['display_errors' => true, 'log_to_file' => false]);
SecureDatabase::init($con);

echo "=== MLM Test Data Seeder ===\n";

// Configuration
$config = [
    'total_users' => 100,           // Total users to create
    'matrix_levels' => 5,           // Number of matrix levels to build
    'positions_per_level' => 5,     // Positions per level (5x5 matrix)
    'root_user_email' => '<EMAIL>',
    'base_email_domain' => 'test.com',
    'default_password' => 'TestUser123!',
    'loan_amount' => 2000,
    'monthly_payment' => 534,
    'maintenance_fee' => 900
];

// Parse command line arguments
if ($argc > 1) {
    for ($i = 1; $i < $argc; $i++) {
        if (strpos($argv[$i], '--users=') === 0) {
            $config['total_users'] = (int)substr($argv[$i], 8);
        } elseif (strpos($argv[$i], '--levels=') === 0) {
            $config['matrix_levels'] = (int)substr($argv[$i], 9);
        } elseif ($argv[$i] === '--help') {
            echo "Usage: php test-seeder.php [options]\n";
            echo "Options:\n";
            echo "  --users=N    Number of users to create (default: 100)\n";
            echo "  --levels=N   Number of matrix levels (default: 5)\n";
            echo "  --clean      Clean existing test data first\n";
            echo "  --help       Show this help message\n";
            exit(0);
        } elseif ($argv[$i] === '--clean') {
            cleanTestData();
        }
    }
}

echo "Configuration:\n";
echo "- Total users: {$config['total_users']}\n";
echo "- Matrix levels: {$config['matrix_levels']}\n";
echo "- Positions per level: {$config['positions_per_level']}\n\n";

try {
    // Start test run tracking
    $test_run_id = startTestRun("Seeder Run - {$config['total_users']} users");
    
    echo "Starting data seeding (Test Run ID: $test_run_id)...\n\n";
    
    // Step 1: Create root user
    echo "1. Creating root user...\n";
    $root_user = createRootUser($config);
    echo "   ✓ Root user created: {$root_user['email']}\n";
    
    // Step 2: Generate user data
    echo "\n2. Generating user data...\n";
    $users = generateUserData($config);
    echo "   ✓ Generated {$config['total_users']} user records\n";
    
    // Step 3: Build matrix structure
    echo "\n3. Building matrix structure...\n";
    $matrix = buildMatrixStructure($users, $config);
    echo "   ✓ Matrix structure built with {$config['matrix_levels']} levels\n";
    
    // Step 4: Create users in database
    echo "\n4. Creating users in database...\n";
    $created_users = createUsersInDatabase($matrix, $config, $test_run_id);
    echo "   ✓ Created {$created_users} users in database\n";
    
    // Step 5: Generate sample pins
    echo "\n5. Generating sample pins...\n";
    $pins_created = generateSamplePins($matrix, $config);
    echo "   ✓ Created $pins_created pins\n";
    
    // Step 6: Create matrix snapshots
    echo "\n6. Creating matrix snapshots...\n";
    $snapshots = createMatrixSnapshots($matrix, $test_run_id);
    echo "   ✓ Created $snapshots matrix snapshots\n";
    
    // Complete test run
    completeTestRun($test_run_id, $created_users);
    
    echo "\n=== Seeding Complete ===\n";
    echo "Test Run ID: $test_run_id\n";
    echo "Users created: $created_users\n";
    echo "Matrix levels: {$config['matrix_levels']}\n";
    echo "Root user: {$root_user['email']} (password: {$config['default_password']})\n\n";
    
    echo "Sample users for testing:\n";
    for ($i = 1; $i <= min(5, $config['total_users']); $i++) {
        echo "- user$i@{$config['base_email_domain']} (password: {$config['default_password']})\n";
    }
    
    echo "\nNext steps:\n";
    echo "1. Run 'php run-simulation.php --test-run=$test_run_id' to test commission logic\n";
    echo "2. Check test results in the test database\n";
    echo "3. Use 'php test-seeder.php --clean' to reset test data\n\n";
    
} catch (Exception $e) {
    echo "Seeding failed: " . $e->getMessage() . "\n";
    ErrorHandler::logError("Test seeding failed", ['error' => $e->getMessage()]);
    exit(1);
}

/**
 * Clean existing test data
 */
function cleanTestData() {
    global $con;
    
    echo "Cleaning existing test data...\n";
    
    $tables = [
        'test_matrix_snapshots',
        'mock_payouts', 
        'test_runs',
        'maintenance_fee',
        'loan',
        'income_received',
        'income',
        'tree',
        'pin_list',
        'join_requests',
        'user'
    ];
    
    // Disable foreign key checks
    mysqli_query($con, "SET FOREIGN_KEY_CHECKS = 0");
    
    foreach ($tables as $table) {
        $result = mysqli_query($con, "DELETE FROM `$table`");
        if ($result) {
            $affected = mysqli_affected_rows($con);
            echo "   ✓ Cleaned $table ($affected rows)\n";
        }
    }
    
    // Re-enable foreign key checks
    mysqli_query($con, "SET FOREIGN_KEY_CHECKS = 1");
    
    echo "✓ Test data cleaned\n\n";
}

/**
 * Start a new test run
 */
function startTestRun($description) {
    return SecureDatabase::insert(
        "INSERT INTO test_runs (run_name, description, status) VALUES (?, ?, 'running')",
        ["Test Seeder", $description]
    );
}

/**
 * Complete a test run
 */
function completeTestRun($test_run_id, $users_created) {
    SecureDatabase::update(
        "UPDATE test_runs SET users_created = ?, completed_at = NOW(), status = 'completed' WHERE id = ?",
        [$users_created, $test_run_id],
        'ii'
    );
}

/**
 * Create root user
 */
function createRootUser($config) {
    $root_user = [
        'email' => $config['root_user_email'],
        'password' => PasswordSecurity::hashPassword($config['default_password']),
        'mobile' => '+**********',
        'address' => 'Root User Address',
        'account' => '**********',
        'under_userid' => '',
        'matrix_position' => 0,
        'join_date' => date('Y-m-d')
    ];
    
    // Create user
    $user_id = SecureDatabase::insert(
        "INSERT INTO user (email, password, mobile, address, account, under_userid, matrix_position, join_date) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
        array_values($root_user),
        'ssssssss'
    );
    
    if ($user_id) {
        // Create tree entry
        SecureDatabase::insert(
            "INSERT INTO tree (userid, sponsor_id, level, matrix_count) VALUES (?, '', 0, 0)",
            [$root_user['email']]
        );
        
        // Create income entry
        SecureDatabase::insert(
            "INSERT INTO income (userid) VALUES (?)",
            [$root_user['email']]
        );
    }
    
    return $root_user;
}

/**
 * Generate user data array
 */
function generateUserData($config) {
    $users = [];
    
    for ($i = 1; $i <= $config['total_users']; $i++) {
        $users[] = [
            'email' => "user$i@{$config['base_email_domain']}",
            'password' => PasswordSecurity::hashPassword($config['default_password']),
            'mobile' => '+1' . str_pad($i, 9, '0', STR_PAD_LEFT),
            'address' => "Test Address $i",
            'account' => str_pad($i, 10, '0', STR_PAD_LEFT),
            'join_date' => date('Y-m-d'),
            'level' => 0,
            'sponsor' => null,
            'matrix_position' => 0
        ];
    }
    
    return $users;
}

/**
 * Build matrix structure
 */
function buildMatrixStructure($users, $config) {
    $matrix = [];
    $current_level = 1;
    $users_per_level = $config['positions_per_level'];
    $user_index = 0;
    
    // Root level (level 0) - assign sponsors from root user
    $root_email = $config['root_user_email'];
    
    for ($pos = 1; $pos <= min($config['positions_per_level'], count($users)); $pos++) {
        if ($user_index < count($users)) {
            $users[$user_index]['sponsor'] = $root_email;
            $users[$user_index]['matrix_position'] = $pos;
            $users[$user_index]['level'] = 1;
            $matrix[1][] = $users[$user_index];
            $user_index++;
        }
    }
    
    // Build subsequent levels
    for ($level = 2; $level <= $config['matrix_levels'] && $user_index < count($users); $level++) {
        $matrix[$level] = [];
        $sponsors_from_previous_level = $matrix[$level - 1] ?? [];
        
        foreach ($sponsors_from_previous_level as $sponsor) {
            for ($pos = 1; $pos <= $config['positions_per_level'] && $user_index < count($users); $pos++) {
                $users[$user_index]['sponsor'] = $sponsor['email'];
                $users[$user_index]['matrix_position'] = $pos;
                $users[$user_index]['level'] = $level;
                $matrix[$level][] = $users[$user_index];
                $user_index++;
            }
        }
    }
    
    return $matrix;
}

/**
 * Create users in database
 */
function createUsersInDatabase($matrix, $config, $test_run_id) {
    $created = 0;

    foreach ($matrix as $level => $users) {
        echo "   Creating level $level users...\n";

        foreach ($users as $user) {
            try {
                // Create user
                $user_id = SecureDatabase::insert(
                    "INSERT INTO user (email, password, mobile, address, account, under_userid, matrix_position, join_date) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
                    [
                        $user['email'],
                        $user['password'],
                        $user['mobile'],
                        $user['address'],
                        $user['account'],
                        $user['sponsor'],
                        $user['matrix_position'],
                        $user['join_date']
                    ],
                    'ssssssss'
                );

                if ($user_id) {
                    // Create tree entry
                    SecureDatabase::insert(
                        "INSERT INTO tree (userid, sponsor_id, level, matrix_count) VALUES (?, ?, ?, ?)",
                        [$user['email'], $user['sponsor'], $user['level'], 0],
                        'ssii'
                    );

                    // Update sponsor's matrix position
                    if ($user['sponsor']) {
                        $position_field = "position" . $user['matrix_position'];
                        SecureDatabase::update(
                            "UPDATE tree SET `$position_field` = ? WHERE userid = ?",
                            [$user['email'], $user['sponsor']]
                        );
                    }

                    // Create income entry
                    SecureDatabase::insert(
                        "INSERT INTO income (userid, matrix_level) VALUES (?, ?)",
                        [$user['email'], $user['level']],
                        'si'
                    );

                    // Create loan entry
                    SecureDatabase::insert(
                        "INSERT INTO loan (userid, loan_amount, remaining_amount, monthly_payment, start_date) VALUES (?, ?, ?, ?, ?)",
                        [$user['email'], $config['loan_amount'], $config['loan_amount'], $config['monthly_payment'], $user['join_date']],
                        'siiss'
                    );

                    // Create maintenance fee entry
                    SecureDatabase::insert(
                        "INSERT INTO maintenance_fee (userid, month, fee_amount, date_paid, status) VALUES (?, 1, ?, ?, 'paid')",
                        [$user['email'], $config['maintenance_fee'], $user['join_date']],
                        'sis'
                    );

                    $created++;
                }
            } catch (Exception $e) {
                echo "   Warning: Failed to create user {$user['email']}: " . $e->getMessage() . "\n";
            }
        }
    }

    return $created;
}

/**
 * Generate sample pins for users
 */
function generateSamplePins($matrix, $config) {
    $pins_created = 0;
    $pin_counter = 100000;

    foreach ($matrix as $level => $users) {
        foreach ($users as $user) {
            // Create 3 pins per user
            for ($i = 1; $i <= 3; $i++) {
                $pin = $pin_counter + $i;

                $result = SecureDatabase::insert(
                    "INSERT INTO pin_list (userid, pin, status) VALUES (?, ?, 'open')",
                    [$user['email'], $pin],
                    'si'
                );

                if ($result) {
                    $pins_created++;
                }
            }
            $pin_counter += 10; // Space out pins
        }
    }

    return $pins_created;
}

/**
 * Create matrix snapshots for analysis
 */
function createMatrixSnapshots($matrix, $test_run_id) {
    $snapshots = 0;

    foreach ($matrix as $level => $users) {
        foreach ($users as $user) {
            // Calculate downline count
            $downline_count = calculateDownlineCount($user['email'], $matrix);

            $result = SecureDatabase::insert(
                "INSERT INTO test_matrix_snapshots (test_run_id, user_id, level, matrix_count, total_downline, commission_earned) VALUES (?, ?, ?, ?, ?, 0.00)",
                [$test_run_id, $user['email'], $user['level'], 0, $downline_count],
                'isiii'
            );

            if ($result) {
                $snapshots++;
            }
        }
    }

    return $snapshots;
}

/**
 * Calculate downline count for a user
 */
function calculateDownlineCount($user_email, $matrix) {
    $count = 0;

    foreach ($matrix as $level => $users) {
        foreach ($users as $user) {
            if ($user['sponsor'] === $user_email) {
                $count++;
                $count += calculateDownlineCount($user['email'], $matrix);
            }
        }
    }

    return $count;
}
?>
