<?php
/**
 * Accessibility Features for South African Users
 * Includes high contrast, large text, and assistive technology support
 */

class Accessibility {
    
    private static $preferences = [];
    
    /**
     * Initialize accessibility preferences
     */
    public static function init() {
        // Load preferences from session
        self::$preferences = $_SESSION['accessibility_preferences'] ?? [
            'high_contrast' => false,
            'large_text' => false,
            'reduced_motion' => false,
            'screen_reader' => false,
            'font_family' => 'default'
        ];
        
        // Detect system preferences
        self::detectSystemPreferences();
    }
    
    /**
     * Detect system accessibility preferences
     */
    private static function detectSystemPreferences() {
        // These would typically be detected via CSS media queries
        // For server-side detection, we can check user agent or headers
        
        if (isset($_SERVER['HTTP_USER_AGENT'])) {
            $userAgent = $_SERVER['HTTP_USER_AGENT'];
            
            // Detect screen readers
            if (strpos($userAgent, 'NVDA') !== false || 
                strpos($userAgent, 'JAWS') !== false || 
                strpos($userAgent, 'VoiceOver') !== false) {
                self::$preferences['screen_reader'] = true;
            }
        }
    }
    
    /**
     * Set accessibility preference
     */
    public static function setPreference($key, $value) {
        if (array_key_exists($key, self::$preferences)) {
            self::$preferences[$key] = $value;
            $_SESSION['accessibility_preferences'] = self::$preferences;
        }
    }
    
    /**
     * Get accessibility preference
     */
    public static function getPreference($key) {
        return self::$preferences[$key] ?? false;
    }
    
    /**
     * Generate accessibility CSS
     */
    public static function generateCSS() {
        $css = '';
        
        if (self::$preferences['high_contrast']) {
            $css .= self::getHighContrastCSS();
        }
        
        if (self::$preferences['large_text']) {
            $css .= self::getLargeTextCSS();
        }
        
        if (self::$preferences['reduced_motion']) {
            $css .= self::getReducedMotionCSS();
        }
        
        if (self::$preferences['font_family'] !== 'default') {
            $css .= self::getFontFamilyCSS(self::$preferences['font_family']);
        }
        
        return $css;
    }
    
    /**
     * High contrast CSS
     */
    private static function getHighContrastCSS() {
        return "
        .high-contrast {
            --sa-green: #00ff00 !important;
            --sa-gold: #ffff00 !important;
            --sa-blue: #0000ff !important;
            --text-dark: #000000 !important;
            --text-light: #666666 !important;
            background: #000000 !important;
            color: #ffffff !important;
        }
        
        .high-contrast .btn {
            border: 2px solid #ffffff !important;
            background: #000000 !important;
            color: #ffffff !important;
        }
        
        .high-contrast .btn:hover {
            background: #ffffff !important;
            color: #000000 !important;
        }
        
        .high-contrast .form-control {
            border: 2px solid #ffffff !important;
            background: #000000 !important;
            color: #ffffff !important;
        }
        
        .high-contrast .card, .high-contrast .dashboard-card {
            border: 2px solid #ffffff !important;
            background: #000000 !important;
            color: #ffffff !important;
        }
        ";
    }
    
    /**
     * Large text CSS
     */
    private static function getLargeTextCSS() {
        return "
        .large-text {
            font-size: 1.25em !important;
        }
        
        .large-text h1 { font-size: 2.5em !important; }
        .large-text h2 { font-size: 2.2em !important; }
        .large-text h3 { font-size: 1.8em !important; }
        .large-text h4 { font-size: 1.5em !important; }
        .large-text h5 { font-size: 1.3em !important; }
        .large-text h6 { font-size: 1.1em !important; }
        
        .large-text .btn {
            font-size: 1.3em !important;
            padding: 1em 1.5em !important;
        }
        
        .large-text .form-control {
            font-size: 1.2em !important;
            padding: 1em !important;
        }
        
        .large-text .nav-link {
            font-size: 1.2em !important;
        }
        ";
    }
    
    /**
     * Reduced motion CSS
     */
    private static function getReducedMotionCSS() {
        return "
        .reduced-motion * {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
            scroll-behavior: auto !important;
        }
        ";
    }
    
    /**
     * Font family CSS
     */
    private static function getFontFamilyCSS($fontFamily) {
        $fonts = [
            'dyslexic' => '"OpenDyslexic", "Comic Sans MS", cursive',
            'serif' => 'Georgia, "Times New Roman", serif',
            'monospace' => '"Courier New", Courier, monospace'
        ];
        
        $font = $fonts[$fontFamily] ?? 'inherit';
        
        return "
        .custom-font, .custom-font * {
            font-family: $font !important;
        }
        ";
    }
    
    /**
     * Generate accessibility toolbar HTML
     */
    public static function generateToolbar() {
        $currentLang = Localization::getCurrentLanguage();
        $languages = Localization::getAvailableLanguages();
        
        ob_start();
        ?>
        <div id="accessibility-toolbar" class="accessibility-toolbar" role="toolbar" aria-label="Accessibility Options">
            <button type="button" class="accessibility-toggle" onclick="toggleAccessibilityPanel()" 
                    aria-label="Open accessibility options" title="Accessibility Options">
                <i class="fas fa-universal-access" aria-hidden="true"></i>
            </button>
            
            <div id="accessibility-panel" class="accessibility-panel" style="display: none;" role="dialog" 
                 aria-labelledby="accessibility-title" aria-modal="true">
                <div class="accessibility-header">
                    <h3 id="accessibility-title">Accessibility Options</h3>
                    <button type="button" class="accessibility-close" onclick="toggleAccessibilityPanel()" 
                            aria-label="Close accessibility options">
                        <i class="fas fa-times" aria-hidden="true"></i>
                    </button>
                </div>
                
                <div class="accessibility-content">
                    <!-- Language Selection -->
                    <div class="accessibility-section">
                        <h4>Language / Ulimi / Taal / Ulwimi</h4>
                        <div class="language-grid">
                            <?php foreach ($languages as $code => $name): ?>
                                <button type="button" class="language-btn <?php echo $code === $currentLang ? 'active' : ''; ?>" 
                                        onclick="changeLanguage('<?php echo $code; ?>')" 
                                        aria-pressed="<?php echo $code === $currentLang ? 'true' : 'false'; ?>">
                                    <?php echo $name; ?>
                                </button>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    
                    <!-- Visual Options -->
                    <div class="accessibility-section">
                        <h4><?php echo __('Visual Options'); ?></h4>
                        <div class="accessibility-options">
                            <label class="accessibility-option">
                                <input type="checkbox" id="high-contrast" 
                                       <?php echo self::$preferences['high_contrast'] ? 'checked' : ''; ?>
                                       onchange="toggleAccessibilityFeature('high_contrast', this.checked)">
                                <span>High Contrast</span>
                            </label>
                            
                            <label class="accessibility-option">
                                <input type="checkbox" id="large-text" 
                                       <?php echo self::$preferences['large_text'] ? 'checked' : ''; ?>
                                       onchange="toggleAccessibilityFeature('large_text', this.checked)">
                                <span>Large Text</span>
                            </label>
                            
                            <label class="accessibility-option">
                                <input type="checkbox" id="reduced-motion" 
                                       <?php echo self::$preferences['reduced_motion'] ? 'checked' : ''; ?>
                                       onchange="toggleAccessibilityFeature('reduced_motion', this.checked)">
                                <span>Reduce Motion</span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- Font Options -->
                    <div class="accessibility-section">
                        <h4>Font Options</h4>
                        <select id="font-family" onchange="changeFontFamily(this.value)" 
                                aria-label="Select font family">
                            <option value="default" <?php echo self::$preferences['font_family'] === 'default' ? 'selected' : ''; ?>>
                                Default Font
                            </option>
                            <option value="dyslexic" <?php echo self::$preferences['font_family'] === 'dyslexic' ? 'selected' : ''; ?>>
                                Dyslexia Friendly
                            </option>
                            <option value="serif" <?php echo self::$preferences['font_family'] === 'serif' ? 'selected' : ''; ?>>
                                Serif Font
                            </option>
                            <option value="monospace" <?php echo self::$preferences['font_family'] === 'monospace' ? 'selected' : ''; ?>>
                                Monospace Font
                            </option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        
        <style>
        .accessibility-toolbar {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
        }
        
        .accessibility-toggle {
            background: var(--sa-blue);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            font-size: 1.2rem;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
        }
        
        .accessibility-toggle:hover {
            background: var(--sa-green);
            transform: scale(1.1);
        }
        
        .accessibility-panel {
            position: absolute;
            top: 60px;
            right: 0;
            width: 300px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.3);
            border: 1px solid #ddd;
        }
        
        .accessibility-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #eee;
            background: var(--sa-green);
            color: white;
            border-radius: 10px 10px 0 0;
        }
        
        .accessibility-header h3 {
            margin: 0;
            font-size: 1rem;
        }
        
        .accessibility-close {
            background: none;
            border: none;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
        }
        
        .accessibility-content {
            padding: 1rem;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .accessibility-section {
            margin-bottom: 1.5rem;
        }
        
        .accessibility-section h4 {
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
            color: var(--text-dark);
        }
        
        .language-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.5rem;
        }
        
        .language-btn {
            padding: 0.5rem;
            border: 1px solid #ddd;
            background: white;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.2s ease;
        }
        
        .language-btn:hover {
            background: var(--warm-white);
        }
        
        .language-btn.active {
            background: var(--sa-green);
            color: white;
            border-color: var(--sa-green);
        }
        
        .accessibility-options {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .accessibility-option {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
            font-size: 0.9rem;
        }
        
        .accessibility-option input[type="checkbox"] {
            width: 16px;
            height: 16px;
        }
        
        #font-family {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .accessibility-panel {
                width: 280px;
                right: -10px;
            }
            
            .accessibility-toolbar {
                top: 10px;
                right: 10px;
            }
        }
        </style>
        
        <script>
        function toggleAccessibilityPanel() {
            const panel = document.getElementById('accessibility-panel');
            const isVisible = panel.style.display !== 'none';
            panel.style.display = isVisible ? 'none' : 'block';
            
            if (!isVisible) {
                panel.focus();
            }
        }
        
        function changeLanguage(language) {
            fetch('php-includes/set-language.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ language: language })
            }).then(() => {
                location.reload();
            });
        }
        
        function toggleAccessibilityFeature(feature, enabled) {
            const body = document.body;
            
            switch(feature) {
                case 'high_contrast':
                    body.classList.toggle('high-contrast', enabled);
                    break;
                case 'large_text':
                    body.classList.toggle('large-text', enabled);
                    break;
                case 'reduced_motion':
                    body.classList.toggle('reduced-motion', enabled);
                    break;
            }
            
            // Save preference
            fetch('php-includes/set-accessibility.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ feature: feature, enabled: enabled })
            });
        }
        
        function changeFontFamily(fontFamily) {
            const body = document.body;
            body.className = body.className.replace(/custom-font-\w+/g, '');
            
            if (fontFamily !== 'default') {
                body.classList.add('custom-font');
                body.classList.add('custom-font-' + fontFamily);
            }
            
            // Save preference
            fetch('php-includes/set-accessibility.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ feature: 'font_family', value: fontFamily })
            });
        }
        
        // Apply saved preferences on load
        document.addEventListener('DOMContentLoaded', function() {
            const body = document.body;
            
            <?php if (self::$preferences['high_contrast']): ?>
            body.classList.add('high-contrast');
            <?php endif; ?>
            
            <?php if (self::$preferences['large_text']): ?>
            body.classList.add('large-text');
            <?php endif; ?>
            
            <?php if (self::$preferences['reduced_motion']): ?>
            body.classList.add('reduced-motion');
            <?php endif; ?>
            
            <?php if (self::$preferences['font_family'] !== 'default'): ?>
            body.classList.add('custom-font');
            body.classList.add('custom-font-<?php echo self::$preferences['font_family']; ?>');
            <?php endif; ?>
        });
        
        // Close panel when clicking outside
        document.addEventListener('click', function(e) {
            const toolbar = document.getElementById('accessibility-toolbar');
            const panel = document.getElementById('accessibility-panel');
            
            if (!toolbar.contains(e.target) && panel.style.display !== 'none') {
                panel.style.display = 'none';
            }
        });
        
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const panel = document.getElementById('accessibility-panel');
                if (panel.style.display !== 'none') {
                    panel.style.display = 'none';
                }
            }
        });
        </script>
        <?php
        return ob_get_clean();
    }
}
?>
