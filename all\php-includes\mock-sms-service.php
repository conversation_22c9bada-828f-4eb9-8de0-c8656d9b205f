<?php
/**
 * Mock SMS Service
 * Simulates SMS sending for testing without real SMS costs
 */

class MockSMSService {
    
    private $con;
    private $success_rate;
    private $delay_simulation;
    
    public function __construct($connection) {
        $this->con = $connection;
        $this->success_rate = 0.98; // 98% success rate
        $this->delay_simulation = true;
    }
    
    /**
     * Mock SMS sending
     * 
     * @param string $to Phone number
     * @param string $message SMS message
     * @return array Mock response
     */
    public function sendSMS($to, $message) {
        if ($this->delay_simulation) {
            usleep(rand(100000, 500000)); // 0.1-0.5 second delay
        }
        
        $success = (rand(1, 100) / 100) <= $this->success_rate;
        $message_id = 'mock_sms_' . time() . '_' . rand(1000, 9999);
        
        $response = [
            'success' => $success,
            'message_id' => $message_id,
            'to' => $to,
            'message' => $message,
            'status' => $success ? 'sent' : 'failed',
            'error' => $success ? null : $this->generateMockError(),
            'cost' => $success ? 0.05 : 0, // Mock cost
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        // Log SMS attempt
        $this->logSMSAttempt($response);
        
        return $response;
    }
    
    /**
     * Mock OTP SMS sending
     * 
     * @param string $to Phone number
     * @param string $otp OTP code
     * @return array Mock response
     */
    public function sendOTP($to, $otp) {
        $message = "Your MLM verification code is: $otp. This code will expire in 15 minutes.";
        return $this->sendSMS($to, $message);
    }
    
    /**
     * Mock WhatsApp message sending
     * 
     * @param string $to Phone number
     * @param string $message WhatsApp message
     * @return array Mock response
     */
    public function sendWhatsApp($to, $message) {
        if ($this->delay_simulation) {
            usleep(rand(200000, 800000)); // 0.2-0.8 second delay
        }
        
        $success = (rand(1, 100) / 100) <= $this->success_rate;
        $message_id = 'mock_whatsapp_' . time() . '_' . rand(1000, 9999);
        
        $response = [
            'success' => $success,
            'message_id' => $message_id,
            'to' => $to,
            'message' => $message,
            'platform' => 'whatsapp',
            'status' => $success ? 'sent' : 'failed',
            'error' => $success ? null : $this->generateMockError(),
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        // Log WhatsApp attempt
        $this->logSMSAttempt($response);
        
        return $response;
    }
    
    /**
     * Get message status
     * 
     * @param string $message_id Message ID
     * @return array Mock status response
     */
    public function getMessageStatus($message_id) {
        if ($this->delay_simulation) {
            usleep(rand(50000, 200000)); // 0.05-0.2 second delay
        }
        
        // In a real implementation, this would query the SMS provider
        // For mock, we'll simulate different statuses
        $statuses = ['sent', 'delivered', 'failed', 'pending'];
        $status = $statuses[array_rand($statuses)];
        
        return [
            'success' => true,
            'message_id' => $message_id,
            'status' => $status,
            'delivered_at' => $status === 'delivered' ? date('Y-m-d H:i:s') : null,
            'error' => $status === 'failed' ? $this->generateMockError() : null
        ];
    }
    
    /**
     * Log SMS attempt to database
     */
    private function logSMSAttempt($response) {
        // Create a simple log table entry
        $log_entry = [
            'type' => $response['platform'] ?? 'sms',
            'to' => $response['to'],
            'message' => $response['message'],
            'status' => $response['status'],
            'message_id' => $response['message_id'],
            'error' => $response['error'],
            'timestamp' => $response['timestamp']
        ];
        
        // Log to file for testing
        $log_message = sprintf(
            "[%s] %s to %s: %s (Status: %s, ID: %s)\n",
            $log_entry['timestamp'],
            strtoupper($log_entry['type']),
            $log_entry['to'],
            substr($log_entry['message'], 0, 50) . '...',
            $log_entry['status'],
            $log_entry['message_id']
        );
        
        file_put_contents('logs/mock_sms.log', $log_message, FILE_APPEND | LOCK_EX);
        
        // Also log via ErrorHandler for centralized logging
        ErrorHandler::logInfo("Mock SMS sent", $log_entry);
    }
    
    /**
     * Generate mock error messages
     */
    private function generateMockError() {
        $errors = [
            'Invalid phone number',
            'Network timeout',
            'SMS service temporarily unavailable',
            'Message too long',
            'Blocked number',
            'Insufficient credits',
            'Rate limit exceeded'
        ];
        
        return $errors[array_rand($errors)];
    }
    
    /**
     * Set success rate for testing
     */
    public function setSuccessRate($rate) {
        $this->success_rate = max(0, min(1, $rate));
    }
    
    /**
     * Enable/disable delay simulation
     */
    public function setDelaySimulation($enabled) {
        $this->delay_simulation = $enabled;
    }
    
    /**
     * Get mock SMS statistics
     */
    public function getStatistics($date_from = null, $date_to = null) {
        // In a real implementation, this would query a proper SMS log table
        // For mock, we'll return simulated statistics
        return [
            'total_sent' => rand(100, 1000),
            'successful' => rand(95, 99),
            'failed' => rand(1, 5),
            'total_cost' => rand(5, 50),
            'average_delivery_time' => rand(1, 5) . ' seconds',
            'date_range' => [
                'from' => $date_from ?: date('Y-m-d', strtotime('-30 days')),
                'to' => $date_to ?: date('Y-m-d')
            ]
        ];
    }
}

/**
 * Mock WhatsApp helper function for backward compatibility
 */
function sendWhatsAppMessage($mobile, $message) {
    if (Config::shouldMockSMS()) {
        $mockSMS = new MockSMSService($GLOBALS['con']);
        $result = $mockSMS->sendWhatsApp($mobile, $message);
        return $result['success'];
    }
    
    // Fall back to real WhatsApp implementation if not mocking
    // Include your real WhatsApp implementation here
    return true; // Placeholder
}

/**
 * Mock OTP sending with fallback
 */
function sendOTPWithFallback($mobile, $otp) {
    if (Config::shouldMockSMS()) {
        $mockSMS = new MockSMSService($GLOBALS['con']);
        
        // Try WhatsApp first
        $whatsapp_result = $mockSMS->sendWhatsApp($mobile, "Your MLM verification code is: $otp");
        if ($whatsapp_result['success']) {
            return true;
        }
        
        // Fallback to SMS
        $sms_result = $mockSMS->sendOTP($mobile, $otp);
        return $sms_result['success'];
    }
    
    // Fall back to real implementation if not mocking
    // Include your real SMS/WhatsApp implementation here
    return true; // Placeholder
}
?>
