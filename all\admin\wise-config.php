<?php
include('php-includes/check-login.php');
require('php-includes/connect.php');
include('../php-includes/wise-api.php');

// Handle form submission
if(isset($_POST['update_config'])) {
    $api_token = mysqli_real_escape_string($con, $_POST['api_token']);
    $profile_id = mysqli_real_escape_string($con, $_POST['profile_id']);
    $sandbox_mode = isset($_POST['sandbox_mode']) ? 1 : 0;
    $default_currency = mysqli_real_escape_string($con, $_POST['default_currency']);
    
    // Check if config exists
    $query = mysqli_query($con, "SELECT * FROM wise_config WHERE id = 1");
    
    if(mysqli_num_rows($query) > 0) {
        // Update existing config
        $update = mysqli_query($con, "UPDATE wise_config SET 
            api_token = '$api_token',
            profile_id = '$profile_id',
            sandbox_mode = $sandbox_mode,
            default_currency = '$default_currency'
            WHERE id = 1");
    } else {
        // Insert new config
        $insert = mysqli_query($con, "INSERT INTO wise_config 
            (api_token, profile_id, sandbox_mode, default_currency) 
            VALUES ('$api_token', '$profile_id', $sandbox_mode, '$default_currency')");
    }
    
    if(isset($update) && $update) {
        $success_message = "Wise API configuration updated successfully.";
    } else if(isset($insert) && $insert) {
        $success_message = "Wise API configuration saved successfully.";
    } else {
        $error_message = "Error saving configuration: " . mysqli_error($con);
    }
}

// Get current config
$query = mysqli_query($con, "SELECT * FROM wise_config WHERE id = 1");
$config = mysqli_num_rows($query) > 0 ? mysqli_fetch_assoc($query) : null;

// Test API connection if requested
if(isset($_POST['test_connection']) && $config) {
    $wise = new WiseAPI($config['api_token'], $config['profile_id'], $config['sandbox_mode'] == 1);
    $response = $wise->makeRequest("/v1/profiles", "GET");
    
    if($response['code'] == 200 && isset($response['data'])) {
        $test_success = "API connection successful! Found " . count($response['data']) . " profile(s).";
    } else {
        $test_error = "API connection failed. Error: " . 
            (isset($response['error']) ? $response['error'] : "HTTP Code: " . $response['code']);
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>MLM Admin - Wise Payment Gateway Configuration</title>

    <!-- Bootstrap Core CSS -->
    <link href="vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">

    <!-- MetisMenu CSS -->
    <link href="vendor/metisMenu/metisMenu.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="dist/css/sb-admin-2.css" rel="stylesheet">

    <!-- Custom Fonts -->
    <link href="vendor/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css">
</head>

<body>

    <div id="wrapper">

        <!-- Navigation -->
        <?php include('php-includes/menu.php'); ?>

        <!-- Page Content -->
        <div id="page-wrapper">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-lg-12">
                        <h1 class="page-header">Wise Payment Gateway Configuration</h1>
                    </div>
                    <!-- /.col-lg-12 -->
                </div>
                <!-- /.row -->
                
                <?php if(isset($success_message)): ?>
                <div class="alert alert-success">
                    <?php echo $success_message; ?>
                </div>
                <?php endif; ?>
                
                <?php if(isset($error_message)): ?>
                <div class="alert alert-danger">
                    <?php echo $error_message; ?>
                </div>
                <?php endif; ?>
                
                <?php if(isset($test_success)): ?>
                <div class="alert alert-success">
                    <?php echo $test_success; ?>
                </div>
                <?php endif; ?>
                
                <?php if(isset($test_error)): ?>
                <div class="alert alert-danger">
                    <?php echo $test_error; ?>
                </div>
                <?php endif; ?>
                
                <div class="row">
                    <div class="col-lg-6">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                Wise API Settings
                            </div>
                            <div class="panel-body">
                                <form method="post" action="">
                                    <div class="form-group">
                                        <label>API Token</label>
                                        <input type="text" class="form-control" name="api_token" 
                                            value="<?php echo $config ? $config['api_token'] : ''; ?>" required>
                                        <p class="help-block">Your Wise API token. Create one in your Wise Business account.</p>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label>Profile ID</label>
                                        <input type="text" class="form-control" name="profile_id" 
                                            value="<?php echo $config ? $config['profile_id'] : ''; ?>" required>
                                        <p class="help-block">Your Wise profile ID. Find this in your Wise Business account.</p>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label>Default Currency</label>
                                        <select class="form-control" name="default_currency">
                                            <option value="USD" <?php echo ($config && $config['default_currency'] == 'USD') ? 'selected' : ''; ?>>USD</option>
                                            <option value="EUR" <?php echo ($config && $config['default_currency'] == 'EUR') ? 'selected' : ''; ?>>EUR</option>
                                            <option value="GBP" <?php echo ($config && $config['default_currency'] == 'GBP') ? 'selected' : ''; ?>>GBP</option>
                                            <option value="CAD" <?php echo ($config && $config['default_currency'] == 'CAD') ? 'selected' : ''; ?>>CAD</option>
                                            <option value="AUD" <?php echo ($config && $config['default_currency'] == 'AUD') ? 'selected' : ''; ?>>AUD</option>
                                            <option value="ZAR" <?php echo ($config && $config['default_currency'] == 'ZAR') ? 'selected' : ''; ?>>ZAR</option>
                                        </select>
                                        <p class="help-block">Default currency for payments.</p>
                                    </div>
                                    
                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox" name="sandbox_mode" 
                                                <?php echo ($config && $config['sandbox_mode'] == 1) ? 'checked' : ''; ?>>
                                            Use Sandbox Mode (for testing)
                                        </label>
                                    </div>
                                    
                                    <button type="submit" name="update_config" class="btn btn-primary">Save Configuration</button>
                                    
                                    <?php if($config): ?>
                                    <button type="submit" name="test_connection" class="btn btn-info">Test Connection</button>
                                    <?php endif; ?>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-6">
                        <div class="panel panel-info">
                            <div class="panel-heading">
                                Wise Integration Instructions
                            </div>
                            <div class="panel-body">
                                <h4>Setting Up Wise Integration</h4>
                                <ol>
                                    <li>Create a Wise Business account at <a href="https://wise.com/business" target="_blank">wise.com/business</a></li>
                                    <li>In your Wise account, go to Settings > API tokens and create a new token</li>
                                    <li>Copy your API token and profile ID to the form on the left</li>
                                    <li>For testing, enable Sandbox Mode and use sandbox credentials</li>
                                    <li>Click "Save Configuration" and then "Test Connection" to verify</li>
                                </ol>
                                
                                <h4>Making Batch Payments</h4>
                                <p>Once configured, you can use the Wise integration to make batch payments to your MLM members based on their matrix earnings. Go to the "Wise Batch Payments" page to process payments.</p>
                                
                                <h4>Important Notes</h4>
                                <ul>
                                    <li>Ensure your Wise account is funded before making payments</li>
                                    <li>Recipients must be set up in the system before payments can be sent</li>
                                    <li>All payment transactions are logged for auditing purposes</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- /.row -->
            </div>
            <!-- /.container-fluid -->
        </div>
        <!-- /#page-wrapper -->

    </div>
    <!-- /#wrapper -->

    <!-- jQuery -->
    <script src="vendor/jquery/jquery.min.js"></script>

    <!-- Bootstrap Core JavaScript -->
    <script src="vendor/bootstrap/js/bootstrap.min.js"></script>

    <!-- Metis Menu Plugin JavaScript -->
    <script src="vendor/metisMenu/metisMenu.min.js"></script>

    <!-- Custom Theme JavaScript -->
    <script src="dist/js/sb-admin-2.js"></script>

</body>

</html>
