<?php
/**
 * MLM Simulation Test Runner
 * Orchestrates complete MLM testing scenarios
 */

// Prevent web access
if (isset($_SERVER['HTTP_HOST'])) {
    die('This script can only be run from command line or by direct access.');
}

// Set testing mode
$_ENV['TESTING_MODE'] = 'true';

require_once __DIR__ . '/php-includes/config.php';
require_once __DIR__ . '/php-includes/test-connect.php';
require_once __DIR__ . '/php-includes/error-handler.php';
require_once __DIR__ . '/php-includes/secure-database.php';
require_once __DIR__ . '/php-includes/mock-stitch-payouts.php';
require_once __DIR__ . '/php-includes/mock-sms-service.php';
require_once __DIR__ . '/php-includes/commission-processor.php';

// Initialize components
ErrorHandler::init($con, ['display_errors' => true, 'log_to_file' => true]);
SecureDatabase::init($con);

echo "=== MLM Simulation Test Runner ===\n";

// Configuration
$config = [
    'test_run_id' => null,
    'new_registrations' => 10,
    'commission_scenarios' => ['matrix_level_up', 'new_member_bonus', 'deep_matrix'],
    'payout_simulation' => true,
    'generate_report' => true,
    'cleanup_after' => false
];

// Parse command line arguments
if ($argc > 1) {
    for ($i = 1; $i < $argc; $i++) {
        if (strpos($argv[$i], '--test-run=') === 0) {
            $config['test_run_id'] = (int)substr($argv[$i], 12);
        } elseif (strpos($argv[$i], '--registrations=') === 0) {
            $config['new_registrations'] = (int)substr($argv[$i], 16);
        } elseif ($argv[$i] === '--no-payouts') {
            $config['payout_simulation'] = false;
        } elseif ($argv[$i] === '--cleanup') {
            $config['cleanup_after'] = true;
        } elseif ($argv[$i] === '--help') {
            showHelp();
            exit(0);
        }
    }
}

echo "Configuration:\n";
echo "- Test Run ID: " . ($config['test_run_id'] ?: 'Auto-detect latest') . "\n";
echo "- New Registrations: {$config['new_registrations']}\n";
echo "- Payout Simulation: " . ($config['payout_simulation'] ? 'Yes' : 'No') . "\n";
echo "- Generate Report: " . ($config['generate_report'] ? 'Yes' : 'No') . "\n\n";

try {
    // Step 1: Initialize test run
    echo "1. Initializing test run...\n";
    $test_run_id = initializeTestRun($config);
    echo "   ✓ Test Run ID: $test_run_id\n";
    
    // Step 2: Verify test data exists
    echo "\n2. Verifying test data...\n";
    $user_count = verifyTestData();
    echo "   ✓ Found $user_count test users\n";
    
    // Step 3: Simulate new registrations
    echo "\n3. Simulating new registrations...\n";
    $new_users = simulateNewRegistrations($config['new_registrations'], $test_run_id);
    echo "   ✓ Created {$new_users['count']} new registrations\n";
    
    // Step 4: Process commissions
    echo "\n4. Processing commissions...\n";
    $commission_results = processCommissions($new_users['users'], $test_run_id);
    echo "   ✓ Processed {$commission_results['total_commissions']} commissions\n";
    echo "   ✓ Total commission amount: R{$commission_results['total_amount']}\n";
    
    // Step 5: Simulate payouts
    if ($config['payout_simulation']) {
        echo "\n5. Simulating payouts...\n";
        $payout_results = simulatePayouts($test_run_id);
        echo "   ✓ Processed {$payout_results['total_payouts']} payouts\n";
        echo "   ✓ Successful: {$payout_results['successful']}, Failed: {$payout_results['failed']}\n";
    }
    
    // Step 6: Generate verification report
    if ($config['generate_report']) {
        echo "\n6. Generating verification report...\n";
        $report = generateVerificationReport($test_run_id);
        echo "   ✓ Report generated: {$report['filename']}\n";
    }
    
    // Step 7: Cleanup (if requested)
    if ($config['cleanup_after']) {
        echo "\n7. Cleaning up test data...\n";
        cleanupTestData($test_run_id);
        echo "   ✓ Test data cleaned\n";
    }
    
    echo "\n=== Simulation Complete ===\n";
    echo "Test Run ID: $test_run_id\n";
    echo "New Users: {$new_users['count']}\n";
    echo "Commissions: {$commission_results['total_commissions']}\n";
    echo "Total Amount: R{$commission_results['total_amount']}\n";
    
    if ($config['payout_simulation']) {
        echo "Payouts: {$payout_results['total_payouts']}\n";
    }
    
    if ($config['generate_report']) {
        echo "Report: {$report['filename']}\n";
    }
    
    echo "\nNext steps:\n";
    echo "1. Review the generated report\n";
    echo "2. Check mock_payouts table for payout simulation results\n";
    echo "3. Verify commission calculations in the income table\n";
    echo "4. Run 'php run-simulation.php --cleanup' to clean test data\n\n";
    
} catch (Exception $e) {
    echo "Simulation failed: " . $e->getMessage() . "\n";
    ErrorHandler::logError("Simulation failed", ['error' => $e->getMessage()]);
    exit(1);
}

/**
 * Show help message
 */
function showHelp() {
    echo "MLM Simulation Test Runner\n\n";
    echo "Usage: php run-simulation.php [options]\n\n";
    echo "Options:\n";
    echo "  --test-run=ID        Use specific test run ID\n";
    echo "  --registrations=N    Number of new registrations to simulate (default: 10)\n";
    echo "  --no-payouts         Skip payout simulation\n";
    echo "  --cleanup            Clean up test data after simulation\n";
    echo "  --help               Show this help message\n\n";
    echo "Examples:\n";
    echo "  php run-simulation.php --registrations=20\n";
    echo "  php run-simulation.php --test-run=5 --no-payouts\n";
    echo "  php run-simulation.php --cleanup\n\n";
}

/**
 * Initialize test run
 */
function initializeTestRun($config) {
    if ($config['test_run_id']) {
        // Use existing test run
        $test_run = SecureDatabase::getRow(
            "SELECT * FROM test_runs WHERE id = ?",
            [$config['test_run_id']],
            'i'
        );
        
        if (!$test_run) {
            throw new Exception("Test run {$config['test_run_id']} not found");
        }
        
        return $config['test_run_id'];
    } else {
        // Get latest test run or create new one
        $latest_run = SecureDatabase::getRow(
            "SELECT id FROM test_runs ORDER BY id DESC LIMIT 1"
        );
        
        if ($latest_run) {
            return $latest_run['id'];
        } else {
            // Create new test run
            return SecureDatabase::insert(
                "INSERT INTO test_runs (run_name, description, status) VALUES (?, ?, 'running')",
                ["Simulation Run", "Automated simulation test"]
            );
        }
    }
}

/**
 * Verify test data exists
 */
function verifyTestData() {
    $user_count = SecureDatabase::getValue(
        "SELECT COUNT(*) FROM user WHERE email LIKE '%@test.com'"
    );
    
    if ($user_count < 10) {
        throw new Exception("Insufficient test data. Run 'php test-seeder.php' first.");
    }
    
    return $user_count;
}

/**
 * Simulate new registrations
 */
function simulateNewRegistrations($count, $test_run_id) {
    $new_users = [];
    $created_count = 0;
    
    // Get available sponsors (users with available matrix positions)
    $sponsors = SecureDatabase::getRows(
        "SELECT u.email, t.position1, t.position2, t.position3, t.position4, t.position5 
         FROM user u 
         JOIN tree t ON u.email = t.userid 
         WHERE u.email LIKE '%@test.com' 
         AND (t.position1 IS NULL OR t.position2 IS NULL OR t.position3 IS NULL OR t.position4 IS NULL OR t.position5 IS NULL)
         LIMIT 20"
    );
    
    if (empty($sponsors)) {
        throw new Exception("No available sponsors found for new registrations");
    }
    
    for ($i = 1; $i <= $count; $i++) {
        $sponsor = $sponsors[array_rand($sponsors)];
        
        // Find available position
        $available_position = null;
        for ($pos = 1; $pos <= 5; $pos++) {
            if (empty($sponsor["position$pos"])) {
                $available_position = $pos;
                break;
            }
        }
        
        if (!$available_position) {
            continue; // Skip if no position available
        }
        
        $new_user_email = "newuser{$i}_" . time() . "@test.com";
        $password = password_hash('TestUser123!', PASSWORD_DEFAULT);
        $join_date = date('Y-m-d');
        
        try {
            // Create user
            $user_id = SecureDatabase::insert(
                "INSERT INTO user (email, password, mobile, address, account, under_userid, matrix_position, join_date) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
                [
                    $new_user_email,
                    $password,
                    '+1' . str_pad($i + 9000, 9, '0', STR_PAD_LEFT),
                    "New Test Address $i",
                    str_pad($i + 9000, 10, '0', STR_PAD_LEFT),
                    $sponsor['email'],
                    $available_position,
                    $join_date
                ],
                'ssssssss'
            );
            
            if ($user_id) {
                // Create tree entry
                SecureDatabase::insert(
                    "INSERT INTO tree (userid, sponsor_id, level, matrix_count) VALUES (?, ?, 1, 0)",
                    [$new_user_email, $sponsor['email']]
                );
                
                // Update sponsor's matrix position
                $position_field = "position" . $available_position;
                SecureDatabase::update(
                    "UPDATE tree SET `$position_field` = ? WHERE userid = ?",
                    [$new_user_email, $sponsor['email']]
                );
                
                // Create income entry
                SecureDatabase::insert(
                    "INSERT INTO income (userid, matrix_level) VALUES (?, 1)",
                    [$new_user_email]
                );
                
                $new_users[] = [
                    'email' => $new_user_email,
                    'sponsor' => $sponsor['email'],
                    'position' => $available_position
                ];
                
                $created_count++;
            }
        } catch (Exception $e) {
            echo "   Warning: Failed to create user $new_user_email: " . $e->getMessage() . "\n";
        }
    }
    
    return [
        'count' => $created_count,
        'users' => $new_users
    ];
}

/**
 * Process commissions for new users
 */
function processCommissions($new_users, $test_run_id) {
    $total_commissions = 0;
    $total_amount = 0;

    foreach ($new_users as $user) {
        try {
            // Initialize commission processor with mock payments
            $mockPayouts = new MockStitchPayouts($GLOBALS['con'], $test_run_id);
            $commissionProcessor = new CommissionProcessor($GLOBALS['con'], $mockPayouts);

            // Process commissions for this new member
            $result = $commissionProcessor->processNewMemberCommissions(
                $user['email'],
                $user['sponsor']
            );

            if ($result['success']) {
                $total_commissions += count($result['commissions']);
                foreach ($result['commissions'] as $commission) {
                    $total_amount += $commission['amount'];
                }
            }

        } catch (Exception $e) {
            echo "   Warning: Commission processing failed for {$user['email']}: " . $e->getMessage() . "\n";
        }
    }

    return [
        'total_commissions' => $total_commissions,
        'total_amount' => $total_amount
    ];
}

/**
 * Simulate payouts
 */
function simulatePayouts($test_run_id) {
    $mockPayouts = new MockStitchPayouts($GLOBALS['con'], $test_run_id);

    // Get pending commissions
    $pending_commissions = SecureDatabase::getRows(
        "SELECT userid, SUM(amount) as total_amount
         FROM income_received
         WHERE status = 'pending'
         GROUP BY userid
         HAVING total_amount > 0"
    );

    if (empty($pending_commissions)) {
        return [
            'total_payouts' => 0,
            'successful' => 0,
            'failed' => 0,
            'total_amount' => 0
        ];
    }

    // Prepare payout data
    $payouts = [];
    foreach ($pending_commissions as $commission) {
        $payouts[] = [
            'user_id' => $commission['userid'],
            'amount' => $commission['total_amount'],
            'commission_type' => 'matrix'
        ];
    }

    // Process payouts through mock service
    $result = $mockPayouts->processMLMPayouts($payouts);

    // Update commission statuses based on mock results
    foreach ($result['payouts'] as $payout) {
        $status = $payout['status'] === 'success' ? 'paid' : 'failed';

        SecureDatabase::update(
            "UPDATE income_received SET status = ?, paid_date = ? WHERE userid = ? AND status = 'pending'",
            [$status, date('Y-m-d H:i:s'), $payout['user_id']]
        );
    }

    return [
        'total_payouts' => $result['total_payouts'],
        'successful' => $result['successful_payouts'],
        'failed' => $result['failed_payouts'],
        'total_amount' => $result['total_amount']
    ];
}

/**
 * Generate verification report
 */
function generateVerificationReport($test_run_id) {
    $report_filename = "reports/simulation_report_" . $test_run_id . "_" . date('Y-m-d_H-i-s') . ".html";

    // Ensure reports directory exists
    if (!is_dir('reports')) {
        mkdir('reports', 0755, true);
    }

    // Gather report data
    $test_run = SecureDatabase::getRow(
        "SELECT * FROM test_runs WHERE id = ?",
        [$test_run_id],
        'i'
    );

    $user_stats = SecureDatabase::getRow(
        "SELECT COUNT(*) as total_users,
                COUNT(CASE WHEN email LIKE 'newuser%' THEN 1 END) as new_users
         FROM user WHERE email LIKE '%@test.com'"
    );

    $commission_stats = SecureDatabase::getRow(
        "SELECT COUNT(*) as total_commissions,
                SUM(amount) as total_amount,
                AVG(amount) as average_amount
         FROM income_received"
    );

    $payout_stats = SecureDatabase::getRow(
        "SELECT COUNT(*) as total_payouts,
                SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as successful_payouts,
                SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_payouts,
                SUM(amount) as total_amount
         FROM mock_payouts WHERE test_run_id = ?",
        [$test_run_id],
        'i'
    );

    $matrix_analysis = SecureDatabase::getRows(
        "SELECT level, COUNT(*) as user_count,
                AVG(total_downline) as avg_downline
         FROM test_matrix_snapshots
         WHERE test_run_id = ?
         GROUP BY level
         ORDER BY level",
        [$test_run_id],
        'i'
    );

    // Generate HTML report
    $html = generateReportHTML($test_run, $user_stats, $commission_stats, $payout_stats, $matrix_analysis);

    file_put_contents($report_filename, $html);

    return [
        'filename' => $report_filename,
        'test_run' => $test_run,
        'stats' => [
            'users' => $user_stats,
            'commissions' => $commission_stats,
            'payouts' => $payout_stats
        ]
    ];
}

/**
 * Generate HTML report
 */
function generateReportHTML($test_run, $user_stats, $commission_stats, $payout_stats, $matrix_analysis) {
    $html = "<!DOCTYPE html>
<html>
<head>
    <title>MLM Simulation Report - Test Run {$test_run['id']}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #4CAF50; color: white; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .stats { display: flex; gap: 20px; }
        .stat-box { flex: 1; background: #f5f5f5; padding: 15px; border-radius: 5px; text-align: center; }
        .stat-number { font-size: 24px; font-weight: bold; color: #4CAF50; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f2f2f2; }
        .success { color: #4CAF50; }
        .failed { color: #f44336; }
    </style>
</head>
<body>
    <div class='header'>
        <h1>MLM Simulation Report</h1>
        <p>Test Run ID: {$test_run['id']} | Generated: " . date('Y-m-d H:i:s') . "</p>
    </div>

    <div class='section'>
        <h2>Test Run Summary</h2>
        <p><strong>Description:</strong> {$test_run['description']}</p>
        <p><strong>Started:</strong> {$test_run['started_at']}</p>
        <p><strong>Status:</strong> {$test_run['status']}</p>
    </div>

    <div class='section'>
        <h2>User Statistics</h2>
        <div class='stats'>
            <div class='stat-box'>
                <div class='stat-number'>{$user_stats['total_users']}</div>
                <div>Total Users</div>
            </div>
            <div class='stat-box'>
                <div class='stat-number'>{$user_stats['new_users']}</div>
                <div>New Users</div>
            </div>
        </div>
    </div>

    <div class='section'>
        <h2>Commission Analysis</h2>
        <div class='stats'>
            <div class='stat-box'>
                <div class='stat-number'>{$commission_stats['total_commissions']}</div>
                <div>Total Commissions</div>
            </div>
            <div class='stat-box'>
                <div class='stat-number'>R" . number_format($commission_stats['total_amount'], 2) . "</div>
                <div>Total Amount</div>
            </div>
            <div class='stat-box'>
                <div class='stat-number'>R" . number_format($commission_stats['average_amount'], 2) . "</div>
                <div>Average Commission</div>
            </div>
        </div>
    </div>";

    if ($payout_stats) {
        $html .= "
    <div class='section'>
        <h2>Payout Simulation Results</h2>
        <div class='stats'>
            <div class='stat-box'>
                <div class='stat-number'>{$payout_stats['total_payouts']}</div>
                <div>Total Payouts</div>
            </div>
            <div class='stat-box'>
                <div class='stat-number success'>{$payout_stats['successful_payouts']}</div>
                <div>Successful</div>
            </div>
            <div class='stat-box'>
                <div class='stat-number failed'>{$payout_stats['failed_payouts']}</div>
                <div>Failed</div>
            </div>
            <div class='stat-box'>
                <div class='stat-number'>R" . number_format($payout_stats['total_amount'], 2) . "</div>
                <div>Total Amount</div>
            </div>
        </div>
    </div>";
    }

    if (!empty($matrix_analysis)) {
        $html .= "
    <div class='section'>
        <h2>Matrix Level Analysis</h2>
        <table>
            <tr>
                <th>Level</th>
                <th>User Count</th>
                <th>Average Downline</th>
            </tr>";

        foreach ($matrix_analysis as $level) {
            $html .= "
            <tr>
                <td>{$level['level']}</td>
                <td>{$level['user_count']}</td>
                <td>" . number_format($level['avg_downline'], 1) . "</td>
            </tr>";
        }

        $html .= "
        </table>
    </div>";
    }

    $html .= "
    <div class='section'>
        <h2>Verification Notes</h2>
        <ul>
            <li>All commission calculations should follow the 5x5 matrix structure</li>
            <li>Each new member should trigger commissions for up to 5 levels of upline</li>
            <li>Payout simulation uses mock services - no real money transferred</li>
            <li>Check database tables for detailed transaction records</li>
        </ul>
    </div>

</body>
</html>";

    return $html;
}

/**
 * Clean up test data
 */
function cleanupTestData($test_run_id) {
    // This function would clean up test-specific data
    // Implementation depends on cleanup requirements
    echo "   Cleanup functionality would be implemented here\n";
}
?>
