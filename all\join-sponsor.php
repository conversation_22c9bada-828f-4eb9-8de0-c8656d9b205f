<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include core files
include('php-includes/connect.php');
include('php-includes/check-login.php');
include('php-includes/email-helper.php');
include('php-includes/whatsapp-helper.php');
include('php-includes/commission-processor.php');

// Include security enhancements
include('php-includes/password-security.php');
include('php-includes/csrf-protection.php');
include('php-includes/rate-limiter.php');
include('php-includes/error-handler.php');
include('php-includes/secure-database.php');

// Initialize security components
ErrorHandler::init($con, ['display_errors' => false]); // Set to true for development
CSRFProtection::init($con);
RateLimiter::init($con);
SecureDatabase::init($con);

$userid = $_SESSION['userid'];
$loan_amount = 2000;
$umgabelo_amount = 500;
$monthly_payment = 534;
$maintenance_fee_month1 = 900;
$maintenance_fee_regular = 2000;
$matrix_type = "5×5"; // Define the matrix type

// Function to generate OTP
function generateOTP($length = 6) {
    $characters = '0123456789';
    $otp = '';
    for ($i = 0; $i < $length; $i++) {
        $otp .= $characters[rand(0, strlen($characters) - 1)];
    }
    return $otp;
}

// Handle initial join request (Step 1)
if(isset($_POST['initiate_join'])) {
    // Validate CSRF token
    if (!CSRFProtection::validatePostToken()) {
        ErrorHandler::logWarning("CSRF token validation failed for join request", [
            'ip' => RateLimiter::getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
        echo '<script>alert("Security validation failed. Please refresh the page and try again.");</script>';
        exit;
    }

    // Get client IP for rate limiting
    $client_ip = RateLimiter::getClientIP();

    // Check rate limiting for registration attempts
    $rate_check = RateLimiter::checkLimit($client_ip, 'registration');
    if (!$rate_check['allowed']) {
        $retry_minutes = ceil($rate_check['retry_after'] / 60);
        echo '<script>alert("Too many registration attempts. Please try again in ' . $retry_minutes . ' minutes.");</script>';
        exit;
    }

    // Sanitize and validate input
    $pin = trim($_POST['pin'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $mobile = trim($_POST['mobile'] ?? '');
    $under_userid = trim($_POST['under_userid'] ?? '');
    $matrix_position = trim($_POST['matrix_position'] ?? '');

    $errors = [];

    // Validate required fields
    if(empty($pin) || empty($email) || empty($mobile) || empty($under_userid) || empty($matrix_position)) {
        $errors[] = "Please fill all the required fields.";
    }

    // Validate email format
    if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Please enter a valid email address.";
    }

    // Validate mobile number format
    if (!empty($mobile) && !preg_match('/^[0-9+\-\s()]{10,15}$/', $mobile)) {
        $errors[] = "Please enter a valid mobile number.";
    }

    if (empty($errors)) {
        // Validate pin using secure database
        if(pin_check_secure($pin)) {
            // Validate email availability
            if(email_check_secure($email)) {
                // Validate sponsor exists
                if(!email_check_secure($under_userid)) {
                    // Validate matrix position
                    if(matrix_position_check_secure($under_userid, $matrix_position)) {
                        $validation_passed = true;
                    } else {
                        $errors[] = "The matrix position you selected is not available.";
                    }
                } else {
                    $errors[] = "Invalid sponsor user ID.";
                }
            } else {
                $errors[] = "This email is already registered.";
            }
        } else {
            $errors[] = "Invalid pin.";
        }
    }

    // Record the attempt for rate limiting
    RateLimiter::recordAttempt($client_ip, 'registration');

    if (!empty($errors)) {
        $error_message = implode("\\n", $errors);
        echo '<script>alert("' . addslashes($error_message) . '");</script>';
    }

    // If all validations pass, generate and send OTP
    if (empty($errors)) {
        // Check OTP rate limiting
        $otp_rate_check = RateLimiter::checkLimit($client_ip, 'otp_request');
        if (!$otp_rate_check['allowed']) {
            $retry_minutes = ceil($otp_rate_check['retry_after'] / 60);
            echo '<script>alert("Too many OTP requests. Please try again in ' . $retry_minutes . ' minutes.");</script>';
            exit;
        }

        // Generate secure OTP
        $otp = generateSecureOTP();
        $expiry = date('Y-m-d H:i:s', strtotime('+15 minutes'));

        // Use database transaction for consistency
        $transaction_result = SecureDatabase::transaction(function() use ($email, $pin, $mobile, $under_userid, $matrix_position, $otp, $expiry, $client_ip) {
            // Check if request already exists
            $existing = SecureDatabase::getRow(
                "SELECT id FROM join_requests WHERE email = ?",
                [$email]
            );

            if ($existing) {
                // Update existing request
                $result = SecureDatabase::update(
                    "UPDATE join_requests SET pin = ?, mobile = ?, under_userid = ?, matrix_position = ?, otp = ?, otp_expiry = ?, status = 'pending', ip_address = ?, user_agent = ? WHERE email = ?",
                    [$pin, $mobile, $under_userid, $matrix_position, $otp, $expiry, $client_ip, $_SERVER['HTTP_USER_AGENT'] ?? '', $email]
                );
            } else {
                // Insert new request
                $result = SecureDatabase::insert(
                    "INSERT INTO join_requests (email, pin, mobile, under_userid, matrix_position, otp, otp_expiry, status, ip_address, user_agent) VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', ?, ?)",
                    [$email, $pin, $mobile, $under_userid, $matrix_position, $otp, $expiry, $client_ip, $_SERVER['HTTP_USER_AGENT'] ?? '']
                );
            }

            return $result !== false;
        });

        if ($transaction_result) {
            // Record OTP attempt for rate limiting
            RateLimiter::recordAttempt($client_ip, 'otp_request');

            // Send OTP via WhatsApp with SMS fallback
            if(sendOTPWithFallback($mobile, $otp)) {
                echo '<script>alert("OTP has been sent to the recruit\'s WhatsApp or mobile number. Ask them for the OTP to complete registration.");</script>';

                // Show OTP verification form
                $_SESSION['temp_email'] = $email;
                echo '<script>document.getElementById("otp-verification").style.display = "block";</script>';

                ErrorHandler::logInfo("OTP sent successfully", [
                    'email' => $email,
                    'mobile' => $mobile,
                    'ip' => $client_ip
                ]);
            } else {
                echo '<script>alert("Failed to send OTP. Please try again.");</script>';
                ErrorHandler::logError("Failed to send OTP", [
                    'email' => $email,
                    'mobile' => $mobile,
                    'ip' => $client_ip
                ]);
            }
        } else {
            echo '<script>alert("Database error occurred. Please try again.");</script>';
            ErrorHandler::logError("Failed to store OTP request", [
                'email' => $email,
                'ip' => $client_ip
            ]);
        }
    }
}

// Handle OTP verification (Step 2)
if(isset($_POST['verify_otp'])) {
    // Validate CSRF token
    if (!CSRFProtection::validatePostToken()) {
        ErrorHandler::logWarning("CSRF token validation failed for OTP verification", [
            'ip' => RateLimiter::getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
        echo '<script>alert("Security validation failed. Please refresh the page and try again.");</script>';
        exit;
    }

    $client_ip = RateLimiter::getClientIP();

    // Check rate limiting for OTP verification
    $rate_check = RateLimiter::checkLimit($client_ip, 'otp_verify');
    if (!$rate_check['allowed']) {
        $retry_minutes = ceil($rate_check['retry_after'] / 60);
        echo '<script>alert("Too many OTP verification attempts. Please try again in ' . $retry_minutes . ' minutes.");</script>';
        exit;
    }

    $email = $_SESSION['temp_email'] ?? '';
    $otp = trim($_POST['otp'] ?? '');

    if(!empty($email) && !empty($otp)) {
        // Record verification attempt for rate limiting
        RateLimiter::recordAttempt($client_ip, 'otp_verify');

        // Verify OTP using secure database
        $request = SecureDatabase::getRow(
            "SELECT * FROM join_requests WHERE email = ? AND otp = ? AND status = 'pending'",
            [$email, $otp]
        );

        if($request) {
            $otp_expiry = strtotime($request['otp_expiry']);
            $current_time = time();

            if($current_time <= $otp_expiry) {
                // OTP is valid, proceed with registration using database transaction
                $registration_result = SecureDatabase::transaction(function() use ($email, $request, $loan_amount, $monthly_payment, $maintenance_fee_month1) {
                    $today = date("Y-m-d");

                    // Generate secure temporary password
                    $temp_password = PasswordSecurity::generateSecurePassword(12);
                    $hashed_password = PasswordSecurity::hashPassword($temp_password);

                    // Update join request status
                    $update_result = SecureDatabase::update(
                        "UPDATE join_requests SET status = 'verified' WHERE email = ?",
                        [$email]
                    );

                    if ($update_result === false) return false;

                    // Insert into User profile with minimal information
                    $user_id = SecureDatabase::insert(
                        "INSERT INTO user (email, password, mobile, under_userid, matrix_position, join_date) VALUES (?, ?, ?, ?, ?, ?)",
                        [$email, $hashed_password, $request['mobile'], $request['under_userid'], $request['matrix_position'], $today],
                        'ssssss'
                    );

                    if ($user_id === false) return false;

                    // Insert into Tree
                    $tree_id = SecureDatabase::insert(
                        "INSERT INTO tree (userid, sponsor_id) VALUES (?, ?)",
                        [$email, $request['under_userid']]
                    );

                    if ($tree_id === false) return false;

                    // Update matrix position
                    $position_field = "position" . $request['matrix_position'];
                    $matrix_update = SecureDatabase::update(
                        "UPDATE tree SET `$position_field` = ? WHERE userid = ?",
                        [$email, $request['under_userid']]
                    );

                    if ($matrix_update === false) return false;

                    // Update pin status to close
                    $pin_update = SecureDatabase::update(
                        "UPDATE pin_list SET status = 'close' WHERE pin = ?",
                        [$request['pin']]
                    );

                    if ($pin_update === false) return false;

                    // Insert into Income
                    $income_id = SecureDatabase::insert(
                        "INSERT INTO income (userid) VALUES (?)",
                        [$email]
                    );

                    if ($income_id === false) return false;

                    // Insert into Loan table
                    $loan_id = SecureDatabase::insert(
                        "INSERT INTO loan (userid, loan_amount, remaining_amount, monthly_payment, start_date) VALUES (?, ?, ?, ?, ?)",
                        [$email, $loan_amount, $loan_amount, $monthly_payment, $today],
                        'siiss'
                    );

                    if ($loan_id === false) return false;

                    // Insert first month maintenance fee
                    $maintenance_id = SecureDatabase::insert(
                        "INSERT INTO maintenance_fee (userid, month, fee_amount, date_paid, status) VALUES (?, ?, ?, ?, 'paid')",
                        [$email, 1, $maintenance_fee_month1, $today],
                        'siis'
                    );

                    if ($maintenance_id === false) return false;

                    return ['temp_password' => $temp_password];
                });

                if ($registration_result !== false) {
                    // Update matrix count and calculate commissions
                    updateMatrixCount($request['under_userid'], $email);

                    // Process commissions for upline using new commission processor
                    $commissionProcessor = new CommissionProcessor($con);
                    $commissionResult = $commissionProcessor->processNewMemberCommissions($email, $request['under_userid']);

                    // Generate a unique token for the completion link with expiry
                    $completion_token = bin2hex(random_bytes(32));
                    $completion_expiry = date('Y-m-d H:i:s', strtotime('+24 hours'));

                    SecureDatabase::update(
                        "UPDATE join_requests SET completion_token = ?, completion_token_expiry = ? WHERE email = ?",
                        [$completion_token, $completion_expiry, $email]
                    );

                    // Send completion link via email using PHPMailer
                    sendRegistrationEmail($email, $completion_token);

                    echo '<script>alert("Registration initiated successfully! An email has been sent to the recruit with instructions to complete their registration. Loan of R' . $loan_amount . ' advanced.");</script>';

                    // Clear the temporary session variable
                    unset($_SESSION['temp_email']);

                    // Reset rate limits for successful registration
                    RateLimiter::resetLimit($client_ip, 'otp_verify');
                    RateLimiter::resetLimit($client_ip, 'registration');

                    // Log successful registration
                    ErrorHandler::logInfo("User registration completed successfully", [
                        'email' => $email,
                        'sponsor' => $request['under_userid'],
                        'ip' => $client_ip
                    ]);

                    // Redirect to prevent form resubmission
                    echo '<script>window.location.href = "join-sponsor.php?success=1";</script>';
                } else {
                    echo '<script>alert("Registration failed due to database error. Please try again.");</script>';
                    ErrorHandler::logError("Registration transaction failed", [
                        'email' => $email,
                        'ip' => $client_ip
                    ]);
                }
            } else {
                echo '<script>alert("OTP has expired. Please initiate the registration again.");</script>';
                ErrorHandler::logWarning("Expired OTP verification attempt", [
                    'email' => $email,
                    'ip' => $client_ip
                ]);
            }
        } else {
            echo '<script>alert("Invalid OTP. Please try again.");</script>';
            ErrorHandler::logWarning("Invalid OTP verification attempt", [
                'email' => $email,
                'otp' => $otp,
                'ip' => $client_ip
            ]);
        }
    } else {
        echo '<script>alert("Please enter the OTP.");</script>';
    }
}

// Enhanced secure functions
function generateSecureOTP($length = 6) {
    // Generate cryptographically secure OTP
    $otp = '';
    for ($i = 0; $i < $length; $i++) {
        $otp .= random_int(0, 9);
    }
    return $otp;
}

// Secure functions using prepared statements
function pin_check_secure($pin){
    global $userid;

    $result = SecureDatabase::getRow(
        "SELECT id FROM pin_list WHERE pin = ? AND userid = ? AND status = 'open'",
        [$pin, $userid]
    );

    return $result !== null;
}

function email_check_secure($email){
    $result = SecureDatabase::getRow(
        "SELECT id FROM user WHERE email = ?",
        [$email]
    );

    return $result === null; // Return true if email is available (not found)
}

function matrix_position_check_secure($email, $position){
    $position_field = "position" . $position;

    $result = SecureDatabase::getRow(
        "SELECT `$position_field` FROM tree WHERE userid = ?",
        [$email]
    );

    if (!$result) {
        return false; // User not found in tree
    }

    $position_value = $result[$position_field];
    return empty($position_value); // Return true if position is available
}

// Legacy functions for backward compatibility (deprecated - use secure versions)
function pin_check($pin){
    global $userid;
    return pin_check_secure($pin);
}

function email_check($email){
    return email_check_secure($email);
}

function matrix_position_check($email, $position){
    return matrix_position_check_secure($email, $position);
}

function income($userid){
    global $con;
    $data = array();
    $query = mysqli_query($con,"select * from income where userid='$userid'");
    $result = mysqli_fetch_array($query);
    $data['month_bal'] = $result['month_bal'];
    $data['current_bal'] = $result['current_bal'];
    $data['total_bal'] = $result['total_bal'];
    $data['matrix_level'] = $result['matrix_level'];
    $data['matrix_earnings'] = $result['matrix_earnings'];

    return $data;
}

function tree($userid){
    global $con;
    $data = array();
    $query = mysqli_query($con,"select * from tree where userid='$userid'");
    $result = mysqli_fetch_array($query);
    $data['position1'] = $result['position1'];
    $data['position2'] = $result['position2'];
    $data['position3'] = $result['position3'];
    $data['position4'] = $result['position4'];
    $data['position5'] = $result['position5'];
    $data['level'] = $result['level'];
    $data['matrix_count'] = $result['matrix_count'];
    $data['sponsor_id'] = $result['sponsor_id'];

    return $data;
}

function updateMatrixCount($sponsor_id, $new_user_id) {
    global $con;

    // Get the current matrix count for the sponsor
    $query = mysqli_query($con, "SELECT matrix_count FROM tree WHERE userid='$sponsor_id'");
    $result = mysqli_fetch_array($query);
    $current_count = $result['matrix_count'];

    // Increment the matrix count
    $new_count = $current_count + 1;
    mysqli_query($con, "UPDATE tree SET matrix_count='$new_count' WHERE userid='$sponsor_id'");

    // Check if we need to update the level and calculate commissions
    calculateMatrixLevel($sponsor_id);

    // Recursively update upline
    $upline_sponsor = getSponsorId($sponsor_id);
    if($upline_sponsor != "" && $upline_sponsor != NULL) {
        updateMatrixCount($upline_sponsor, $sponsor_id);
    }
}

function getSponsorId($userid) {
    global $con;
    $query = mysqli_query($con, "SELECT sponsor_id FROM tree WHERE userid='$userid'");
    $result = mysqli_fetch_array($query);
    return $result['sponsor_id'];
}

function calculateMatrixLevel($userid) {
    global $con;

    // Get current matrix count
    $query = mysqli_query($con, "SELECT matrix_count FROM tree WHERE userid='$userid'");
    $result = mysqli_fetch_array($query);
    $count = $result['matrix_count'];

    // Calculate level based on matrix count for 5×5 matrix
    $level = 0;
    if($count >= 5) { // Level 1: 5 direct recruits
        $level = 1;
    }
    if($count >= 30) { // Level 2: 5 + 25 = 30 total in network
        $level = 2;
    }
    if($count >= 155) { // Level 3: 5 + 25 + 125 = 155 total
        $level = 3;
    }
    if($count >= 780) { // Level 4: 5 + 25 + 125 + 625 = 780 total
        $level = 4;
    }
    if($count >= 3905) { // Level 5: 5 + 25 + 125 + 625 + 3125 = 3905 total
        $level = 5;
    }

    // Update level in tree table
    mysqli_query($con, "UPDATE tree SET level='$level' WHERE userid='$userid'");

    // Get current income data
    $income_data = income($userid);
    $current_matrix_level = $income_data['matrix_level'];

    // If level increased, calculate and add commission
    if($level > $current_matrix_level) {
        // Get commission amount for this level
        $query = mysqli_query($con, "SELECT commission_amount FROM matrix_commission WHERE level='$level'");
        $result = mysqli_fetch_array($query);
        $commission = $result['commission_amount'];

        // Update income with new commission
        $new_matrix_earnings = $income_data['matrix_earnings'] + $commission;
        $new_month_bal = $income_data['month_bal'] + $commission;
        $new_current_bal = $income_data['current_bal'] + $commission;
        $new_total_bal = $income_data['total_bal'] + $commission;

        mysqli_query($con, "UPDATE income SET
            matrix_level='$level',
            matrix_earnings='$new_matrix_earnings',
            month_bal='$new_month_bal',
            current_bal='$new_current_bal',
            total_bal='$new_total_bal'
            WHERE userid='$userid'");

        // Record the commission payment
        $today = date("Y-m-d");
        mysqli_query($con, "INSERT INTO income_received (userid, amount, date)
            VALUES ('$userid', '$commission', '$today')");
    }
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>MLM Website - Sponsor Registration</title>

    <!-- Bootstrap Core CSS -->
    <link href="vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">

    <!-- MetisMenu CSS -->
    <link href="vendor/metisMenu/metisMenu.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="dist/css/sb-admin-2.css" rel="stylesheet">

    <!-- Custom Fonts -->
    <link href="vendor/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css">
</head>

<body>
    <div id="wrapper">
        <!-- Navigation -->
        <?php include('php-includes/menu.php'); ?>

        <!-- Page Content -->
        <div id="page-wrapper">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-lg-12">
                        <h1 class="page-header">Sponsor Registration</h1>
                    </div>
                    <!-- /.col-lg-12 -->
                </div>
                <!-- /.row -->

                <?php if(isset($_GET['success']) && $_GET['success'] == 1): ?>
                <div class="alert alert-success">
                    <strong>Success!</strong> Registration initiated successfully. The recruit will receive an email with instructions to complete their registration.
                </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-lg-8">
                        <div class="panel panel-primary">
                            <div class="panel-heading">
                                <h4><i class="fa fa-user-plus"></i> Step 1: Initiate Registration</h4>
                            </div>
                            <div class="panel-body">
                                <p class="alert alert-info">
                                    <i class="fa fa-info-circle"></i> Enter the recruit's basic information to initiate the registration process. An OTP will be sent to their mobile number for verification.
                                </p>

                                <form method="post" id="initiate-form">
                                    <?php echo CSRFProtection::getTokenField(); ?>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Pin</label>
                                                <input type="text" name="pin" class="form-control" required>
                                                <p class="help-block">Enter your registration pin</p>
                                            </div>

                                            <div class="form-group">
                                                <label>Recruit's Email</label>
                                                <input type="email" name="email" class="form-control" required>
                                                <p class="help-block">This will be their login username</p>
                                            </div>

                                            <div class="form-group">
                                                <label>Recruit's Mobile Number</label>
                                                <input type="text" name="mobile" class="form-control" required>
                                                <p class="help-block">OTP will be sent to this number via WhatsApp or SMS</p>
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Sponsor User ID</label>
                                                <input type="text" name="under_userid" class="form-control" value="<?php echo SecureDatabase::escapeOutput($userid); ?>" readonly>
                                                <p class="help-block">Your user ID as the sponsor</p>
                                            </div>

                                            <div class="form-group">
                                                <label>Matrix Position (1-5)</label>
                                                <select name="matrix_position" class="form-control" required>
                                                    <option value="">Select Position</option>
                                                    <?php
                                                    // Get available positions
                                                    $tree_data = tree($userid);
                                                    for($i = 1; $i <= 5; $i++) {
                                                        $position_field = "position" . $i;
                                                        $position_value = $tree_data[$position_field];
                                                        if($position_value == '' || $position_value == NULL) {
                                                            echo "<option value=\"$i\">Position $i (Available)</option>";
                                                        } else {
                                                            echo "<option value=\"$i\" disabled>Position $i (Taken)</option>";
                                                        }
                                                    }
                                                    ?>
                                                </select>
                                                <p class="help-block">Select an available position in your matrix</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <button type="submit" name="initiate_join" class="btn btn-primary btn-lg">
                                            <i class="fa fa-paper-plane"></i> Send OTP to Recruit
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h4><i class="fa fa-info-circle"></i> Registration Process</h4>
                            </div>
                            <div class="panel-body">
                                <ol>
                                    <li><strong>Sponsor Initiates:</strong> Enter recruit's email and mobile</li>
                                    <li><strong>OTP Verification:</strong> Recruit receives OTP via WhatsApp or SMS</li>
                                    <li><strong>Verify OTP:</strong> Enter the OTP provided by the recruit</li>
                                    <li><strong>Recruit Completes:</strong> Recruit receives email to complete registration</li>
                                    <li><strong>Account Setup:</strong> Recruit enters personal and payment details</li>
                                </ol>

                                <div class="alert alert-warning">
                                    <strong>Important!</strong> Make sure the recruit provides their consent by sharing the OTP with you. This ensures they are aware of and agree to join the platform.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- OTP Verification Form (initially hidden) -->
                <div class="row" id="otp-verification" style="display: <?php echo isset($_SESSION['temp_email']) ? 'block' : 'none'; ?>;">
                    <div class="col-lg-6">
                        <div class="panel panel-success">
                            <div class="panel-heading">
                                <h4><i class="fa fa-check-circle"></i> Step 2: Verify OTP</h4>
                            </div>
                            <div class="panel-body">
                                <p class="alert alert-info">
                                    <i class="fa fa-info-circle"></i> Ask the recruit for the OTP sent to their WhatsApp or mobile number and enter it below.
                                </p>

                                <form method="post">
                                    <?php echo CSRFProtection::getTokenField(); ?>
                                    <div class="form-group">
                                        <label>Enter OTP</label>
                                        <input type="text" name="otp" class="form-control" placeholder="Enter 6-digit OTP" required>
                                    </div>

                                    <div class="form-group">
                                        <button type="submit" name="verify_otp" class="btn btn-success">
                                            <i class="fa fa-check"></i> Verify OTP & Complete Registration
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- /.container-fluid -->
        </div>
        <!-- /#page-wrapper -->
    </div>
    <!-- /#wrapper -->

    <!-- jQuery -->
    <script src="vendor/jquery/jquery.min.js"></script>

    <!-- Bootstrap Core JavaScript -->
    <script src="vendor/bootstrap/js/bootstrap.min.js"></script>

    <!-- Metis Menu Plugin JavaScript -->
    <script src="vendor/metisMenu/metisMenu.min.js"></script>

    <!-- Custom Theme JavaScript -->
    <script src="dist/js/sb-admin-2.js"></script>
</body>
</html>
