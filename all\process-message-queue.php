<?php
/**
 * Message Queue Processor
 * Run this script as a cron job to process queued messages
 * 
 * Example cron job (runs every minute):
 * * * * * * /usr/bin/php /path/to/your/project/process-message-queue.php
 */

// Prevent web access
if (isset($_SERVER['HTTP_HOST'])) {
    die('This script can only be run from command line.');
}

// Include required files
require_once __DIR__ . '/php-includes/connect.php';
require_once __DIR__ . '/php-includes/error-handler.php';
require_once __DIR__ . '/php-includes/secure-database.php';
require_once __DIR__ . '/php-includes/message-queue.php';
require_once __DIR__ . '/php-includes/email-helper.php';
require_once __DIR__ . '/php-includes/whatsapp-helper.php';

// Initialize components
ErrorHandler::init($con, ['display_errors' => true, 'log_to_file' => true]);
SecureDatabase::init($con);
MessageQueue::init($con);

// Configuration
$max_messages_per_run = 50;
$max_execution_time = 300; // 5 minutes
$start_time = time();

echo "Message Queue Processor started at " . date('Y-m-d H:i:s') . "\n";

try {
    // Get pending messages
    $messages = MessageQueue::getPendingMessages($max_messages_per_run);
    
    if (empty($messages)) {
        echo "No pending messages to process.\n";
        exit(0);
    }
    
    echo "Processing " . count($messages) . " messages...\n";
    
    $processed = 0;
    $sent = 0;
    $failed = 0;
    
    foreach ($messages as $message) {
        // Check execution time limit
        if (time() - $start_time > $max_execution_time) {
            echo "Execution time limit reached. Stopping processing.\n";
            break;
        }
        
        // Mark as processing
        if (!MessageQueue::markAsProcessing($message['id'])) {
            echo "Failed to mark message {$message['id']} as processing.\n";
            continue;
        }
        
        $success = false;
        $error_message = '';
        
        try {
            switch ($message['type']) {
                case 'email':
                    $success = processEmail($message);
                    break;
                    
                case 'sms':
                    $success = processSMS($message);
                    break;
                    
                case 'whatsapp':
                    $success = processWhatsApp($message);
                    break;
                    
                default:
                    $error_message = "Unknown message type: {$message['type']}";
                    break;
            }
        } catch (Exception $e) {
            $error_message = $e->getMessage();
            ErrorHandler::logError("Message processing failed", [
                'message_id' => $message['id'],
                'type' => $message['type'],
                'error' => $error_message
            ]);
        }
        
        if ($success) {
            MessageQueue::markAsSent($message['id']);
            $sent++;
            echo "✓ Sent {$message['type']} to {$message['recipient']}\n";
        } else {
            MessageQueue::markAsFailed($message['id'], $error_message);
            $failed++;
            echo "✗ Failed to send {$message['type']} to {$message['recipient']}: $error_message\n";
        }
        
        $processed++;
        
        // Small delay to prevent overwhelming external services
        usleep(100000); // 0.1 seconds
    }
    
    echo "\nProcessing completed:\n";
    echo "- Processed: $processed\n";
    echo "- Sent: $sent\n";
    echo "- Failed: $failed\n";
    
    // Cleanup old messages (run occasionally)
    if (rand(1, 100) <= 5) { // 5% chance
        $cleaned = MessageQueue::cleanup(30);
        if ($cleaned > 0) {
            echo "- Cleaned up: $cleaned old messages\n";
        }
    }
    
} catch (Exception $e) {
    ErrorHandler::logError("Queue processor error", ['error' => $e->getMessage()]);
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}

echo "Message Queue Processor finished at " . date('Y-m-d H:i:s') . "\n";

/**
 * Process an email message
 * 
 * @param array $message Message data
 * @return bool True if sent successfully
 */
function processEmail($message) {
    try {
        return sendEmail(
            $message['recipient'],
            $message['subject'],
            $message['message']
        );
    } catch (Exception $e) {
        ErrorHandler::logError("Email sending failed", [
            'recipient' => $message['recipient'],
            'subject' => $message['subject'],
            'error' => $e->getMessage()
        ]);
        return false;
    }
}

/**
 * Process an SMS message
 * 
 * @param array $message Message data
 * @return bool True if sent successfully
 */
function processSMS($message) {
    try {
        // Implement SMS sending logic here
        // This is a placeholder - replace with actual SMS service integration
        
        // For now, just log the SMS (replace with actual SMS API call)
        ErrorHandler::logInfo("SMS would be sent", [
            'recipient' => $message['recipient'],
            'message' => $message['message']
        ]);
        
        return true; // Return false if SMS sending fails
    } catch (Exception $e) {
        ErrorHandler::logError("SMS sending failed", [
            'recipient' => $message['recipient'],
            'message' => $message['message'],
            'error' => $e->getMessage()
        ]);
        return false;
    }
}

/**
 * Process a WhatsApp message
 * 
 * @param array $message Message data
 * @return bool True if sent successfully
 */
function processWhatsApp($message) {
    try {
        // Use existing WhatsApp helper function
        return sendWhatsAppMessage($message['recipient'], $message['message']);
    } catch (Exception $e) {
        ErrorHandler::logError("WhatsApp sending failed", [
            'recipient' => $message['recipient'],
            'message' => $message['message'],
            'error' => $e->getMessage()
        ]);
        return false;
    }
}
?>
