<?php
/**
 * Enhanced Security Setup Script
 * Run this script to apply all security enhancements to the database
 */

// Prevent web access
if (isset($_SERVER['HTTP_HOST'])) {
    die('This script can only be run from command line or by direct access.');
}

// Include required files
require_once __DIR__ . '/php-includes/connect.php';
require_once __DIR__ . '/php-includes/config.php';
require_once __DIR__ . '/php-includes/error-handler.php';
require_once __DIR__ . '/php-includes/secure-database.php';
require_once __DIR__ . '/php-includes/password-security.php';

// Initialize components
ErrorHandler::init($con, ['display_errors' => true, 'log_to_file' => true]);
SecureDatabase::init($con);

echo "=== MLM System Enhanced Security Setup ===\n";
echo "Starting database migration...\n\n";

try {
    // Read and execute the security migration SQL
    $migration_file = __DIR__ . '/security-migration.sql';
    
    if (!file_exists($migration_file)) {
        throw new Exception("Migration file not found: $migration_file");
    }
    
    $sql_content = file_get_contents($migration_file);
    $statements = explode(';', $sql_content);
    
    $executed = 0;
    $errors = 0;
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        try {
            $con->query($statement);
            $executed++;
            echo "✓ Executed: " . substr($statement, 0, 50) . "...\n";
        } catch (Exception $e) {
            $errors++;
            echo "✗ Error: " . $e->getMessage() . "\n";
            echo "  Statement: " . substr($statement, 0, 100) . "...\n";
        }
    }
    
    echo "\nMigration completed:\n";
    echo "- Executed: $executed statements\n";
    echo "- Errors: $errors statements\n\n";
    
    // Update existing user passwords to use secure hashing
    echo "Updating existing user passwords...\n";
    updateExistingPasswords();
    
    // Create default admin user if not exists
    echo "Setting up admin user...\n";
    setupAdminUser();
    
    // Create sample data for testing
    if (Config::isDevelopment()) {
        echo "Creating sample data for development...\n";
        createSampleData();
    }
    
    echo "\n=== Setup completed successfully! ===\n";
    echo "Next steps:\n";
    echo "1. Copy .env.example to .env and configure your settings\n";
    echo "2. Set up a cron job to run process-message-queue.php every minute\n";
    echo "3. Test the registration process\n";
    echo "4. Review the error logs in logs/app.log\n\n";
    
} catch (Exception $e) {
    echo "Setup failed: " . $e->getMessage() . "\n";
    ErrorHandler::logError("Setup script failed", ['error' => $e->getMessage()]);
    exit(1);
}

/**
 * Update existing user passwords to use secure hashing
 */
function updateExistingPasswords() {
    global $con;
    
    // Get all users with plain text passwords
    $users = SecureDatabase::getRows("SELECT id, email, password FROM user WHERE LENGTH(password) < 60");
    
    $updated = 0;
    foreach ($users as $user) {
        // Hash the existing password
        $hashed_password = PasswordSecurity::hashPassword($user['password']);
        
        $result = SecureDatabase::update(
            "UPDATE user SET password = ? WHERE id = ?",
            [$hashed_password, $user['id']],
            'si'
        );
        
        if ($result !== false) {
            $updated++;
            echo "✓ Updated password for user: {$user['email']}\n";
        } else {
            echo "✗ Failed to update password for user: {$user['email']}\n";
        }
    }
    
    echo "Updated $updated user passwords\n\n";
}

/**
 * Setup admin user with secure password
 */
function setupAdminUser() {
    global $con;
    
    // Check if admin user exists
    $admin = SecureDatabase::getRow("SELECT id, password FROM admin WHERE userid = 'mlm'");
    
    if ($admin) {
        // Update admin password if it's not hashed
        if (strlen($admin['password']) < 60) {
            $new_password = PasswordSecurity::generateSecurePassword(16);
            $hashed_password = PasswordSecurity::hashPassword($new_password);
            
            $result = SecureDatabase::update(
                "UPDATE admin SET password = ? WHERE userid = 'mlm'",
                [$hashed_password],
                's'
            );
            
            if ($result !== false) {
                echo "✓ Admin password updated\n";
                echo "  New admin password: $new_password\n";
                echo "  Please save this password securely!\n\n";
            } else {
                echo "✗ Failed to update admin password\n\n";
            }
        } else {
            echo "✓ Admin user already has secure password\n\n";
        }
    } else {
        echo "✗ Admin user not found in database\n\n";
    }
}

/**
 * Create sample data for development environment
 */
function createSampleData() {
    global $con;
    
    // Create sample user if not exists
    $sample_email = '<EMAIL>';
    $existing_user = SecureDatabase::getRow("SELECT id FROM user WHERE email = ?", [$sample_email]);
    
    if (!$existing_user) {
        $sample_password = PasswordSecurity::generateSecurePassword(12);
        $hashed_password = PasswordSecurity::hashPassword($sample_password);
        
        $user_id = SecureDatabase::insert(
            "INSERT INTO user (email, password, mobile, address, account, under_userid, matrix_position, join_date) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
            [$sample_email, $hashed_password, '+**********', 'Sample Address', '**********', '', 0, date('Y-m-d')],
            'ssssssss'
        );
        
        if ($user_id) {
            // Create tree entry
            SecureDatabase::insert(
                "INSERT INTO tree (userid, sponsor_id) VALUES (?, ?)",
                [$sample_email, '']
            );
            
            // Create income entry
            SecureDatabase::insert(
                "INSERT INTO income (userid) VALUES (?)",
                [$sample_email]
            );
            
            // Create sample pins
            for ($i = 1; $i <= 5; $i++) {
                SecureDatabase::insert(
                    "INSERT INTO pin_list (userid, pin, status) VALUES (?, ?, 'open')",
                    [$sample_email, 100000 + $i],
                    'si'
                );
            }
            
            echo "✓ Created sample user: $sample_email\n";
            echo "  Password: $sample_password\n";
            echo "  Sample pins: 100001-100005\n\n";
        } else {
            echo "✗ Failed to create sample user\n\n";
        }
    } else {
        echo "✓ Sample user already exists\n\n";
    }
}
?>
