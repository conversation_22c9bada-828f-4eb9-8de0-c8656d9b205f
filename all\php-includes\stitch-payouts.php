<?php
/**
 * Stitch Payouts Integration
 * This class handles automated payouts to MLM network members
 * Designed for high-volume, scalable payment processing
 */
class StitchPayouts {
    private $clientId;
    private $clientSecret;
    private $baseUrl;
    private $accessToken;
    private $sandbox;
    
    /**
     * Constructor
     * 
     * @param string $clientId Stitch client ID
     * @param string $clientSecret Stitch client secret
     * @param bool $sandbox Whether to use sandbox environment
     */
    public function __construct($clientId = null, $clientSecret = null, $sandbox = true) {
        global $con;
        
        // If not provided, get from database
        if ($clientId === null || $clientSecret === null) {
            $query = mysqli_query($con, "SELECT * FROM stitch_config WHERE id = 1");
            if (mysqli_num_rows($query) > 0) {
                $config = mysqli_fetch_assoc($query);
                $this->clientId = $config['client_id'];
                $this->clientSecret = $config['client_secret'];
                $this->sandbox = $config['sandbox_mode'] == 1;
            }
        } else {
            $this->clientId = $clientId;
            $this->clientSecret = $clientSecret;
            $this->sandbox = $sandbox;
        }
        
        // Set base URL based on environment
        $this->baseUrl = $this->sandbox ? 
            'https://api.sandbox.stitch.money' : 
            'https://api.stitch.money';
            
        // Get access token
        $this->authenticate();
    }
    
    /**
     * Authenticate with Stitch API and get access token
     * 
     * @return bool True if authentication successful
     */
    private function authenticate() {
        $url = $this->baseUrl . '/graphql';
        
        $mutation = '
            mutation ClientTokenAuth($input: ClientTokenAuthInput!) {
                clientTokenAuth(input: $input) {
                    accessToken
                    expiresIn
                }
            }
        ';
        
        $variables = [
            'input' => [
                'clientId' => $this->clientId,
                'clientSecret' => $this->clientSecret,
                'scopes' => ['client_payouts']
            ]
        ];
        
        $response = $this->makeGraphQLRequest($mutation, $variables, false);
        
        if (isset($response['data']['clientTokenAuth']['accessToken'])) {
            $this->accessToken = $response['data']['clientTokenAuth']['accessToken'];
            return true;
        }
        
        return false;
    }
    
    /**
     * Make a GraphQL request to Stitch API
     * 
     * @param string $query GraphQL query/mutation
     * @param array $variables Query variables
     * @param bool $requireAuth Whether authentication is required
     * @return array Response data
     */
    private function makeGraphQLRequest($query, $variables = [], $requireAuth = true) {
        $url = $this->baseUrl . '/graphql';
        
        $headers = [
            'Content-Type: application/json',
            'Accept: application/json'
        ];
        
        if ($requireAuth && $this->accessToken) {
            $headers[] = 'Authorization: Bearer ' . $this->accessToken;
        }
        
        $payload = [
            'query' => $query,
            'variables' => $variables
        ];
        
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        if (curl_errno($ch)) {
            $error = curl_error($ch);
            curl_close($ch);
            return ['error' => $error, 'code' => $httpCode];
        }
        
        curl_close($ch);
        
        $decodedResponse = json_decode($response, true);
        
        return [
            'data' => $decodedResponse,
            'code' => $httpCode
        ];
    }
    
    /**
     * Create a payout batch for multiple recipients
     * 
     * @param array $payouts Array of payout details
     * @param string $batchReference Unique batch reference
     * @return array Batch creation result
     */
    public function createPayoutBatch($payouts, $batchReference = null) {
        if (empty($payouts)) {
            return ['error' => 'No payouts provided'];
        }
        
        if ($batchReference === null) {
            $batchReference = 'MLM_BATCH_' . date('YmdHis') . '_' . uniqid();
        }
        
        $mutation = '
            mutation CreatePayoutBatch($input: CreatePayoutBatchInput!) {
                createPayoutBatch(input: $input) {
                    id
                    reference
                    status
                    totalAmount {
                        quantity
                        currency
                    }
                    payouts {
                        id
                        reference
                        amount {
                            quantity
                            currency
                        }
                        status
                        recipient {
                            id
                            name
                        }
                    }
                }
            }
        ';
        
        $payoutInputs = [];
        foreach ($payouts as $payout) {
            $payoutInputs[] = [
                'reference' => $payout['reference'],
                'amount' => [
                    'quantity' => $payout['amount'],
                    'currency' => $payout['currency'] ?? 'ZAR'
                ],
                'recipientId' => $payout['recipient_id'],
                'externalReference' => $payout['external_reference'] ?? null
            ];
        }
        
        $variables = [
            'input' => [
                'reference' => $batchReference,
                'payouts' => $payoutInputs
            ]
        ];
        
        return $this->makeGraphQLRequest($mutation, $variables);
    }
    
    /**
     * Create a recipient for payouts
     * 
     * @param array $recipientData Recipient information
     * @return array Recipient creation result
     */
    public function createRecipient($recipientData) {
        $mutation = '
            mutation CreatePayoutRecipient($input: CreatePayoutRecipientInput!) {
                createPayoutRecipient(input: $input) {
                    id
                    name
                    accountNumber
                    bankId
                    status
                }
            }
        ';
        
        $variables = [
            'input' => [
                'name' => $recipientData['name'],
                'accountNumber' => $recipientData['account_number'],
                'bankId' => $recipientData['bank_id'],
                'reference' => $recipientData['reference'] ?? null
            ]
        ];
        
        return $this->makeGraphQLRequest($mutation, $variables);
    }
    
    /**
     * Get available banks for recipient creation
     * 
     * @return array List of available banks
     */
    public function getBanks() {
        $query = '
            query GetBanks {
                banks {
                    id
                    name
                    code
                    country
                }
            }
        ';
        
        return $this->makeGraphQLRequest($query);
    }
    
    /**
     * Get payout batch status
     * 
     * @param string $batchId Batch ID
     * @return array Batch status information
     */
    public function getPayoutBatchStatus($batchId) {
        $query = '
            query GetPayoutBatch($id: ID!) {
                payoutBatch(id: $id) {
                    id
                    reference
                    status
                    totalAmount {
                        quantity
                        currency
                    }
                    payouts {
                        id
                        reference
                        amount {
                            quantity
                            currency
                        }
                        status
                        recipient {
                            id
                            name
                        }
                        statusReason
                    }
                    createdAt
                    updatedAt
                }
            }
        ';
        
        $variables = ['id' => $batchId];
        
        return $this->makeGraphQLRequest($query, $variables);
    }
    
    /**
     * Process MLM commission payouts automatically
     * 
     * @param array $commissions Array of commission data
     * @return array Processing result
     */
    public function processMLMPayouts($commissions) {
        global $con;
        
        $results = [
            'success' => false,
            'batch_id' => null,
            'payouts' => [],
            'errors' => []
        ];
        
        if (empty($commissions)) {
            $results['errors'][] = 'No commissions to process';
            return $results;
        }
        
        $payouts = [];
        
        foreach ($commissions as $commission) {
            $userId = $commission['userid'];
            $amount = $commission['amount'];
            
            // Get user's Stitch recipient ID
            $recipientQuery = mysqli_query($con, "SELECT * FROM stitch_recipients WHERE userid='$userId'");
            
            if (mysqli_num_rows($recipientQuery) == 0) {
                $results['errors'][] = "No Stitch recipient found for user: $userId";
                continue;
            }
            
            $recipient = mysqli_fetch_assoc($recipientQuery);
            
            $payouts[] = [
                'reference' => 'MLM_PAYOUT_' . $userId . '_' . time(),
                'amount' => $amount,
                'currency' => 'ZAR',
                'recipient_id' => $recipient['stitch_recipient_id'],
                'external_reference' => $commission['commission_id'] ?? null
            ];
        }
        
        if (empty($payouts)) {
            $results['errors'][] = 'No valid payouts to process';
            return $results;
        }
        
        // Create the payout batch
        $batchResult = $this->createPayoutBatch($payouts);
        
        if (isset($batchResult['error'])) {
            $results['errors'][] = 'Failed to create payout batch: ' . $batchResult['error'];
            return $results;
        }
        
        if (isset($batchResult['data']['data']['createPayoutBatch'])) {
            $batch = $batchResult['data']['data']['createPayoutBatch'];
            $results['batch_id'] = $batch['id'];
            $results['payouts'] = $batch['payouts'];
            $results['success'] = true;
            
            // Store batch information in database
            $batchId = $batch['id'];
            $batchReference = $batch['reference'];
            $totalAmount = $batch['totalAmount']['quantity'];
            $currency = $batch['totalAmount']['currency'];
            
            mysqli_query($con, "INSERT INTO stitch_payout_batches 
                (batch_id, reference, total_amount, currency, status, created_date) 
                VALUES ('$batchId', '$batchReference', '$totalAmount', '$currency', 'created', NOW())");
            
            $dbBatchId = mysqli_insert_id($con);
            
            // Store individual payout records
            foreach ($batch['payouts'] as $payout) {
                $payoutId = $payout['id'];
                $payoutReference = $payout['reference'];
                $payoutAmount = $payout['amount']['quantity'];
                $recipientId = $payout['recipient']['id'];
                $status = $payout['status'];
                
                mysqli_query($con, "INSERT INTO stitch_payouts 
                    (batch_id, payout_id, reference, amount, recipient_id, status, created_date) 
                    VALUES ('$dbBatchId', '$payoutId', '$payoutReference', '$payoutAmount', '$recipientId', '$status', NOW())");
            }
        } else {
            $results['errors'][] = 'Unexpected response format from Stitch API';
        }
        
        return $results;
    }
}
?>
