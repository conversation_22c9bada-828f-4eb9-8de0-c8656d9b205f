<?php
/**
 * Localization Support for South African Languages
 * Supports English, Afrikaans, Zulu, and Xhosa
 */

class Localization {
    
    private static $current_language = 'en';
    private static $translations = [];
    private static $loaded_languages = [];
    
    /**
     * Initialize localization system
     */
    public static function init($default_language = 'en') {
        self::$current_language = $default_language;
        
        // Load language from session or browser
        if (isset($_SESSION['language'])) {
            self::$current_language = $_SESSION['language'];
        } elseif (isset($_SERVER['HTTP_ACCEPT_LANGUAGE'])) {
            $browser_lang = substr($_SERVER['HTTP_ACCEPT_LANGUAGE'], 0, 2);
            if (in_array($browser_lang, ['en', 'af', 'zu', 'xh'])) {
                self::$current_language = $browser_lang;
            }
        }
        
        self::loadLanguage(self::$current_language);
    }
    
    /**
     * Load language translations
     */
    private static function loadLanguage($language) {
        if (in_array($language, self::$loaded_languages)) {
            return;
        }
        
        self::$translations[$language] = self::getTranslations($language);
        self::$loaded_languages[] = $language;
    }
    
    /**
     * Get translations for a specific language
     */
    private static function getTranslations($language) {
        $translations = [
            'en' => [
                // Navigation
                'home' => 'Home',
                'dashboard' => 'Dashboard',
                'income' => 'Income',
                'matrix' => 'Matrix',
                'team' => 'Team',
                'profile' => 'Profile',
                'logout' => 'Logout',
                'login' => 'Login',
                
                // Common
                'welcome' => 'Welcome',
                'loading' => 'Loading...',
                'save' => 'Save',
                'cancel' => 'Cancel',
                'submit' => 'Submit',
                'continue' => 'Continue',
                'back' => 'Back',
                'next' => 'Next',
                'close' => 'Close',
                'error' => 'Error',
                'success' => 'Success',
                'warning' => 'Warning',
                'info' => 'Information',
                
                // MLM Specific
                'ubuntu_wealth_network' => 'Ubuntu Wealth Network',
                'financial_freedom' => 'Financial Freedom',
                'build_wealth_together' => 'Building Wealth Together',
                'ubuntu_philosophy' => 'Ubuntu: I am because we are',
                'invite_member' => 'Invite New Member',
                'total_earnings' => 'Total Earnings',
                'team_members' => 'Team Members',
                'current_level' => 'Current Level',
                'pending_payments' => 'Pending Payments',
                'commission_potential' => 'Commission Potential',
                'matrix_progress' => 'Matrix Progress',
                'recent_income' => 'Recent Income',
                'quick_actions' => 'Quick Actions',
                
                // Forms
                'email_address' => 'Email Address',
                'mobile_number' => 'Mobile Number',
                'password' => 'Password',
                'confirm_password' => 'Confirm Password',
                'registration_pin' => 'Registration Pin',
                'matrix_position' => 'Matrix Position',
                'select_position' => 'Select position (1-5)',
                
                // Messages
                'otp_sent' => 'OTP has been sent to your mobile number',
                'registration_complete' => 'Registration Complete!',
                'invalid_credentials' => 'Invalid email or password',
                'account_created' => 'Account created successfully',
                'welcome_back' => 'Welcome back',
                
                // Currency
                'currency_symbol' => 'R',
                'currency_name' => 'Rand',
            ],
            
            'af' => [
                // Navigation
                'home' => 'Tuis',
                'dashboard' => 'Kontrolepaneel',
                'income' => 'Inkomste',
                'matrix' => 'Matriks',
                'team' => 'Span',
                'profile' => 'Profiel',
                'logout' => 'Teken Uit',
                'login' => 'Teken In',
                
                // Common
                'welcome' => 'Welkom',
                'loading' => 'Laai...',
                'save' => 'Stoor',
                'cancel' => 'Kanselleer',
                'submit' => 'Dien In',
                'continue' => 'Gaan Voort',
                'back' => 'Terug',
                'next' => 'Volgende',
                'close' => 'Sluit',
                'error' => 'Fout',
                'success' => 'Sukses',
                'warning' => 'Waarskuwing',
                'info' => 'Inligting',
                
                // MLM Specific
                'ubuntu_wealth_network' => 'Ubuntu Rykdom Netwerk',
                'financial_freedom' => 'Finansiële Vryheid',
                'build_wealth_together' => 'Bou Rykdom Saam',
                'ubuntu_philosophy' => 'Ubuntu: Ek is omdat ons is',
                'invite_member' => 'Nooi Nuwe Lid',
                'total_earnings' => 'Totale Verdienste',
                'team_members' => 'Spanlede',
                'current_level' => 'Huidige Vlak',
                'pending_payments' => 'Hangende Betalings',
                'commission_potential' => 'Kommissie Potensiaal',
                'matrix_progress' => 'Matriks Vordering',
                'recent_income' => 'Onlangse Inkomste',
                'quick_actions' => 'Vinnige Aksies',
                
                // Forms
                'email_address' => 'E-pos Adres',
                'mobile_number' => 'Selfoon Nommer',
                'password' => 'Wagwoord',
                'confirm_password' => 'Bevestig Wagwoord',
                'registration_pin' => 'Registrasie Pin',
                'matrix_position' => 'Matriks Posisie',
                'select_position' => 'Kies posisie (1-5)',
                
                // Messages
                'otp_sent' => 'OTP is na jou selfoon gestuur',
                'registration_complete' => 'Registrasie Voltooi!',
                'invalid_credentials' => 'Ongeldige e-pos of wagwoord',
                'account_created' => 'Rekening suksesvol geskep',
                'welcome_back' => 'Welkom terug',
                
                // Currency
                'currency_symbol' => 'R',
                'currency_name' => 'Rand',
            ],
            
            'zu' => [
                // Navigation
                'home' => 'Ikhaya',
                'dashboard' => 'Ibhodi Lokulawula',
                'income' => 'Imali Engenayo',
                'matrix' => 'I-Matrix',
                'team' => 'Ithimba',
                'profile' => 'Iphrofayela',
                'logout' => 'Phuma',
                'login' => 'Ngena',
                
                // Common
                'welcome' => 'Sawubona',
                'loading' => 'Iyalayisha...',
                'save' => 'Londoloza',
                'cancel' => 'Khansela',
                'submit' => 'Thumela',
                'continue' => 'Qhubeka',
                'back' => 'Buyela Emuva',
                'next' => 'Okulandelayo',
                'close' => 'Vala',
                'error' => 'Iphutha',
                'success' => 'Impumelelo',
                'warning' => 'Isexwayiso',
                'info' => 'Ulwazi',
                
                // MLM Specific
                'ubuntu_wealth_network' => 'Inethiwekhi Yenotho ye-Ubuntu',
                'financial_freedom' => 'Inkululeko Yezimali',
                'build_wealth_together' => 'Sakha Inotho Ndawonye',
                'ubuntu_philosophy' => 'Ubuntu: Ngikhona ngoba sikhona',
                'invite_member' => 'Mema Ilungu Elisha',
                'total_earnings' => 'Imali Ephelele Etholiwe',
                'team_members' => 'Amalungu Ethimba',
                'current_level' => 'Izinga Lamanje',
                'pending_payments' => 'Izinkokhelo Ezilindile',
                'commission_potential' => 'Amandla e-Commission',
                'matrix_progress' => 'Inqubekelaphambili ye-Matrix',
                'recent_income' => 'Imali Yakamuva Engenayo',
                'quick_actions' => 'Izenzo Ezisheshayo',
                
                // Forms
                'email_address' => 'Ikheli le-imeyili',
                'mobile_number' => 'Inombolo Yocingo',
                'password' => 'Iphasiwedi',
                'confirm_password' => 'Qinisekisa Iphasiwedi',
                'registration_pin' => 'I-Pin Yokubhalisa',
                'matrix_position' => 'Isikhundla se-Matrix',
                'select_position' => 'Khetha isikhundla (1-5)',
                
                // Messages
                'otp_sent' => 'I-OTP ithunyelwe kunombolo yakho yocingo',
                'registration_complete' => 'Ukubhalisa Kuqediwe!',
                'invalid_credentials' => 'I-imeyili noma iphasiwedi engalungile',
                'account_created' => 'I-akhawunti idalwe ngempumelelo',
                'welcome_back' => 'Sawubona futhi',
                
                // Currency
                'currency_symbol' => 'R',
                'currency_name' => 'Randi',
            ],
            
            'xh' => [
                // Navigation
                'home' => 'Ikhaya',
                'dashboard' => 'Ibhodi Yolawulo',
                'income' => 'Ingeniso',
                'matrix' => 'I-Matrix',
                'team' => 'Iqela',
                'profile' => 'Iprofayile',
                'logout' => 'Phuma',
                'login' => 'Ngena',
                
                // Common
                'welcome' => 'Wamkelekile',
                'loading' => 'Iyalayisha...',
                'save' => 'Gcina',
                'cancel' => 'Rhoxisa',
                'submit' => 'Ngenisa',
                'continue' => 'Qhubeka',
                'back' => 'Buyela',
                'next' => 'Okulandelayo',
                'close' => 'Vala',
                'error' => 'Impazamo',
                'success' => 'Impumelelo',
                'warning' => 'Isilumkiso',
                'info' => 'Inkcazelo',
                
                // MLM Specific
                'ubuntu_wealth_network' => 'Uthungelwano Lobutyebi be-Ubuntu',
                'financial_freedom' => 'Inkululeko Yezemali',
                'build_wealth_together' => 'Sakha Ubutyebi Kunye',
                'ubuntu_philosophy' => 'Ubuntu: Ndikho kuba sikho',
                'invite_member' => 'Mema Ilungu Elitsha',
                'total_earnings' => 'Ingeniso Epheleleyo',
                'team_members' => 'Amalungu Eqela',
                'current_level' => 'Inqanaba Langoku',
                'pending_payments' => 'Iintlawulo Ezilindileyo',
                'commission_potential' => 'Amandla e-Commission',
                'matrix_progress' => 'Inkqubela ye-Matrix',
                'recent_income' => 'Ingeniso Yakutshanje',
                'quick_actions' => 'Izenzo Ezikhawulezayo',
                
                // Forms
                'email_address' => 'Idilesi ye-imeyile',
                'mobile_number' => 'Inombolo Yeselfowuni',
                'password' => 'Iphaswedi',
                'confirm_password' => 'Qinisekisa Iphaswedi',
                'registration_pin' => 'I-Pin Yokubhalisa',
                'matrix_position' => 'Indawo ye-Matrix',
                'select_position' => 'Khetha indawo (1-5)',
                
                // Messages
                'otp_sent' => 'I-OTP ithunyelwe kwinombolo yakho yeselfowuni',
                'registration_complete' => 'Ukubhalisa Kugqityiwe!',
                'invalid_credentials' => 'I-imeyile okanye iphaswedi engeyiyo',
                'account_created' => 'I-akhawunti yenziwe ngempumelelo',
                'welcome_back' => 'Wamkelekile kwakhona',
                
                // Currency
                'currency_symbol' => 'R',
                'currency_name' => 'Randi',
            ]
        ];
        
        return $translations[$language] ?? $translations['en'];
    }
    
    /**
     * Get translated text
     */
    public static function get($key, $default = null) {
        if (!isset(self::$translations[self::$current_language])) {
            self::loadLanguage(self::$current_language);
        }
        
        $translation = self::$translations[self::$current_language][$key] ?? null;
        
        if ($translation === null && self::$current_language !== 'en') {
            // Fallback to English
            if (!isset(self::$translations['en'])) {
                self::loadLanguage('en');
            }
            $translation = self::$translations['en'][$key] ?? null;
        }
        
        return $translation ?? $default ?? $key;
    }
    
    /**
     * Set current language
     */
    public static function setLanguage($language) {
        if (in_array($language, ['en', 'af', 'zu', 'xh'])) {
            self::$current_language = $language;
            $_SESSION['language'] = $language;
            self::loadLanguage($language);
        }
    }
    
    /**
     * Get current language
     */
    public static function getCurrentLanguage() {
        return self::$current_language;
    }
    
    /**
     * Get available languages
     */
    public static function getAvailableLanguages() {
        return [
            'en' => 'English',
            'af' => 'Afrikaans',
            'zu' => 'isiZulu',
            'xh' => 'isiXhosa'
        ];
    }
    
    /**
     * Format currency
     */
    public static function formatCurrency($amount, $include_symbol = true) {
        $symbol = self::get('currency_symbol', 'R');
        $formatted = number_format($amount, 2);
        
        return $include_symbol ? $symbol . $formatted : $formatted;
    }
    
    /**
     * Format date according to locale
     */
    public static function formatDate($date, $format = 'medium') {
        $timestamp = is_string($date) ? strtotime($date) : $date;
        
        switch ($format) {
            case 'short':
                return date('d/m/Y', $timestamp);
            case 'medium':
                return date('j M Y', $timestamp);
            case 'long':
                return date('j F Y', $timestamp);
            case 'full':
                return date('l, j F Y', $timestamp);
            default:
                return date($format, $timestamp);
        }
    }
    
    /**
     * Get language direction (for future RTL support)
     */
    public static function getDirection() {
        return 'ltr'; // All supported languages are left-to-right
    }
}

/**
 * Helper function for translations
 */
function __($key, $default = null) {
    return Localization::get($key, $default);
}

/**
 * Helper function for currency formatting
 */
function currency($amount, $include_symbol = true) {
    return Localization::formatCurrency($amount, $include_symbol);
}

/**
 * Helper function for date formatting
 */
function formatDate($date, $format = 'medium') {
    return Localization::formatDate($date, $format);
}
?>
