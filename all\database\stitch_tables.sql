-- Stitch Payouts Database Tables
-- Run this SQL to create the necessary tables for Stitch integration

-- Configuration table for Stitch API credentials
CREATE TABLE IF NOT EXISTS `stitch_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` varchar(255) NOT NULL,
  `client_secret` varchar(255) NOT NULL,
  `sandbox_mode` tinyint(1) DEFAULT 1,
  `created_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table to store Stitch recipient information for users
CREATE TABLE IF NOT EXISTS `stitch_recipients` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userid` varchar(100) NOT NULL,
  `stitch_recipient_id` varchar(255) NOT NULL,
  `recipient_name` varchar(255) NOT NULL,
  `account_number` varchar(50) NOT NULL,
  `bank_id` varchar(50) NOT NULL,
  `bank_name` varchar(255) DEFAULT NULL,
  `status` enum('active','inactive','pending') DEFAULT 'pending',
  `created_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `userid` (`userid`),
  UNIQUE KEY `stitch_recipient_id` (`stitch_recipient_id`),
  KEY `idx_userid` (`userid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table to store payout batch information
CREATE TABLE IF NOT EXISTS `stitch_payout_batches` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `batch_id` varchar(255) NOT NULL,
  `reference` varchar(255) NOT NULL,
  `total_amount` decimal(15,2) NOT NULL,
  `currency` varchar(3) DEFAULT 'ZAR',
  `status` enum('created','processing','completed','failed','cancelled') DEFAULT 'created',
  `total_payouts` int(11) DEFAULT 0,
  `successful_payouts` int(11) DEFAULT 0,
  `failed_payouts` int(11) DEFAULT 0,
  `created_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `batch_id` (`batch_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_date` (`created_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table to store individual payout records
CREATE TABLE IF NOT EXISTS `stitch_payouts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `batch_id` int(11) NOT NULL,
  `payout_id` varchar(255) NOT NULL,
  `reference` varchar(255) NOT NULL,
  `userid` varchar(100) DEFAULT NULL,
  `amount` decimal(15,2) NOT NULL,
  `currency` varchar(3) DEFAULT 'ZAR',
  `recipient_id` varchar(255) NOT NULL,
  `status` enum('created','processing','completed','failed','cancelled') DEFAULT 'created',
  `status_reason` text DEFAULT NULL,
  `commission_type` enum('matrix','referral','bonus','other') DEFAULT 'matrix',
  `commission_level` int(11) DEFAULT NULL,
  `created_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `payout_id` (`payout_id`),
  KEY `batch_id` (`batch_id`),
  KEY `idx_userid` (`userid`),
  KEY `idx_status` (`status`),
  KEY `idx_created_date` (`created_date`),
  CONSTRAINT `stitch_payouts_ibfk_1` FOREIGN KEY (`batch_id`) REFERENCES `stitch_payout_batches` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table to track commission calculations and payouts
CREATE TABLE IF NOT EXISTS `commission_queue` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userid` varchar(100) NOT NULL,
  `commission_type` enum('matrix','referral','bonus','other') NOT NULL,
  `level` int(11) DEFAULT NULL,
  `amount` decimal(15,2) NOT NULL,
  `currency` varchar(3) DEFAULT 'ZAR',
  `triggered_by` varchar(100) DEFAULT NULL,
  `status` enum('pending','processing','paid','failed') DEFAULT 'pending',
  `batch_id` int(11) DEFAULT NULL,
  `payout_id` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `processed_date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_userid` (`userid`),
  KEY `idx_status` (`status`),
  KEY `idx_commission_type` (`commission_type`),
  KEY `idx_created_date` (`created_date`),
  KEY `batch_id` (`batch_id`),
  CONSTRAINT `commission_queue_ibfk_1` FOREIGN KEY (`batch_id`) REFERENCES `stitch_payout_batches` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table to store available banks from Stitch
CREATE TABLE IF NOT EXISTS `stitch_banks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `stitch_bank_id` varchar(50) NOT NULL,
  `name` varchar(255) NOT NULL,
  `code` varchar(10) DEFAULT NULL,
  `country` varchar(2) DEFAULT 'ZA',
  `is_active` tinyint(1) DEFAULT 1,
  `created_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `stitch_bank_id` (`stitch_bank_id`),
  KEY `idx_country` (`country`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table to log payout processing activities
CREATE TABLE IF NOT EXISTS `payout_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `batch_id` int(11) DEFAULT NULL,
  `payout_id` int(11) DEFAULT NULL,
  `action` varchar(100) NOT NULL,
  `status` varchar(50) NOT NULL,
  `message` text DEFAULT NULL,
  `response_data` json DEFAULT NULL,
  `created_date` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `batch_id` (`batch_id`),
  KEY `payout_id` (`payout_id`),
  KEY `idx_action` (`action`),
  KEY `idx_created_date` (`created_date`),
  CONSTRAINT `payout_logs_ibfk_1` FOREIGN KEY (`batch_id`) REFERENCES `stitch_payout_batches` (`id`) ON DELETE SET NULL,
  CONSTRAINT `payout_logs_ibfk_2` FOREIGN KEY (`payout_id`) REFERENCES `stitch_payouts` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default Stitch configuration (update with your actual credentials)
INSERT INTO `stitch_config` (`client_id`, `client_secret`, `sandbox_mode`) 
VALUES ('your_stitch_client_id', 'your_stitch_client_secret', 1)
ON DUPLICATE KEY UPDATE 
  `client_id` = VALUES(`client_id`),
  `client_secret` = VALUES(`client_secret`),
  `sandbox_mode` = VALUES(`sandbox_mode`);

-- Insert some common South African banks (update as needed)
INSERT INTO `stitch_banks` (`stitch_bank_id`, `name`, `code`, `country`) VALUES
('absa', 'ABSA Bank', 'ABSA', 'ZA'),
('standard_bank', 'Standard Bank', 'SBZA', 'ZA'),
('fnb', 'First National Bank', 'FNB', 'ZA'),
('nedbank', 'Nedbank', 'NEDBANK', 'ZA'),
('capitec', 'Capitec Bank', 'CAPITEC', 'ZA'),
('investec', 'Investec Bank', 'INVESTEC', 'ZA'),
('discovery', 'Discovery Bank', 'DISCOVERY', 'ZA'),
('tymebank', 'TymeBank', 'TYMEBANK', 'ZA')
ON DUPLICATE KEY UPDATE 
  `name` = VALUES(`name`),
  `code` = VALUES(`code`),
  `country` = VALUES(`country`);
