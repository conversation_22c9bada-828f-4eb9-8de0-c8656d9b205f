<?php
/**
 * MLM Logic Verification Script
 * Comprehensive verification of commission calculations and matrix logic
 */

// Prevent web access
if (isset($_SERVER['HTTP_HOST'])) {
    die('This script can only be run from command line or by direct access.');
}

// Set testing mode
$_ENV['TESTING_MODE'] = 'true';

require_once __DIR__ . '/php-includes/config.php';
require_once __DIR__ . '/php-includes/test-connect.php';
require_once __DIR__ . '/php-includes/error-handler.php';
require_once __DIR__ . '/php-includes/secure-database.php';

// Initialize components
ErrorHandler::init($con, ['display_errors' => true, 'log_to_file' => false]);
SecureDatabase::init($con);

echo "=== MLM Logic Verification ===\n";

$tests_passed = 0;
$tests_failed = 0;
$test_results = [];

// Test 1: Matrix Structure Verification
echo "\n1. Testing Matrix Structure...\n";
$matrix_test = testMatrixStructure();
if ($matrix_test['passed']) {
    echo "   ✓ Matrix structure is valid\n";
    $tests_passed++;
} else {
    echo "   ✗ Matrix structure issues found\n";
    foreach ($matrix_test['errors'] as $error) {
        echo "     - $error\n";
    }
    $tests_failed++;
}
$test_results['matrix_structure'] = $matrix_test;

// Test 2: Commission Calculation Verification
echo "\n2. Testing Commission Calculations...\n";
$commission_test = testCommissionCalculations();
if ($commission_test['passed']) {
    echo "   ✓ Commission calculations are correct\n";
    $tests_passed++;
} else {
    echo "   ✗ Commission calculation issues found\n";
    foreach ($commission_test['errors'] as $error) {
        echo "     - $error\n";
    }
    $tests_failed++;
}
$test_results['commission_calculations'] = $commission_test;

// Test 3: Upline Chain Verification
echo "\n3. Testing Upline Chain Logic...\n";
$upline_test = testUplineChain();
if ($upline_test['passed']) {
    echo "   ✓ Upline chain logic is correct\n";
    $tests_passed++;
} else {
    echo "   ✗ Upline chain issues found\n";
    foreach ($upline_test['errors'] as $error) {
        echo "     - $error\n";
    }
    $tests_failed++;
}
$test_results['upline_chain'] = $upline_test;

// Test 4: Matrix Position Validation
echo "\n4. Testing Matrix Position Logic...\n";
$position_test = testMatrixPositions();
if ($position_test['passed']) {
    echo "   ✓ Matrix position logic is correct\n";
    $tests_passed++;
} else {
    echo "   ✗ Matrix position issues found\n";
    foreach ($position_test['errors'] as $error) {
        echo "     - $error\n";
    }
    $tests_failed++;
}
$test_results['matrix_positions'] = $position_test;

// Test 5: Commission Level Distribution
echo "\n5. Testing Commission Level Distribution...\n";
$level_test = testCommissionLevels();
if ($level_test['passed']) {
    echo "   ✓ Commission level distribution is correct\n";
    $tests_passed++;
} else {
    echo "   ✗ Commission level distribution issues found\n";
    foreach ($level_test['errors'] as $error) {
        echo "     - $error\n";
    }
    $tests_failed++;
}
$test_results['commission_levels'] = $level_test;

// Test 6: Data Consistency Check
echo "\n6. Testing Data Consistency...\n";
$consistency_test = testDataConsistency();
if ($consistency_test['passed']) {
    echo "   ✓ Data consistency is maintained\n";
    $tests_passed++;
} else {
    echo "   ✗ Data consistency issues found\n";
    foreach ($consistency_test['errors'] as $error) {
        echo "     - $error\n";
    }
    $tests_failed++;
}
$test_results['data_consistency'] = $consistency_test;

// Generate detailed report
echo "\n7. Generating detailed verification report...\n";
$report = generateDetailedReport($test_results);
echo "   ✓ Report saved: {$report['filename']}\n";

// Summary
echo "\n=== Verification Summary ===\n";
echo "Tests Passed: $tests_passed\n";
echo "Tests Failed: $tests_failed\n";
echo "Total Tests: " . ($tests_passed + $tests_failed) . "\n";
echo "Success Rate: " . round(($tests_passed / ($tests_passed + $tests_failed)) * 100, 1) . "%\n";

if ($tests_failed === 0) {
    echo "\n🎉 All MLM logic tests passed! The system is working correctly.\n";
    exit(0);
} else {
    echo "\n⚠️  Some tests failed. Please review the detailed report: {$report['filename']}\n";
    exit(1);
}

/**
 * Test matrix structure integrity
 */
function testMatrixStructure() {
    $errors = [];
    
    // Check for orphaned users (users without sponsors in tree)
    $orphaned = SecureDatabase::getRows(
        "SELECT u.email FROM user u 
         LEFT JOIN tree t ON u.email = t.userid 
         WHERE t.userid IS NULL AND u.email LIKE '%@test.com'"
    );
    
    if (!empty($orphaned)) {
        $errors[] = "Found " . count($orphaned) . " orphaned users without tree entries";
    }
    
    // Check for circular references
    $circular = SecureDatabase::getRows(
        "SELECT t1.userid FROM tree t1 
         JOIN tree t2 ON t1.sponsor_id = t2.userid 
         WHERE t2.sponsor_id = t1.userid"
    );
    
    if (!empty($circular)) {
        $errors[] = "Found " . count($circular) . " circular sponsor references";
    }
    
    // Check matrix position consistency
    $position_issues = SecureDatabase::getRows(
        "SELECT t.userid, u.matrix_position, 
                CASE 
                    WHEN u.matrix_position = 1 AND t.position1 != u.email THEN 'Position 1 mismatch'
                    WHEN u.matrix_position = 2 AND t.position2 != u.email THEN 'Position 2 mismatch'
                    WHEN u.matrix_position = 3 AND t.position3 != u.email THEN 'Position 3 mismatch'
                    WHEN u.matrix_position = 4 AND t.position4 != u.email THEN 'Position 4 mismatch'
                    WHEN u.matrix_position = 5 AND t.position5 != u.email THEN 'Position 5 mismatch'
                END as issue
         FROM user u 
         JOIN tree t ON u.under_userid = t.userid 
         WHERE u.email LIKE '%@test.com' AND u.matrix_position > 0
         HAVING issue IS NOT NULL"
    );
    
    if (!empty($position_issues)) {
        $errors[] = "Found " . count($position_issues) . " matrix position inconsistencies";
    }
    
    return [
        'passed' => empty($errors),
        'errors' => $errors,
        'details' => [
            'orphaned_users' => count($orphaned ?? []),
            'circular_references' => count($circular ?? []),
            'position_issues' => count($position_issues ?? [])
        ]
    ];
}

/**
 * Test commission calculations
 */
function testCommissionCalculations() {
    $errors = [];
    
    // Get commission configuration
    $commission_levels = [
        1 => Config::get('commissions.level_1', 1000),
        2 => Config::get('commissions.level_2', 5000),
        3 => Config::get('commissions.level_3', 25000),
        4 => Config::get('commissions.level_4', 125000),
        5 => Config::get('commissions.level_5', 625000)
    ];
    
    // Check if commissions match expected amounts
    foreach ($commission_levels as $level => $expected_amount) {
        $actual_commissions = SecureDatabase::getRows(
            "SELECT * FROM income_received WHERE commission_level = ? AND amount != ?",
            [$level, $expected_amount],
            'ii'
        );
        
        if (!empty($actual_commissions)) {
            $errors[] = "Found " . count($actual_commissions) . " incorrect commission amounts for level $level";
        }
    }
    
    // Check for missing commissions (users who should have received but didn't)
    $missing_commissions = SecureDatabase::getRows(
        "SELECT u1.email as new_user, u2.email as sponsor
         FROM user u1 
         JOIN user u2 ON u1.under_userid = u2.email 
         LEFT JOIN income_received ir ON ir.userid = u2.email AND ir.from_userid = u1.email
         WHERE u1.email LIKE '<EMAIL>' AND ir.id IS NULL"
    );
    
    if (!empty($missing_commissions)) {
        $errors[] = "Found " . count($missing_commissions) . " missing commission records";
    }
    
    return [
        'passed' => empty($errors),
        'errors' => $errors,
        'details' => [
            'commission_levels_checked' => count($commission_levels),
            'missing_commissions' => count($missing_commissions ?? [])
        ]
    ];
}

/**
 * Test upline chain logic
 */
function testUplineChain() {
    $errors = [];
    
    // Test upline chain depth (should not exceed 5 levels)
    $deep_chains = SecureDatabase::getRows(
        "WITH RECURSIVE upline_chain AS (
            SELECT userid, sponsor_id, 1 as level
            FROM tree 
            WHERE userid LIKE '<EMAIL>'
            
            UNION ALL
            
            SELECT t.userid, t.sponsor_id, uc.level + 1
            FROM tree t
            JOIN upline_chain uc ON t.userid = uc.sponsor_id
            WHERE uc.level < 10
        )
        SELECT userid, MAX(level) as max_level 
        FROM upline_chain 
        GROUP BY userid 
        HAVING max_level > 5"
    );
    
    if (!empty($deep_chains)) {
        $errors[] = "Found " . count($deep_chains) . " upline chains exceeding 5 levels";
    }
    
    // Check for broken upline chains
    $broken_chains = SecureDatabase::getRows(
        "SELECT t.userid FROM tree t 
         WHERE t.sponsor_id != '' 
         AND t.sponsor_id NOT IN (SELECT userid FROM tree WHERE userid = t.sponsor_id)"
    );
    
    if (!empty($broken_chains)) {
        $errors[] = "Found " . count($broken_chains) . " broken upline chain references";
    }
    
    return [
        'passed' => empty($errors),
        'errors' => $errors,
        'details' => [
            'deep_chains' => count($deep_chains ?? []),
            'broken_chains' => count($broken_chains ?? [])
        ]
    ];
}

/**
 * Test matrix positions
 */
function testMatrixPositions() {
    $errors = [];
    
    // Check for duplicate positions
    $duplicates = SecureDatabase::getRows(
        "SELECT sponsor_id, matrix_position, COUNT(*) as count
         FROM user 
         WHERE under_userid != '' AND matrix_position > 0
         GROUP BY under_userid, matrix_position 
         HAVING count > 1"
    );
    
    if (!empty($duplicates)) {
        $errors[] = "Found " . count($duplicates) . " duplicate matrix positions";
    }
    
    // Check for invalid position numbers
    $invalid_positions = SecureDatabase::getRows(
        "SELECT email FROM user 
         WHERE matrix_position NOT IN (0, 1, 2, 3, 4, 5)"
    );
    
    if (!empty($invalid_positions)) {
        $errors[] = "Found " . count($invalid_positions) . " users with invalid matrix positions";
    }
    
    return [
        'passed' => empty($errors),
        'errors' => $errors,
        'details' => [
            'duplicate_positions' => count($duplicates ?? []),
            'invalid_positions' => count($invalid_positions ?? [])
        ]
    ];
}

/**
 * Test commission level distribution
 */
function testCommissionLevels() {
    $errors = [];
    
    // Check if commission levels are distributed correctly
    $level_distribution = SecureDatabase::getRows(
        "SELECT commission_level, COUNT(*) as count 
         FROM income_received 
         GROUP BY commission_level 
         ORDER BY commission_level"
    );
    
    // Verify that higher levels have fewer commissions (pyramid structure)
    $previous_count = PHP_INT_MAX;
    foreach ($level_distribution as $level) {
        if ($level['count'] > $previous_count) {
            $errors[] = "Level {$level['commission_level']} has more commissions than previous level (pyramid inversion)";
        }
        $previous_count = $level['count'];
    }
    
    return [
        'passed' => empty($errors),
        'errors' => $errors,
        'details' => [
            'level_distribution' => $level_distribution
        ]
    ];
}

/**
 * Test data consistency
 */
function testDataConsistency() {
    $errors = [];
    
    // Check user-tree consistency
    $user_tree_mismatch = SecureDatabase::getValue(
        "SELECT COUNT(*) FROM user u 
         LEFT JOIN tree t ON u.email = t.userid 
         WHERE t.userid IS NULL AND u.email LIKE '%@test.com'"
    );
    
    if ($user_tree_mismatch > 0) {
        $errors[] = "$user_tree_mismatch users missing from tree table";
    }
    
    // Check user-income consistency
    $user_income_mismatch = SecureDatabase::getValue(
        "SELECT COUNT(*) FROM user u 
         LEFT JOIN income i ON u.email = i.userid 
         WHERE i.userid IS NULL AND u.email LIKE '%@test.com'"
    );
    
    if ($user_income_mismatch > 0) {
        $errors[] = "$user_income_mismatch users missing from income table";
    }
    
    return [
        'passed' => empty($errors),
        'errors' => $errors,
        'details' => [
            'user_tree_mismatch' => $user_tree_mismatch,
            'user_income_mismatch' => $user_income_mismatch
        ]
    ];
}

/**
 * Generate detailed verification report
 */
function generateDetailedReport($test_results) {
    $filename = "reports/mlm_verification_" . date('Y-m-d_H-i-s') . ".json";
    
    if (!is_dir('reports')) {
        mkdir('reports', 0755, true);
    }
    
    $report = [
        'timestamp' => date('Y-m-d H:i:s'),
        'test_results' => $test_results,
        'summary' => [
            'total_tests' => count($test_results),
            'passed_tests' => count(array_filter($test_results, function($test) { return $test['passed']; })),
            'failed_tests' => count(array_filter($test_results, function($test) { return !$test['passed']; }))
        ]
    ];
    
    file_put_contents($filename, json_encode($report, JSON_PRETTY_PRINT));
    
    return [
        'filename' => $filename,
        'report' => $report
    ];
}
?>
