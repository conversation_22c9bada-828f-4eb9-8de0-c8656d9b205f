-- Table structure for join requests with OTP verification
CREATE TABLE `join_requests` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(100) NOT NULL,
  `pin` varchar(20) NOT NULL,
  `mobile` varchar(20) NOT NULL,
  `under_userid` varchar(100) NOT NULL,
  `matrix_position` int(11) NOT NULL,
  `otp` varchar(10) NOT NULL,
  `otp_expiry` datetime NOT NULL,
  `completion_token` varchar(100) DEFAULT NULL,
  `status` enum('pending','verified','completed','expired') NOT NULL DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`),
  KEY `under_userid` (`under_userid`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
