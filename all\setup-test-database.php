<?php
/**
 * Test Database Setup Script
 * Creates and configures the test database for MLM testing
 */

// Prevent web access
if (isset($_SERVER['HTTP_HOST'])) {
    die('This script can only be run from command line or by direct access.');
}

require_once __DIR__ . '/php-includes/config.php';

echo "=== MLM Test Database Setup ===\n";

// Get database configurations
$prod_config = Config::get('database');
$test_config = Config::get('test_database');

echo "Production DB: {$prod_config['name']}@{$prod_config['host']}:{$prod_config['port']}\n";
echo "Test DB: {$test_config['name']}@{$test_config['host']}:{$test_config['port']}\n\n";

try {
    // Connect to MySQL server (without specifying database)
    $con = mysqli_connect(
        $test_config['host'],
        $test_config['user'],
        $test_config['pass'],
        null,
        $test_config['port']
    );
    
    if (!$con) {
        throw new Exception("Failed to connect to MySQL server: " . mysqli_connect_error());
    }
    
    echo "✓ Connected to MySQL server\n";
    
    // Create test database if it doesn't exist
    $test_db_name = $test_config['name'];
    $create_db_sql = "CREATE DATABASE IF NOT EXISTS `$test_db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
    
    if (mysqli_query($con, $create_db_sql)) {
        echo "✓ Test database '$test_db_name' created/verified\n";
    } else {
        throw new Exception("Failed to create test database: " . mysqli_error($con));
    }
    
    // Select the test database
    if (!mysqli_select_db($con, $test_db_name)) {
        throw new Exception("Failed to select test database: " . mysqli_error($con));
    }
    
    echo "✓ Selected test database\n";
    
    // Read and execute the main schema
    $schema_file = __DIR__ . '/mlm.sql';
    if (!file_exists($schema_file)) {
        throw new Exception("Schema file not found: $schema_file");
    }
    
    echo "Importing main schema...\n";
    $schema_content = file_get_contents($schema_file);
    
    // Remove database creation and use statements from the schema
    $schema_content = preg_replace('/CREATE DATABASE.*?;/i', '', $schema_content);
    $schema_content = preg_replace('/USE.*?;/i', '', $schema_content);
    
    // Execute schema statements
    $statements = explode(';', $schema_content);
    $executed = 0;
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        if (mysqli_query($con, $statement)) {
            $executed++;
        } else {
            // Some statements might fail if tables already exist, that's okay
            $error = mysqli_error($con);
            if (strpos($error, 'already exists') === false && strpos($error, 'duplicate') === false) {
                echo "Warning: $error\n";
            }
        }
    }
    
    echo "✓ Executed $executed schema statements\n";
    
    // Apply security enhancements
    $migration_file = __DIR__ . '/security-migration.sql';
    if (file_exists($migration_file)) {
        echo "Applying security enhancements...\n";
        $migration_content = file_get_contents($migration_file);
        $migration_statements = explode(';', $migration_content);
        $migration_executed = 0;
        
        foreach ($migration_statements as $statement) {
            $statement = trim($statement);
            if (empty($statement) || strpos($statement, '--') === 0) {
                continue;
            }
            
            if (mysqli_query($con, $statement)) {
                $migration_executed++;
            } else {
                $error = mysqli_error($con);
                if (strpos($error, 'already exists') === false && strpos($error, 'duplicate') === false) {
                    echo "Warning: $error\n";
                }
            }
        }
        
        echo "✓ Executed $migration_executed migration statements\n";
    }
    
    // Create test-specific tables
    echo "Creating test-specific tables...\n";
    
    $test_tables = [
        "CREATE TABLE IF NOT EXISTS `test_runs` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `run_name` varchar(255) NOT NULL,
            `description` text,
            `users_created` int(11) DEFAULT 0,
            `commissions_generated` decimal(10,2) DEFAULT 0.00,
            `payouts_processed` int(11) DEFAULT 0,
            `started_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `completed_at` timestamp NULL DEFAULT NULL,
            `status` enum('running','completed','failed') DEFAULT 'running',
            PRIMARY KEY (`id`),
            KEY `idx_status` (`status`),
            KEY `idx_started_at` (`started_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4",
        
        "CREATE TABLE IF NOT EXISTS `mock_payouts` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `test_run_id` int(11) DEFAULT NULL,
            `user_id` varchar(100) NOT NULL,
            `amount` decimal(10,2) NOT NULL,
            `commission_type` varchar(50) NOT NULL,
            `status` enum('pending','success','failed') DEFAULT 'pending',
            `mock_response` json DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `processed_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `idx_test_run` (`test_run_id`),
            KEY `idx_user_id` (`user_id`),
            KEY `idx_status` (`status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4",
        
        "CREATE TABLE IF NOT EXISTS `test_matrix_snapshots` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `test_run_id` int(11) DEFAULT NULL,
            `user_id` varchar(100) NOT NULL,
            `level` int(11) NOT NULL,
            `matrix_count` int(11) NOT NULL,
            `total_downline` int(11) NOT NULL,
            `commission_earned` decimal(10,2) DEFAULT 0.00,
            `snapshot_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_test_run` (`test_run_id`),
            KEY `idx_user_level` (`user_id`, `level`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4"
    ];
    
    foreach ($test_tables as $table_sql) {
        if (mysqli_query($con, $table_sql)) {
            echo "✓ Created test table\n";
        } else {
            echo "Warning: " . mysqli_error($con) . "\n";
        }
    }
    
    // Create test admin user
    echo "Setting up test admin user...\n";
    
    $test_admin_email = '<EMAIL>';
    $test_admin_password = password_hash('TestAdmin123!', PASSWORD_DEFAULT);
    
    $admin_sql = "INSERT IGNORE INTO admin (userid, password) VALUES ('test_admin', ?)";
    $stmt = mysqli_prepare($con, $admin_sql);
    mysqli_stmt_bind_param($stmt, 's', $test_admin_password);
    
    if (mysqli_stmt_execute($stmt)) {
        echo "✓ Test admin user created (username: test_admin, password: TestAdmin123!)\n";
    }
    
    mysqli_stmt_close($stmt);
    mysqli_close($con);
    
    echo "\n=== Test Database Setup Complete ===\n";
    echo "Test database '$test_db_name' is ready for testing.\n";
    echo "To use the test database, set TESTING_MODE=true in your .env file.\n\n";
    
    echo "Next steps:\n";
    echo "1. Copy .env.example to .env if you haven't already\n";
    echo "2. Set TESTING_MODE=true in .env\n";
    echo "3. Run 'php test-seeder.php' to populate with test data\n";
    echo "4. Run 'php run-simulation.php' to test MLM logic\n\n";
    
} catch (Exception $e) {
    echo "Setup failed: " . $e->getMessage() . "\n";
    exit(1);
}
?>
