<?php
/**
 * Centralized Error Handler and Logger
 * Provides secure error handling and logging functionality
 */
class ErrorHandler {
    
    private static $con;
    private static $log_to_database = true;
    private static $log_to_file = true;
    private static $log_file_path = 'logs/error.log';
    private static $display_errors = false; // Set to true only in development
    
    /**
     * Initialize error handler with database connection
     * 
     * @param mysqli $connection Database connection
     * @param array $config Configuration options
     */
    public static function init($connection, $config = []) {
        self::$con = $connection;
        
        // Apply configuration
        if (isset($config['log_to_database'])) {
            self::$log_to_database = $config['log_to_database'];
        }
        if (isset($config['log_to_file'])) {
            self::$log_to_file = $config['log_to_file'];
        }
        if (isset($config['log_file_path'])) {
            self::$log_file_path = $config['log_file_path'];
        }
        if (isset($config['display_errors'])) {
            self::$display_errors = $config['display_errors'];
        }
        
        // Set up error handlers
        set_error_handler([self::class, 'handleError']);
        set_exception_handler([self::class, 'handleException']);
        register_shutdown_function([self::class, 'handleFatalError']);
        
        // Create logs directory if it doesn't exist
        if (self::$log_to_file) {
            $log_dir = dirname(self::$log_file_path);
            if (!is_dir($log_dir)) {
                mkdir($log_dir, 0755, true);
            }
        }
    }
    
    /**
     * Log an error with context information
     * 
     * @param string $level Error level (ERROR, WARNING, INFO, DEBUG)
     * @param string $message Error message
     * @param string $file File where error occurred
     * @param int $line Line number where error occurred
     * @param array $context Additional context information
     */
    public static function log($level, $message, $file = null, $line = null, $context = []) {
        $error_data = [
            'level' => $level,
            'message' => $message,
            'file' => $file,
            'line' => $line,
            'user_id' => $_SESSION['userid'] ?? null,
            'ip_address' => self::getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
            'context' => $context,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        // Log to database
        if (self::$log_to_database && self::$con) {
            self::logToDatabase($error_data);
        }
        
        // Log to file
        if (self::$log_to_file) {
            self::logToFile($error_data);
        }
        
        // Display error if in development mode
        if (self::$display_errors && $level === 'ERROR') {
            self::displayError($error_data);
        }
    }
    
    /**
     * Handle PHP errors
     */
    public static function handleError($severity, $message, $file, $line) {
        $level = self::getSeverityLevel($severity);
        
        self::log($level, $message, $file, $line, [
            'severity' => $severity,
            'type' => 'php_error'
        ]);
        
        // Don't execute PHP internal error handler
        return true;
    }
    
    /**
     * Handle uncaught exceptions
     */
    public static function handleException($exception) {
        self::log('ERROR', $exception->getMessage(), $exception->getFile(), $exception->getLine(), [
            'type' => 'exception',
            'class' => get_class($exception),
            'trace' => $exception->getTraceAsString()
        ]);
        
        if (self::$display_errors) {
            echo "<h1>An error occurred</h1>";
            echo "<p>Please try again later or contact support if the problem persists.</p>";
        } else {
            http_response_code(500);
            echo "Internal Server Error";
        }
    }
    
    /**
     * Handle fatal errors
     */
    public static function handleFatalError() {
        $error = error_get_last();
        
        if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
            self::log('ERROR', $error['message'], $error['file'], $error['line'], [
                'type' => 'fatal_error',
                'error_type' => $error['type']
            ]);
        }
    }
    
    /**
     * Log application-specific errors
     * 
     * @param string $message Error message
     * @param array $context Additional context
     */
    public static function logError($message, $context = []) {
        $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        $caller = $backtrace[0] ?? [];
        
        self::log('ERROR', $message, $caller['file'] ?? null, $caller['line'] ?? null, $context);
    }
    
    /**
     * Log warnings
     * 
     * @param string $message Warning message
     * @param array $context Additional context
     */
    public static function logWarning($message, $context = []) {
        $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        $caller = $backtrace[0] ?? [];
        
        self::log('WARNING', $message, $caller['file'] ?? null, $caller['line'] ?? null, $context);
    }
    
    /**
     * Log info messages
     * 
     * @param string $message Info message
     * @param array $context Additional context
     */
    public static function logInfo($message, $context = []) {
        $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        $caller = $backtrace[0] ?? [];
        
        self::log('INFO', $message, $caller['file'] ?? null, $caller['line'] ?? null, $context);
    }
    
    /**
     * Log to database
     */
    private static function logToDatabase($error_data) {
        try {
            $stmt = self::$con->prepare("INSERT INTO error_logs (level, message, file, line, user_id, ip_address, user_agent, context) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
            $context_json = json_encode($error_data['context']);
            $stmt->bind_param("ssssisss", 
                $error_data['level'],
                $error_data['message'],
                $error_data['file'],
                $error_data['line'],
                $error_data['user_id'],
                $error_data['ip_address'],
                $error_data['user_agent'],
                $context_json
            );
            $stmt->execute();
            $stmt->close();
        } catch (Exception $e) {
            // Fallback to file logging if database fails
            self::logToFile($error_data);
        }
    }
    
    /**
     * Log to file
     */
    private static function logToFile($error_data) {
        $log_entry = sprintf(
            "[%s] %s: %s in %s:%d | User: %s | IP: %s | Context: %s\n",
            $error_data['timestamp'],
            $error_data['level'],
            $error_data['message'],
            $error_data['file'] ?? 'unknown',
            $error_data['line'] ?? 0,
            $error_data['user_id'] ?? 'anonymous',
            $error_data['ip_address'],
            json_encode($error_data['context'])
        );
        
        file_put_contents(self::$log_file_path, $log_entry, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Display error for development
     */
    private static function displayError($error_data) {
        echo "<div style='background: #ffebee; border: 1px solid #f44336; padding: 10px; margin: 10px; border-radius: 5px;'>";
        echo "<h4 style='color: #d32f2f; margin: 0 0 10px 0;'>Error: {$error_data['level']}</h4>";
        echo "<p><strong>Message:</strong> " . htmlspecialchars($error_data['message']) . "</p>";
        if ($error_data['file']) {
            echo "<p><strong>File:</strong> {$error_data['file']}:{$error_data['line']}</p>";
        }
        if (!empty($error_data['context'])) {
            echo "<p><strong>Context:</strong> " . htmlspecialchars(json_encode($error_data['context'])) . "</p>";
        }
        echo "</div>";
    }
    
    /**
     * Convert PHP error severity to string
     */
    private static function getSeverityLevel($severity) {
        switch ($severity) {
            case E_ERROR:
            case E_CORE_ERROR:
            case E_COMPILE_ERROR:
            case E_USER_ERROR:
                return 'ERROR';
            case E_WARNING:
            case E_CORE_WARNING:
            case E_COMPILE_WARNING:
            case E_USER_WARNING:
                return 'WARNING';
            case E_NOTICE:
            case E_USER_NOTICE:
                return 'INFO';
            default:
                return 'DEBUG';
        }
    }
    
    /**
     * Get client IP address
     */
    private static function getClientIP() {
        $ip_keys = ['HTTP_CF_CONNECTING_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR'];
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = explode(',', $ip)[0];
                }
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
}
?>
