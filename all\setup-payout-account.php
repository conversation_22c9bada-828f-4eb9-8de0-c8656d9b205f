<?php
include('php-includes/connect.php');
include('php-includes/check-login.php');
include('php-includes/stitch-payouts.php');

$userid = $_SESSION['userid'];
$stitch = new StitchPayouts();

// Handle form submission
if(isset($_POST['setup_account'])) {
    $account_holder_name = mysqli_real_escape_string($con, $_POST['account_holder_name']);
    $account_number = mysqli_real_escape_string($con, $_POST['account_number']);
    $bank_id = mysqli_real_escape_string($con, $_POST['bank_id']);
    
    $flag = 0;
    
    if($account_holder_name != '' && $account_number != '' && $bank_id != '') {
        // Validate account number (basic validation)
        if(strlen($account_number) >= 8 && strlen($account_number) <= 12 && is_numeric($account_number)) {
            $flag = 1;
        } else {
            echo '<script>alert("Please enter a valid account number (8-12 digits).");</script>';
        }
    } else {
        echo '<script>alert("Please fill all the required fields.");</script>';
    }
    
    if($flag == 1) {
        // Get bank name
        $bank_query = mysqli_query($con, "SELECT name FROM stitch_banks WHERE stitch_bank_id='$bank_id'");
        $bank_name = '';
        if(mysqli_num_rows($bank_query) > 0) {
            $bank_result = mysqli_fetch_assoc($bank_query);
            $bank_name = $bank_result['name'];
        }
        
        // Create recipient in Stitch (for demo, we'll simulate this)
        $recipientData = [
            'name' => $account_holder_name,
            'account_number' => $account_number,
            'bank_id' => $bank_id,
            'reference' => 'MLM_USER_' . $userid
        ];
        
        // For demo purposes, simulate successful recipient creation
        $stitch_recipient_id = 'demo_recipient_' . uniqid();
        
        // Check if user already has a recipient account
        $check_query = mysqli_query($con, "SELECT * FROM stitch_recipients WHERE userid='$userid'");
        
        if(mysqli_num_rows($check_query) > 0) {
            // Update existing recipient
            $update_query = "UPDATE stitch_recipients SET 
                           stitch_recipient_id='$stitch_recipient_id',
                           recipient_name='$account_holder_name',
                           account_number='$account_number',
                           bank_id='$bank_id',
                           bank_name='$bank_name',
                           status='active',
                           updated_date=NOW()
                           WHERE userid='$userid'";
            
            if(mysqli_query($con, $update_query)) {
                echo '<script>alert("Payout account updated successfully!");</script>';
            } else {
                echo '<script>alert("Failed to update payout account. Please try again.");</script>';
            }
        } else {
            // Insert new recipient
            $insert_query = "INSERT INTO stitch_recipients 
                           (userid, stitch_recipient_id, recipient_name, account_number, bank_id, bank_name, status) 
                           VALUES ('$userid', '$stitch_recipient_id', '$account_holder_name', '$account_number', '$bank_id', '$bank_name', 'active')";
            
            if(mysqli_query($con, $insert_query)) {
                echo '<script>alert("Payout account setup successfully!");</script>';
            } else {
                echo '<script>alert("Failed to setup payout account. Please try again.");</script>';
            }
        }
    }
}

// Get user's current recipient details
$recipient_query = mysqli_query($con, "SELECT * FROM stitch_recipients WHERE userid='$userid'");
$current_recipient = null;
if(mysqli_num_rows($recipient_query) > 0) {
    $current_recipient = mysqli_fetch_assoc($recipient_query);
}

// Get available banks
$banks_query = mysqli_query($con, "SELECT * FROM stitch_banks WHERE is_active=1 ORDER BY name");
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>MLM Website - Setup Payout Account</title>

    <!-- Bootstrap Core CSS -->
    <link href="vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">

    <!-- MetisMenu CSS -->
    <link href="vendor/metisMenu/metisMenu.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="dist/css/sb-admin-2.css" rel="stylesheet">

    <!-- Custom Fonts -->
    <link href="vendor/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css">
</head>

<body>
    <div id="wrapper">
        <!-- Navigation -->
        <?php include('php-includes/menu.php'); ?>

        <!-- Page Content -->
        <div id="page-wrapper">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-lg-12">
                        <h1 class="page-header">Setup Payout Account</h1>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-8">
                        <div class="panel panel-primary">
                            <div class="panel-heading">
                                <h4><i class="fa fa-bank"></i> Bank Account Details</h4>
                            </div>
                            <div class="panel-body">
                                <?php if($current_recipient): ?>
                                <div class="alert alert-success">
                                    <i class="fa fa-check-circle"></i> You have an active payout account setup. You can update your details below if needed.
                                </div>
                                <?php else: ?>
                                <div class="alert alert-info">
                                    <i class="fa fa-info-circle"></i> Setup your bank account to receive commission payouts automatically via Stitch Payouts.
                                </div>
                                <?php endif; ?>

                                <form method="post">
                                    <div class="form-group">
                                        <label>Account Holder Name</label>
                                        <input type="text" name="account_holder_name" class="form-control" 
                                               value="<?php echo $current_recipient['recipient_name'] ?? ''; ?>" required>
                                        <p class="help-block">Full name as it appears on your bank account</p>
                                    </div>

                                    <div class="form-group">
                                        <label>Bank</label>
                                        <select name="bank_id" class="form-control" required>
                                            <option value="">Select Your Bank</option>
                                            <?php while($bank = mysqli_fetch_assoc($banks_query)): ?>
                                            <option value="<?php echo $bank['stitch_bank_id']; ?>" 
                                                    <?php echo ($current_recipient && $current_recipient['bank_id'] == $bank['stitch_bank_id']) ? 'selected' : ''; ?>>
                                                <?php echo $bank['name']; ?>
                                            </option>
                                            <?php endwhile; ?>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label>Account Number</label>
                                        <input type="text" name="account_number" class="form-control" 
                                               value="<?php echo $current_recipient['account_number'] ?? ''; ?>" 
                                               placeholder="Enter your account number" required>
                                        <p class="help-block">Your bank account number (8-12 digits)</p>
                                    </div>

                                    <div class="alert alert-warning">
                                        <i class="fa fa-shield"></i> <strong>Security Notice:</strong> Your banking details are processed securely through Stitch Payouts. We do not store your sensitive banking information on our servers.
                                    </div>

                                    <div class="form-group">
                                        <button type="submit" name="setup_account" class="btn btn-primary btn-lg">
                                            <i class="fa fa-save"></i> 
                                            <?php echo $current_recipient ? 'Update' : 'Setup'; ?> Payout Account
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h4><i class="fa fa-info-circle"></i> About Stitch Payouts</h4>
                            </div>
                            <div class="panel-body">
                                <h5>Secure & Automated</h5>
                                <p>Stitch Payouts provides secure, automated payment processing for your MLM commissions.</p>

                                <h5>Fast Processing</h5>
                                <p>Payments are processed in batches and typically arrive within 1-2 business days.</p>

                                <h5>No Hidden Fees</h5>
                                <p>Transparent pricing with no hidden fees. You'll know exactly what you're receiving.</p>

                                <h5>Bank Support</h5>
                                <p>Supports all major South African banks including ABSA, Standard Bank, FNB, Nedbank, and more.</p>
                            </div>
                        </div>

                        <?php if($current_recipient): ?>
                        <div class="panel panel-success">
                            <div class="panel-heading">
                                <h4><i class="fa fa-check"></i> Current Account</h4>
                            </div>
                            <div class="panel-body">
                                <table class="table table-bordered">
                                    <tr>
                                        <th>Name:</th>
                                        <td><?php echo $current_recipient['recipient_name']; ?></td>
                                    </tr>
                                    <tr>
                                        <th>Bank:</th>
                                        <td><?php echo $current_recipient['bank_name']; ?></td>
                                    </tr>
                                    <tr>
                                        <th>Account:</th>
                                        <td>****<?php echo substr($current_recipient['account_number'], -4); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Status:</th>
                                        <td><span class="label label-success"><?php echo ucfirst($current_recipient['status']); ?></span></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="vendor/jquery/jquery.min.js"></script>

    <!-- Bootstrap Core JavaScript -->
    <script src="vendor/bootstrap/js/bootstrap.min.js"></script>

    <!-- Metis Menu Plugin JavaScript -->
    <script src="vendor/metisMenu/metisMenu.min.js"></script>

    <!-- Custom Theme JavaScript -->
    <script src="dist/js/sb-admin-2.js"></script>
</body>
</html>
