<?php
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\Exception;

// Load Composer's autoloader if you're using Composer
// require 'vendor/autoload.php';

// If not using <PERSON>, include the PHPMailer files directly
require __DIR__ . '/PHPMailer/src/Exception.php';
require __DIR__ . '/PHPMailer/src/PHPMailer.php';
require __DIR__ . '/PHPMailer/src/SMTP.php';

// Include message queue for asynchronous processing
require_once __DIR__ . '/message-queue.php';

// Email configuration - store these in a separate config file in production
$EMAIL_CONFIG = [
    'host' => 'smtp.gmail.com',
    'username' => '<EMAIL>', // Replace with your Gmail address
    'password' => 'your-app-password',    // Replace with your Gmail App Password
    'from_email' => '<EMAIL>',
    'from_name' => 'MLM System',
    'port' => 587,
    'encryption' => PHPMailer::ENC<PERSON>YPTION_STARTTLS
];

/**
 * Send an email using PHPMailer and Gmail SMTP
 *
 * @param string $to Recipient email address
 * @param string $subject Email subject
 * @param string $message Email body (HTML)
 * @param string $plain_message Plain text version of the message (optional)
 * @param array $attachments Array of file paths to attach (optional)
 * @return bool True if email sent successfully, false otherwise
 */
function sendEmail($to, $subject, $message, $plain_message = '', $attachments = []) {
    global $EMAIL_CONFIG;

    // For testing purposes, we'll just log the email instead of sending it
    $log_message = "==========\n";
    $log_message .= "Date: " . date('Y-m-d H:i:s') . "\n";
    $log_message .= "To: $to\n";
    $log_message .= "From: {$EMAIL_CONFIG['from_name']} <{$EMAIL_CONFIG['from_email']}>\n";
    $log_message .= "Subject: $subject\n";
    $log_message .= "Message:\n$message\n";
    $log_message .= "==========\n\n";

    file_put_contents('email_log.txt', $log_message, FILE_APPEND);

    // In a real implementation, you would use PHPMailer to send the email
    // For now, we'll just return true to simulate successful sending
    return true;

    /* Real implementation would look like this:
    $mail = new PHPMailer(true);

    try {
        // Server settings
        $mail->isSMTP();
        $mail->Host       = $EMAIL_CONFIG['host'];
        $mail->SMTPAuth   = true;
        $mail->Username   = $EMAIL_CONFIG['username'];
        $mail->Password   = $EMAIL_CONFIG['password'];
        $mail->SMTPSecure = $EMAIL_CONFIG['encryption'];
        $mail->Port       = $EMAIL_CONFIG['port'];

        // Recipients
        $mail->setFrom($EMAIL_CONFIG['from_email'], $EMAIL_CONFIG['from_name']);
        $mail->addAddress($to);

        // Content
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body    = $message;
        $mail->AltBody = !empty($plain_message) ? $plain_message : strip_tags($message);

        // Attachments
        if (!empty($attachments)) {
            foreach ($attachments as $attachment) {
                if (file_exists($attachment)) {
                    $mail->addAttachment($attachment);
                }
            }
        }

        $mail->send();
        return true;
    } catch (Exception $e) {
        // Log the error
        error_log("Email Error: {$mail->ErrorInfo}");
        return false;
    }
    */
}

/**
 * Send a registration completion email with a token link
 *
 * @param string $to Recipient email address
 * @param string $token Unique token for registration completion
 * @return bool True if email sent successfully, false otherwise
 */
function sendRegistrationEmail($to, $token) {
    $base_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST'];
    $completion_link = $base_url . "/complete-registration.php?token=" . $token;

    $subject = "Complete Your MLM Registration";

    $message = "
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #4CAF50; color: white; padding: 10px; text-align: center; }
            .content { padding: 20px; background-color: #f9f9f9; border: 1px solid #ddd; }
            .button { display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px;
                      text-decoration: none; border-radius: 5px; margin: 20px 0; }
            .footer { font-size: 12px; color: #777; margin-top: 20px; text-align: center; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h2>Welcome to Our MLM Platform!</h2>
            </div>
            <div class='content'>
                <p>Thank you for joining our MLM platform. You're just one step away from completing your registration.</p>
                <p>Please click the button below to set up your account and payment details:</p>
                <p style='text-align: center;'>
                    <a href='$completion_link' class='button'>Complete Registration</a>
                </p>
                <p><strong>Important:</strong> This link will expire in 24 hours.</p>
                <p>If you did not request to join our platform, please ignore this email.</p>
            </div>
            <div class='footer'>
                <p>© " . date('Y') . " MLM System. All rights reserved.</p>
                <p>If you have any questions, please contact support.</p>
            </div>
        </div>
    </body>
    </html>";

    $plain_message = "Welcome to Our MLM Platform!\n\n" .
                    "Thank you for joining our MLM platform. You're just one step away from completing your registration.\n\n" .
                    "Please click the link below to set up your account and payment details:\n" .
                    "$completion_link\n\n" .
                    "Important: This link will expire in 24 hours.\n\n" .
                    "If you did not request to join our platform, please ignore this email.\n\n" .
                    "© " . date('Y') . " MLM System. All rights reserved.";

    return sendEmail($to, $subject, $message, $plain_message);
}
?>
