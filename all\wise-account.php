<?php
require('php-includes/connect.php');
include('php-includes/check-login.php');
include('php-includes/wise-api.php');
$userid = $_SESSION['userid'];

// Initialize Wise API
$wise = new WiseAPI();

// Handle form submission
if(isset($_POST['save_account'])) {
    $account_number = mysqli_real_escape_string($con, $_POST['account_number']);
    $account_type = mysqli_real_escape_string($con, $_POST['account_type']);
    $bank_code = mysqli_real_escape_string($con, $_POST['bank_code']);
    $first_name = mysqli_real_escape_string($con, $_POST['first_name']);
    $last_name = mysqli_real_escape_string($con, $_POST['last_name']);
    $address_line1 = mysqli_real_escape_string($con, $_POST['address_line1']);
    $city = mysqli_real_escape_string($con, $_POST['city']);
    $postal_code = mysqli_real_escape_string($con, $_POST['postal_code']);
    $country = mysqli_real_escape_string($con, $_POST['country']);
    $currency = mysqli_real_escape_string($con, $_POST['currency']);
    
    // Prepare recipient account details
    $accountDetails = [
        'currency' => $currency,
        'type' => 'bank_account',
        'accountHolderName' => $first_name . ' ' . $last_name,
        'details' => [
            'accountNumber' => $account_number,
            'accountType' => $account_type,
            'bankCode' => $bank_code,
            'address' => [
                'country' => $country,
                'city' => $city,
                'postCode' => $postal_code,
                'firstLine' => $address_line1
            ]
        ]
    ];
    
    // For demonstration purposes, we'll just store the account details
    // In a real implementation, you would call the Wise API to create the recipient
    // $response = $wise->createRecipient($accountDetails);
    
    // For now, we'll simulate a successful response
    $recipient_id = 'demo_' . uniqid();
    
    // Check if recipient already exists for this user
    $check_query = mysqli_query($con, "SELECT * FROM wise_recipients WHERE userid='$userid'");
    
    if(mysqli_num_rows($check_query) > 0) {
        // Update existing recipient
        $update = mysqli_query($con, "UPDATE wise_recipients SET 
            recipient_id = '$recipient_id',
            currency = '$currency',
            account_details = '" . json_encode($accountDetails) . "'
            WHERE userid = '$userid'");
            
        if($update) {
            $success_message = "Your Wise account details have been updated successfully.";
        } else {
            $error_message = "Error updating account details: " . mysqli_error($con);
        }
    } else {
        // Insert new recipient
        $insert = mysqli_query($con, "INSERT INTO wise_recipients 
            (userid, recipient_id, currency, account_details) 
            VALUES ('$userid', '$recipient_id', '$currency', '" . json_encode($accountDetails) . "')");
            
        if($insert) {
            $success_message = "Your Wise account details have been saved successfully.";
        } else {
            $error_message = "Error saving account details: " . mysqli_error($con);
        }
    }
}

// Get user's current Wise account details
$recipient_query = mysqli_query($con, "SELECT * FROM wise_recipients WHERE userid='$userid'");
$recipient = mysqli_num_rows($recipient_query) > 0 ? mysqli_fetch_assoc($recipient_query) : null;

// Parse account details if available
$account_details = null;
if($recipient && isset($recipient['account_details'])) {
    $account_details = json_decode($recipient['account_details'], true);
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>MLM Website - Wise Account Setup</title>

    <!-- Bootstrap Core CSS -->
    <link href="vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">

    <!-- MetisMenu CSS -->
    <link href="vendor/metisMenu/metisMenu.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="dist/css/sb-admin-2.css" rel="stylesheet">

    <!-- Custom Fonts -->
    <link href="vendor/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css">
</head>

<body>

    <div id="wrapper">

        <!-- Navigation -->
        <?php include('php-includes/menu.php'); ?>

        <!-- Page Content -->
        <div id="page-wrapper">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-lg-12">
                        <h1 class="page-header">Wise Account Setup</h1>
                    </div>
                    <!-- /.col-lg-12 -->
                </div>
                <!-- /.row -->
                
                <?php if(isset($success_message)): ?>
                <div class="alert alert-success">
                    <?php echo $success_message; ?>
                </div>
                <?php endif; ?>
                
                <?php if(isset($error_message)): ?>
                <div class="alert alert-danger">
                    <?php echo $error_message; ?>
                </div>
                <?php endif; ?>
                
                <div class="row">
                    <div class="col-lg-8">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                Your Wise Payment Account Details
                            </div>
                            <div class="panel-body">
                                <form method="post" action="">
                                    <div class="row">
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label>Currency</label>
                                                <select class="form-control" name="currency" required>
                                                    <option value="USD" <?php echo ($recipient && $recipient['currency'] == 'USD') ? 'selected' : ''; ?>>USD</option>
                                                    <option value="EUR" <?php echo ($recipient && $recipient['currency'] == 'EUR') ? 'selected' : ''; ?>>EUR</option>
                                                    <option value="GBP" <?php echo ($recipient && $recipient['currency'] == 'GBP') ? 'selected' : ''; ?>>GBP</option>
                                                    <option value="CAD" <?php echo ($recipient && $recipient['currency'] == 'CAD') ? 'selected' : ''; ?>>CAD</option>
                                                    <option value="AUD" <?php echo ($recipient && $recipient['currency'] == 'AUD') ? 'selected' : ''; ?>>AUD</option>
                                                    <option value="ZAR" <?php echo ($recipient && $recipient['currency'] == 'ZAR') ? 'selected' : ''; ?>>ZAR</option>
                                                </select>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label>Account Number</label>
                                                <input type="text" class="form-control" name="account_number" 
                                                    value="<?php echo $account_details ? $account_details['details']['accountNumber'] : ''; ?>" required>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label>Account Type</label>
                                                <select class="form-control" name="account_type">
                                                    <option value="checking" <?php echo ($account_details && $account_details['details']['accountType'] == 'checking') ? 'selected' : ''; ?>>Checking</option>
                                                    <option value="savings" <?php echo ($account_details && $account_details['details']['accountType'] == 'savings') ? 'selected' : ''; ?>>Savings</option>
                                                </select>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label>Bank Code / Routing Number</label>
                                                <input type="text" class="form-control" name="bank_code" 
                                                    value="<?php echo $account_details ? $account_details['details']['bankCode'] : ''; ?>" required>
                                            </div>
                                        </div>
                                        
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label>First Name</label>
                                                <input type="text" class="form-control" name="first_name" 
                                                    value="<?php echo $account_details ? explode(' ', $account_details['accountHolderName'])[0] : ''; ?>" required>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label>Last Name</label>
                                                <input type="text" class="form-control" name="last_name" 
                                                    value="<?php echo $account_details ? explode(' ', $account_details['accountHolderName'])[1] : ''; ?>" required>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label>Address Line 1</label>
                                                <input type="text" class="form-control" name="address_line1" 
                                                    value="<?php echo $account_details ? $account_details['details']['address']['firstLine'] : ''; ?>" required>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label>City</label>
                                                <input type="text" class="form-control" name="city" 
                                                    value="<?php echo $account_details ? $account_details['details']['address']['city'] : ''; ?>" required>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label>Postal Code</label>
                                                <input type="text" class="form-control" name="postal_code" 
                                                    value="<?php echo $account_details ? $account_details['details']['address']['postCode'] : ''; ?>" required>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label>Country</label>
                                                <select class="form-control" name="country" required>
                                                    <option value="US" <?php echo ($account_details && $account_details['details']['address']['country'] == 'US') ? 'selected' : ''; ?>>United States</option>
                                                    <option value="GB" <?php echo ($account_details && $account_details['details']['address']['country'] == 'GB') ? 'selected' : ''; ?>>United Kingdom</option>
                                                    <option value="CA" <?php echo ($account_details && $account_details['details']['address']['country'] == 'CA') ? 'selected' : ''; ?>>Canada</option>
                                                    <option value="AU" <?php echo ($account_details && $account_details['details']['address']['country'] == 'AU') ? 'selected' : ''; ?>>Australia</option>
                                                    <option value="ZA" <?php echo ($account_details && $account_details['details']['address']['country'] == 'ZA') ? 'selected' : ''; ?>>South Africa</option>
                                                    <option value="DE" <?php echo ($account_details && $account_details['details']['address']['country'] == 'DE') ? 'selected' : ''; ?>>Germany</option>
                                                    <option value="FR" <?php echo ($account_details && $account_details['details']['address']['country'] == 'FR') ? 'selected' : ''; ?>>France</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <button type="submit" name="save_account" class="btn btn-primary">Save Account Details</button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4">
                        <div class="panel panel-info">
                            <div class="panel-heading">
                                About Wise Payments
                            </div>
                            <div class="panel-body">
                                <p>Wise (formerly TransferWise) is a global technology company that's building the best way to move money around the world.</p>
                                
                                <p>By setting up your Wise account details, you'll be able to receive your MLM matrix earnings directly to your bank account through Wise's secure payment system.</p>
                                
                                <h4>Benefits:</h4>
                                <ul>
                                    <li>Fast transfers to your bank account</li>
                                    <li>Lower fees compared to traditional bank transfers</li>
                                    <li>Transparent exchange rates</li>
                                    <li>Secure payment processing</li>
                                </ul>
                                
                                <p>Please ensure your bank account details are accurate to avoid payment delays.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- /.row -->
            </div>
            <!-- /.container-fluid -->
        </div>
        <!-- /#page-wrapper -->

    </div>
    <!-- /#wrapper -->

    <!-- jQuery -->
    <script src="vendor/jquery/jquery.min.js"></script>

    <!-- Bootstrap Core JavaScript -->
    <script src="vendor/bootstrap/js/bootstrap.min.js"></script>

    <!-- Metis Menu Plugin JavaScript -->
    <script src="vendor/metisMenu/metisMenu.min.js"></script>

    <!-- Custom Theme JavaScript -->
    <script src="dist/js/sb-admin-2.js"></script>

</body>

</html>
