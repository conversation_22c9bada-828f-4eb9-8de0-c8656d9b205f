<?php
/**
 * Automated Payout Processor
 * This script processes pending commissions and creates Stitch payout batches
 * Can be run manually or as a scheduled cron job
 */

// Prevent direct browser access for security
if (php_sapi_name() !== 'cli' && !isset($_GET['manual_run'])) {
    die('This script should be run from command line or with manual_run parameter');
}

include('php-includes/connect.php');
include('php-includes/commission-processor.php');

// Configuration
$BATCH_SIZE = 50;           // Maximum number of users per batch
$MINIMUM_PAYOUT = 100.00;   // Minimum amount to trigger payout (R100)
$LOG_FILE = 'logs/payout_processing.log';

// Ensure logs directory exists
if (!file_exists('logs')) {
    mkdir('logs', 0755, true);
}

/**
 * Log message with timestamp
 */
function logMessage($message) {
    global $LOG_FILE;
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] $message" . PHP_EOL;
    file_put_contents($LOG_FILE, $logEntry, FILE_APPEND | LOCK_EX);
    
    // Also output to console if running from CLI
    if (php_sapi_name() === 'cli') {
        echo $logEntry;
    }
}

/**
 * Send notification email about payout processing
 */
function sendNotificationEmail($subject, $message) {
    // You can implement email notification here
    // For now, just log it
    logMessage("EMAIL NOTIFICATION: $subject - $message");
}

// Start processing
logMessage("=== Starting Payout Processing ===");

try {
    // Initialize commission processor
    $processor = new CommissionProcessor($con);
    
    // Process pending commissions
    logMessage("Processing pending commissions...");
    $result = $processor->processPendingCommissions($BATCH_SIZE, $MINIMUM_PAYOUT);
    
    if ($result['success']) {
        logMessage("Successfully processed {$result['processed_count']} commissions");
        logMessage("Total amount: R" . number_format($result['total_amount'], 2));
        logMessage("Batch ID: {$result['batch_id']}");
        
        // Send success notification
        $subject = "MLM Payouts Processed Successfully";
        $message = "Processed {$result['processed_count']} commissions totaling R" . number_format($result['total_amount'], 2);
        sendNotificationEmail($subject, $message);
        
        // Store the batch ID for status checking
        file_put_contents('logs/last_batch_id.txt', $result['batch_id']);
        
    } else {
        logMessage("No commissions processed");
        if (!empty($result['errors'])) {
            foreach ($result['errors'] as $error) {
                logMessage("ERROR: $error");
            }
        }
    }
    
    // Check status of recent batches
    logMessage("Checking status of recent batches...");
    
    $recentBatchesQuery = mysqli_query($con, "
        SELECT batch_id, reference, status, created_date 
        FROM stitch_payout_batches 
        WHERE created_date >= DATE_SUB(NOW(), INTERVAL 7 DAY) 
        AND status IN ('created', 'processing')
        ORDER BY created_date DESC
    ");
    
    while ($batch = mysqli_fetch_assoc($recentBatchesQuery)) {
        logMessage("Checking status for batch: {$batch['batch_id']}");
        
        $statusResult = $processor->updatePayoutStatuses($batch['batch_id']);
        
        if ($statusResult['success']) {
            logMessage("Updated {$statusResult['updated_count']} payout statuses for batch {$batch['batch_id']}");
        } else {
            logMessage("Failed to update status for batch {$batch['batch_id']}");
            foreach ($statusResult['errors'] as $error) {
                logMessage("ERROR: $error");
            }
        }
    }
    
    // Generate summary statistics
    logMessage("Generating summary statistics...");
    
    $statsQuery = mysqli_query($con, "
        SELECT 
            COUNT(*) as total_pending,
            SUM(amount) as total_pending_amount,
            (SELECT COUNT(*) FROM commission_queue WHERE status='paid' AND DATE(processed_date) = CURDATE()) as today_paid,
            (SELECT SUM(amount) FROM commission_queue WHERE status='paid' AND DATE(processed_date) = CURDATE()) as today_paid_amount
        FROM commission_queue 
        WHERE status='pending'
    ");
    
    if ($stats = mysqli_fetch_assoc($statsQuery)) {
        logMessage("=== SUMMARY STATISTICS ===");
        logMessage("Pending commissions: {$stats['total_pending']} (R" . number_format($stats['total_pending_amount'] ?? 0, 2) . ")");
        logMessage("Paid today: {$stats['today_paid']} (R" . number_format($stats['today_paid_amount'] ?? 0, 2) . ")");
    }
    
} catch (Exception $e) {
    logMessage("FATAL ERROR: " . $e->getMessage());
    
    // Send error notification
    $subject = "MLM Payout Processing Error";
    $message = "Fatal error during payout processing: " . $e->getMessage();
    sendNotificationEmail($subject, $message);
}

logMessage("=== Payout Processing Complete ===");

// If running manually, show results in browser
if (isset($_GET['manual_run'])) {
    echo "<h2>Payout Processing Complete</h2>";
    echo "<p>Check the log file for details: $LOG_FILE</p>";
    
    if (file_exists($LOG_FILE)) {
        echo "<h3>Recent Log Entries:</h3>";
        echo "<pre>";
        $logContent = file_get_contents($LOG_FILE);
        $lines = explode("\n", $logContent);
        $recentLines = array_slice($lines, -50); // Show last 50 lines
        echo htmlspecialchars(implode("\n", $recentLines));
        echo "</pre>";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Payout Processing</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <?php if (isset($_GET['manual_run'])): ?>
    <h1>MLM Payout Processing</h1>
    
    <div class="info">
        <h3>Processing Configuration:</h3>
        <ul>
            <li>Batch Size: <?php echo $BATCH_SIZE; ?> users</li>
            <li>Minimum Payout: R<?php echo number_format($MINIMUM_PAYOUT, 2); ?></li>
            <li>Log File: <?php echo $LOG_FILE; ?></li>
        </ul>
    </div>
    
    <div class="info">
        <h3>How to Setup Automated Processing:</h3>
        <p>Add this to your crontab to run every hour:</p>
        <code>0 * * * * /usr/bin/php <?php echo realpath(__FILE__); ?></code>
        
        <p>Or run daily at 2 AM:</p>
        <code>0 2 * * * /usr/bin/php <?php echo realpath(__FILE__); ?></code>
    </div>
    
    <div class="info">
        <h3>Manual Processing:</h3>
        <p><a href="?manual_run=1">Run Payout Processing Now</a></p>
    </div>
    
    <?php endif; ?>
</body>
</html>
