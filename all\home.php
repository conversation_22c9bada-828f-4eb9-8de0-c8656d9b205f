<?php
// Enhanced security and session management
include('php-includes/check-login.php');
include('php-includes/connect.php');
require_once 'php-includes/localization.php';
require_once 'php-includes/accessibility.php';

// Initialize localization and accessibility
Localization::init();
Accessibility::init();

$userid = $_SESSION['userid'];

// Get user information with enhanced security
$user_query = mysqli_prepare($con, "SELECT * FROM user WHERE email = ?");
mysqli_stmt_bind_param($user_query, 's', $userid);
mysqli_stmt_execute($user_query);
$user_result = mysqli_stmt_get_result($user_query);
$user_info = mysqli_fetch_array($user_result);
?>
<!DOCTYPE html>
<html lang="<?php echo Localization::getCurrentLanguage(); ?>">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="Ubuntu Wealth Network - Member Dashboard">
    <meta name="author" content="Ubuntu Wealth Network">

    <title><?php echo __('dashboard'); ?> - Ubuntu Wealth Network</title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Ubuntu:wght@300;400;500;700&family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Legacy CSS for compatibility -->
    <link href="vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="vendor/metisMenu/metisMenu.min.css" rel="stylesheet">
    <link href="dist/css/sb-admin-2.css" rel="stylesheet">
    <link href="vendor/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css">

    <!-- Mobile-First Ubuntu CSS -->
    <link href="dist/css/ubuntu-mobile.css" rel="stylesheet">

    <style>
        <?php echo Accessibility::generateCSS(); ?>

        :root {
            --sa-green: #007749;
            --sa-gold: #FFB612;
            --sa-blue: #002395;
            --sa-red: #DE3831;
            --ubuntu-orange: #E95420;
            --warm-white: #FFF8F0;
            --text-dark: #2C3E50;
            --text-light: #6C757D;
            --success-green: #28a745;
            --warning-orange: #fd7e14;
        }

        body {
            font-family: 'Open Sans', sans-serif;
            background: linear-gradient(135deg, var(--warm-white) 0%, #ffffff 100%);
            color: var(--text-dark);
        }

        .ubuntu-font {
            font-family: 'Ubuntu', sans-serif;
        }

        /* Header */
        .header {
            background: linear-gradient(90deg, var(--sa-green) 0%, var(--sa-blue) 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-family: 'Ubuntu', sans-serif;
            font-size: 1.8rem;
            margin: 0;
        }

        .user-info {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        /* Dashboard Cards */
        .dashboard-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            border: none;
            height: 100%;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }

        .card-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .card-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .card-label {
            color: var(--text-light);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Stats Cards */
        .stats-monthly {
            background: linear-gradient(135deg, var(--sa-blue) 0%, #6610f2 100%);
            color: white;
        }

        .stats-current {
            background: linear-gradient(135deg, var(--success-green) 0%, #20c997 100%);
            color: white;
        }

        .stats-total {
            background: linear-gradient(135deg, var(--sa-red) 0%, #dc3545 100%);
            color: white;
        }

        .stats-pins {
            background: linear-gradient(135deg, var(--sa-gold) 0%, var(--warning-orange) 100%);
            color: white;
        }

        /* SA Flag Accent */
        .sa-accent {
            background: linear-gradient(90deg,
                var(--sa-green) 0%,
                var(--sa-gold) 25%,
                var(--sa-blue) 50%,
                var(--sa-red) 75%,
                var(--sa-green) 100%);
            height: 4px;
            width: 100%;
        }

        /* Motivational Elements */
        .motivation-card {
            background: linear-gradient(135deg, var(--sa-green) 0%, var(--sa-blue) 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            margin-bottom: 2rem;
        }

        .motivation-quote {
            font-size: 1.1rem;
            font-style: italic;
            margin-bottom: 0.5rem;
        }

        /* Progress Bars */
        .progress {
            height: 10px;
            border-radius: 10px;
            background: #e9ecef;
        }

        .progress-bar {
            background: linear-gradient(90deg, var(--sa-green) 0%, var(--success-green) 100%);
            border-radius: 10px;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 1.4rem;
            }

            .card-value {
                font-size: 1.5rem;
            }

            .dashboard-card {
                padding: 1rem;
            }
        }
    </style>
</head>

<body>
    <!-- Accessibility Toolbar -->
    <?php echo Accessibility::generateToolbar(); ?>

    <!-- SA Flag Accent -->
    <div class="sa-accent"></div>

    <div id="wrapper">
        <!-- Navigation - Preserve existing menu -->
        <?php include('php-includes/menu.php'); ?>

        <!-- Page Content -->
        <div id="page-wrapper">
            <!-- Header -->
            <div class="header">
                <div class="container-fluid">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h1>
                                <i class="fas fa-hands-helping me-2"></i>
                                <?php echo __('ubuntu_wealth_network'); ?>
                            </h1>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <div class="user-info">
                                <i class="fas fa-user me-1"></i>
                                <?php echo __('welcome'); ?>, <?php echo htmlspecialchars($user_info['email'] ?? $userid); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="container-fluid py-4">
                <!-- Motivational Quote -->
                <div class="row">
                    <div class="col-12">
                        <div class="motivation-card">
                            <div class="motivation-quote">
                                "<?php echo __('ubuntu_philosophy'); ?>. <?php echo __('build_wealth_together'); ?>!"
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Dashboard Stats - Preserve existing data queries -->
                <div class="row g-3 mb-4">
                    <?php
                    // Preserve original queries with enhanced security
                    $query = mysqli_prepare($con, "SELECT * FROM income WHERE userid = ?");
                    mysqli_stmt_bind_param($query, 's', $userid);
                    mysqli_stmt_execute($query);
                    $income_result = mysqli_stmt_get_result($query);
                    $result = mysqli_fetch_array($income_result);

                    // Get tree data
                    $tree_query = mysqli_prepare($con, "SELECT * FROM tree WHERE userid = ?");
                    mysqli_stmt_bind_param($tree_query, 's', $userid);
                    mysqli_stmt_execute($tree_query);
                    $tree_result_set = mysqli_stmt_get_result($tree_query);
                    $tree_result = mysqli_fetch_array($tree_result_set);
                    $matrix_count = $tree_result['matrix_count'] ?? 0;
                    $matrix_level = $result['matrix_level'] ?? 1;

                    // Get available pins count
                    $pin_query = mysqli_prepare($con, "SELECT COUNT(*) as pin_count FROM pin_list WHERE userid = ? AND status = 'open'");
                    mysqli_stmt_bind_param($pin_query, 's', $userid);
                    mysqli_stmt_execute($pin_query);
                    $pin_result_set = mysqli_stmt_get_result($pin_query);
                    $pin_result = mysqli_fetch_array($pin_result_set);
                    $available_pins = $pin_result['pin_count'] ?? 0;
                    ?>

                    <div class="col-6 col-md-3">
                        <div class="dashboard-card stats-monthly">
                            <div class="card-icon">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <div class="card-value"><?php echo currency($result['month_bal'] ?? 0); ?></div>
                            <div class="card-label"><?php echo __('Monthly Income'); ?></div>
                        </div>
                    </div>
                    <div class="col-6 col-md-3">
                        <div class="dashboard-card stats-current">
                            <div class="card-icon">
                                <i class="fas fa-wallet"></i>
                            </div>
                            <div class="card-value"><?php echo currency($result['current_bal'] ?? 0); ?></div>
                            <div class="card-label"><?php echo __('Current Balance'); ?></div>
                        </div>
                    </div>
                    <div class="col-6 col-md-3">
                        <div class="dashboard-card stats-total">
                            <div class="card-icon">
                                <i class="fas fa-coins"></i>
                            </div>
                            <div class="card-value"><?php echo currency($result['total_bal'] ?? 0); ?></div>
                            <div class="card-label"><?php echo __('total_earnings'); ?></div>
                        </div>
                    </div>
                    <div class="col-6 col-md-3">
                        <div class="dashboard-card stats-pins">
                            <div class="card-icon">
                                <i class="fas fa-key"></i>
                            </div>
                            <div class="card-value"><?php echo $available_pins; ?></div>
                            <div class="card-label"><?php echo __('Available Pins'); ?></div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-6">
                        <div class="panel panel-primary">
                            <div class="panel-heading">
                                <h4 class="panel-title">5×5 Matrix Status</h4>
                            </div>
                            <div class="panel-body">
                                <p><strong>Matrix Level:</strong> <?php echo $matrix_level; ?></p>
                                <p><strong>Total Downline:</strong> <?php echo $matrix_count; ?></p>
                                <p><strong>Matrix Earnings:</strong> R <?php echo $result['matrix_earnings']; ?></p>

                                <?php
                                // Get next level requirements
                                $next_level = $matrix_level + 1;
                                $next_level_members = 0;

                                if($next_level == 1) {
                                    $next_level_members = 5;
                                } else if($next_level == 2) {
                                    $next_level_members = 30;
                                } else if($next_level == 3) {
                                    $next_level_members = 155;
                                } else if($next_level == 4) {
                                    $next_level_members = 780;
                                } else if($next_level == 5) {
                                    $next_level_members = 3905;
                                }

                                if($next_level <= 5) {
                                    $members_needed = $next_level_members - $matrix_count;
                                    echo "<p><strong>Next Level:</strong> Need $members_needed more members to reach Level $next_level</p>";

                                    // Get commission for next level
                                    $comm_query = mysqli_query($con, "SELECT commission_amount FROM matrix_commission WHERE level='$next_level'");
                                    $comm_result = mysqli_fetch_array($comm_query);
                                    $next_commission = $comm_result['commission_amount'];

                                    echo "<p><strong>Next Level Commission:</strong> R " . number_format($next_commission) . "</p>";
                                } else {
                                    echo "<p><strong>Status:</strong> You have reached the maximum matrix level!</p>";
                                }
                                ?>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="panel panel-info">
                            <div class="panel-heading">
                                <h4 class="panel-title">Loan & Maintenance Status</h4>
                            </div>
                            <div class="panel-body">
                                <?php
                                // Get loan information
$loan_query = mysqli_query($con, "SELECT * FROM loan WHERE userid='$userid'");
                                $loan_result = mysqli_fetch_array($loan_query);
                                $loan_status = (is_array($loan_result) && isset($loan_result['status'])) ? $loan_result['status'] : 'N/A';
                                $loan_remaining = (is_array($loan_result) && isset($loan_result['remaining_amount'])) ? $loan_result['remaining_amount'] : '0';

                                echo "<p><strong>Loan Status:</strong> " . ucfirst($loan_status) . "</p>";
                                echo "<p><strong>Remaining Amount:</strong> R " . $loan_remaining . "</p>";
                                echo "<p><strong>Loan Repaid:</strong> R " . ($result ? $result['loan_repaid'] : '0') . "</p>";
                                echo "<p><strong>Maintenance Paid:</strong> R " . ($result ? $result['maintenance_paid'] : '0') . "</p>";

                                // Get latest maintenance fee payment
                                $maint_query = mysqli_query($con, "SELECT * FROM maintenance_fee WHERE userid='$userid' ORDER BY month DESC LIMIT 1");
                                if(mysqli_num_rows($maint_query) > 0) {
                                    $maint_result = mysqli_fetch_array($maint_query);
                                    $last_month = $maint_result['month'];
                                    $last_payment = $maint_result['date_paid'];

                                    echo "<p><strong>Last Maintenance Payment:</strong> Month $last_month on $last_payment</p>";
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- /.container-fluid -->
        </div>
        <!-- /#page-wrapper -->

    </div>
    <!-- /#wrapper -->

    <!-- jQuery -->
    <script src="vendor/jquery/jquery.min.js"></script>

    <!-- Bootstrap Core JavaScript -->
    <script src="vendor/bootstrap/js/bootstrap.min.js"></script>

    <!-- Metis Menu Plugin JavaScript -->
    <script src="vendor/metisMenu/metisMenu.min.js"></script>

    <!-- Custom Theme JavaScript -->
    <script src="dist/js/sb-admin-2.js"></script>

</body>

</html>
