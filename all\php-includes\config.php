<?php
/**
 * Configuration Management
 * Handles environment variables and application configuration
 */

class Config {
    private static $config = [];
    private static $loaded = false;
    
    /**
     * Load configuration from environment file
     */
    public static function load() {
        if (self::$loaded) {
            return;
        }
        
        // Load .env file if it exists
        $env_file = __DIR__ . '/../.env';
        if (file_exists($env_file)) {
            $lines = file($env_file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                if (strpos($line, '#') === 0) {
                    continue; // Skip comments
                }
                
                if (strpos($line, '=') !== false) {
                    list($key, $value) = explode('=', $line, 2);
                    $key = trim($key);
                    $value = trim($value, '"\'');
                    
                    // Set environment variable if not already set
                    if (!isset($_ENV[$key])) {
                        $_ENV[$key] = $value;
                        putenv("$key=$value");
                    }
                }
            }
        }
        
        // Load default configuration
        self::loadDefaults();
        self::$loaded = true;
    }
    
    /**
     * Load default configuration values
     */
    private static function loadDefaults() {
        self::$config = [
            // Database
            'database' => [
                'host' => self::env('DB_HOST', 'localhost'),
                'port' => self::env('DB_PORT', 3308),
                'name' => self::env('DB_NAME', 'mlm'),
                'user' => self::env('DB_USER', 'root'),
                'pass' => self::env('DB_PASS', ''),
            ],
            
            // Application
            'app' => [
                'env' => self::env('APP_ENV', 'production'),
                'debug' => self::env('APP_DEBUG', false),
                'url' => self::env('APP_URL', 'http://localhost'),
            ],
            
            // Security
            'security' => [
                'csrf_token_expiry' => self::env('CSRF_TOKEN_EXPIRY', 3600),
                'session_lifetime' => self::env('SESSION_LIFETIME', 7200),
                'password_min_length' => self::env('PASSWORD_MIN_LENGTH', 8),
            ],
            
            // Email
            'mail' => [
                'driver' => self::env('MAIL_DRIVER', 'smtp'),
                'host' => self::env('MAIL_HOST', 'smtp.gmail.com'),
                'port' => self::env('MAIL_PORT', 587),
                'username' => self::env('MAIL_USERNAME', ''),
                'password' => self::env('MAIL_PASSWORD', ''),
                'encryption' => self::env('MAIL_ENCRYPTION', 'tls'),
                'from_address' => self::env('MAIL_FROM_ADDRESS', ''),
                'from_name' => self::env('MAIL_FROM_NAME', 'MLM System'),
            ],
            
            // SMS
            'sms' => [
                'provider' => self::env('SMS_PROVIDER', 'twilio'),
                'api_key' => self::env('SMS_API_KEY', ''),
                'api_secret' => self::env('SMS_API_SECRET', ''),
                'from_number' => self::env('SMS_FROM_NUMBER', ''),
            ],
            
            // WhatsApp
            'whatsapp' => [
                'api_url' => self::env('WHATSAPP_API_URL', ''),
                'api_token' => self::env('WHATSAPP_API_TOKEN', ''),
            ],
            
            // Rate Limiting
            'rate_limit' => [
                'otp_requests' => self::env('RATE_LIMIT_OTP_REQUESTS', 5),
                'otp_window' => self::env('RATE_LIMIT_OTP_WINDOW', 15),
                'login_attempts' => self::env('RATE_LIMIT_LOGIN_ATTEMPTS', 5),
                'login_window' => self::env('RATE_LIMIT_LOGIN_WINDOW', 15),
            ],
            
            // Queue
            'queue' => [
                'driver' => self::env('QUEUE_DRIVER', 'database'),
                'max_retries' => self::env('QUEUE_MAX_RETRIES', 3),
                'retry_delay' => self::env('QUEUE_RETRY_DELAY', 300),
            ],
            
            // Logging
            'logging' => [
                'level' => self::env('LOG_LEVEL', 'info'),
                'to_file' => self::env('LOG_TO_FILE', true),
                'to_database' => self::env('LOG_TO_DATABASE', true),
                'file_path' => self::env('LOG_FILE_PATH', 'logs/app.log'),
            ],
            
            // MLM Business
            'mlm' => [
                'loan_amount' => self::env('LOAN_AMOUNT', 2000),
                'umgabelo_amount' => self::env('UMGABELO_AMOUNT', 500),
                'monthly_payment' => self::env('MONTHLY_PAYMENT', 534),
                'maintenance_fee_month1' => self::env('MAINTENANCE_FEE_MONTH1', 900),
                'maintenance_fee_regular' => self::env('MAINTENANCE_FEE_REGULAR', 2000),
                'matrix_type' => self::env('MATRIX_TYPE', '5×5'),
            ],
            
            // Commissions
            'commissions' => [
                'level_1' => self::env('COMMISSION_LEVEL_1', 1000),
                'level_2' => self::env('COMMISSION_LEVEL_2', 5000),
                'level_3' => self::env('COMMISSION_LEVEL_3', 25000),
                'level_4' => self::env('COMMISSION_LEVEL_4', 125000),
                'level_5' => self::env('COMMISSION_LEVEL_5', 625000),
            ],
        ];
    }
    
    /**
     * Get environment variable with default value
     */
    public static function env($key, $default = null) {
        $value = $_ENV[$key] ?? getenv($key) ?? $default;
        
        // Convert string booleans to actual booleans
        if (is_string($value)) {
            $lower = strtolower($value);
            if ($lower === 'true') return true;
            if ($lower === 'false') return false;
            if (is_numeric($value)) return is_float($value + 0) ? (float)$value : (int)$value;
        }
        
        return $value;
    }
    
    /**
     * Get configuration value using dot notation
     */
    public static function get($key, $default = null) {
        self::load();
        
        $keys = explode('.', $key);
        $value = self::$config;
        
        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                return $default;
            }
            $value = $value[$k];
        }
        
        return $value;
    }
    
    /**
     * Set configuration value
     */
    public static function set($key, $value) {
        self::load();
        
        $keys = explode('.', $key);
        $config = &self::$config;
        
        foreach ($keys as $k) {
            if (!isset($config[$k])) {
                $config[$k] = [];
            }
            $config = &$config[$k];
        }
        
        $config = $value;
    }
    
    /**
     * Get all configuration
     */
    public static function all() {
        self::load();
        return self::$config;
    }
    
    /**
     * Check if application is in debug mode
     */
    public static function isDebug() {
        return self::get('app.debug', false);
    }
    
    /**
     * Check if application is in development environment
     */
    public static function isDevelopment() {
        return self::get('app.env') === 'development';
    }
    
    /**
     * Check if application is in production environment
     */
    public static function isProduction() {
        return self::get('app.env') === 'production';
    }
}

// Auto-load configuration
Config::load();
?>
