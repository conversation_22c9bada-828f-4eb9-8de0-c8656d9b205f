<?php
/**
 * Wise Payment Gateway Integration
 * This class handles incoming payments from sponsors into the loan pool
 * For member payouts, use StitchPayouts class instead
 */
class WiseAPI {
    private $apiToken;
    private $profileId;
    private $baseUrl;
    private $sandbox;
    
    /**
     * Constructor
     * 
     * @param string $apiToken The Wise API token
     * @param string $profileId The Wise profile ID
     * @param bool $sandbox Whether to use sandbox environment
     */
    public function __construct($apiToken = null, $profileId = null, $sandbox = true) {
        global $con;
        
        // If not provided, get from database
        if ($apiToken === null || $profileId === null) {
            $query = mysqli_query($con, "SELECT * FROM wise_config WHERE id = 1");
            if (mysqli_num_rows($query) > 0) {
                $config = mysqli_fetch_assoc($query);
                $this->apiToken = $config['api_token'];
                $this->profileId = $config['profile_id'];
                $this->sandbox = $config['sandbox_mode'] == 1;
            }
        } else {
            $this->apiToken = $apiToken;
            $this->profileId = $profileId;
            $this->sandbox = $sandbox;
        }
        
        // Set base URL based on environment
        $this->baseUrl = $this->sandbox ? 
            'https://api.sandbox.transferwise.tech' : 
            'https://api.wise.com';
    }
    
    /**
     * Make a request to the Wise API
     * 
     * @param string $endpoint API endpoint
     * @param string $method HTTP method (GET, POST, etc.)
     * @param array $data Request data
     * @return array Response data
     */
    private function makeRequest($endpoint, $method = 'GET', $data = null) {
        $url = $this->baseUrl . $endpoint;
        
        $ch = curl_init($url);
        
        $headers = [
            'Authorization: Bearer ' . $this->apiToken,
            'Content-Type: application/json'
        ];
        
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            if ($data !== null) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
        } else if ($method === 'GET') {
            curl_setopt($ch, CURLOPT_HTTPGET, true);
        } else if ($method === 'PATCH') {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PATCH');
            if ($data !== null) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        if (curl_errno($ch)) {
            $error = curl_error($ch);
            curl_close($ch);
            return ['error' => $error, 'code' => $httpCode];
        }
        
        curl_close($ch);
        
        return [
            'data' => json_decode($response, true),
            'code' => $httpCode
        ];
    }
    
    /**
     * Create a batch group for multiple payments
     * 
     * @param string $name Batch group name
     * @param string $sourceCurrency Source currency code
     * @return array Batch group data or error
     */
    public function createBatchGroup($name, $sourceCurrency = 'USD') {
        $endpoint = "/v3/profiles/{$this->profileId}/batch-groups";
        $data = [
            'name' => $name,
            'sourceCurrency' => $sourceCurrency
        ];
        
        return $this->makeRequest($endpoint, 'POST', $data);
    }
    
    /**
     * Get a quote for a transfer
     * 
     * @param string $sourceCurrency Source currency code
     * @param string $targetCurrency Target currency code
     * @param float $sourceAmount Amount in source currency
     * @return array Quote data or error
     */
    public function createQuote($sourceCurrency, $targetCurrency, $sourceAmount) {
        $endpoint = "/v3/profiles/{$this->profileId}/quotes";
        $data = [
            'sourceCurrency' => $sourceCurrency,
            'targetCurrency' => $targetCurrency,
            'sourceAmount' => $sourceAmount,
            'preferredPayIn' => 'BALANCE',
            'targetAccount' => null
        ];
        
        return $this->makeRequest($endpoint, 'POST', $data);
    }
    
    /**
     * Create a recipient account
     * 
     * @param array $accountDetails Recipient account details
     * @return array Recipient account data or error
     */
    public function createRecipient($accountDetails) {
        $endpoint = "/v1/accounts";
        
        return $this->makeRequest($endpoint, 'POST', $accountDetails);
    }
    
    /**
     * Create a transfer in a batch group
     * 
     * @param string $batchGroupId Batch group ID
     * @param string $targetAccountId Recipient account ID
     * @param string $quoteId Quote ID
     * @param string $reference Payment reference
     * @return array Transfer data or error
     */
    public function createBatchTransfer($batchGroupId, $targetAccountId, $quoteId, $reference) {
        $endpoint = "/v3/profiles/{$this->profileId}/batch-groups/{$batchGroupId}/transfers";
        
        $data = [
            'targetAccount' => $targetAccountId,
            'quoteUuid' => $quoteId,
            'customerTransactionId' => uniqid('mlm_', true),
            'details' => [
                'reference' => $reference
            ]
        ];
        
        return $this->makeRequest($endpoint, 'POST', $data);
    }
    
    /**
     * Complete a batch group to prepare for funding
     * 
     * @param string $batchGroupId Batch group ID
     * @param int $version Batch group version
     * @return array Batch group data or error
     */
    public function completeBatchGroup($batchGroupId, $version) {
        $endpoint = "/v3/profiles/{$this->profileId}/batch-groups/{$batchGroupId}";
        
        $data = [
            'status' => 'COMPLETED',
            'version' => $version
        ];
        
        return $this->makeRequest($endpoint, 'PATCH', $data);
    }
    
    /**
     * Fund a batch group from Wise balance
     * 
     * @param string $batchGroupId Batch group ID
     * @return array Funding result or error
     */
    public function fundBatchGroup($batchGroupId) {
        $endpoint = "/v3/profiles/{$this->profileId}/batch-payments/{$batchGroupId}/payments";
        
        $data = [
            'type' => 'BALANCE'
        ];
        
        return $this->makeRequest($endpoint, 'POST', $data);
    }
    
    /**
     * Get a batch group by ID
     * 
     * @param string $batchGroupId Batch group ID
     * @return array Batch group data or error
     */
    public function getBatchGroup($batchGroupId) {
        $endpoint = "/v3/profiles/{$this->profileId}/batch-groups/{$batchGroupId}";
        
        return $this->makeRequest($endpoint, 'GET');
    }
    
    /**
     * Get a transfer by ID
     * 
     * @param int $transferId Transfer ID
     * @return array Transfer data or error
     */
    public function getTransfer($transferId) {
        $endpoint = "/v1/transfers/{$transferId}";
        
        return $this->makeRequest($endpoint, 'GET');
    }
    
    /**
     * Process batch payments for multiple users
     * 
     * @param array $payments Array of payment details (userid, amount, etc.)
     * @param string $sourceCurrency Source currency code
     * @return array Processing result
     */
    public function processBatchPayments($payments, $sourceCurrency = 'USD') {
        $results = [
            'success' => false,
            'batch_group_id' => null,
            'transfers' => [],
            'errors' => []
        ];
        
        if (empty($payments)) {
            $results['errors'][] = 'No payments to process';
            return $results;
        }
        
        // Create a batch group
        $batchName = 'MLM Matrix Payments ' . date('Y-m-d H:i:s');
        $batchGroup = $this->createBatchGroup($batchName, $sourceCurrency);
        
        if (isset($batchGroup['error']) || !isset($batchGroup['data']['id'])) {
            $results['errors'][] = 'Failed to create batch group: ' . 
                (isset($batchGroup['error']) ? $batchGroup['error'] : 'Unknown error');
            return $results;
        }
        
        $batchGroupId = $batchGroup['data']['id'];
        $batchVersion = $batchGroup['data']['version'];
        $results['batch_group_id'] = $batchGroupId;
        
        // Process each payment
        foreach ($payments as $payment) {
            $userId = $payment['userid'];
            $amount = $payment['amount'];
            
            // Get user account details
            global $con;
            $query = mysqli_query($con, "SELECT * FROM user WHERE email='$userId'");
            if (mysqli_num_rows($query) == 0) {
                $results['errors'][] = "User not found: $userId";
                continue;
            }
            
            $user = mysqli_fetch_assoc($query);
            
            // Check if user has Wise recipient account ID
            $recipientQuery = mysqli_query($con, "SELECT * FROM wise_recipients WHERE userid='$userId'");
            $recipientId = null;
            
            if (mysqli_num_rows($recipientQuery) > 0) {
                $recipient = mysqli_fetch_assoc($recipientQuery);
                $recipientId = $recipient['recipient_id'];
            } else {
                // For now, skip users without recipient accounts
                // In a real implementation, you would create recipient accounts
                $results['errors'][] = "No Wise recipient account for user: $userId";
                continue;
            }
            
            // Create a quote
            $quote = $this->createQuote($sourceCurrency, $sourceCurrency, $amount);
            if (isset($quote['error']) || !isset($quote['data']['id'])) {
                $results['errors'][] = "Failed to create quote for $userId: " . 
                    (isset($quote['error']) ? $quote['error'] : 'Unknown error');
                continue;
            }
            
            $quoteId = $quote['data']['id'];
            
            // Create a transfer in the batch group
            $reference = "MLM Matrix Payment";
            $transfer = $this->createBatchTransfer($batchGroupId, $recipientId, $quoteId, $reference);
            
            if (isset($transfer['error']) || !isset($transfer['data']['id'])) {
                $results['errors'][] = "Failed to create transfer for $userId: " . 
                    (isset($transfer['error']) ? $transfer['error'] : 'Unknown error');
                continue;
            }
            
            $transferId = $transfer['data']['id'];
            
            // Store transfer details
            $results['transfers'][] = [
                'userid' => $userId,
                'amount' => $amount,
                'transfer_id' => $transferId
            ];
            
            // Update batch version
            $batchVersion = $transfer['data']['version'] ?? $batchVersion;
        }
        
        // Complete the batch group if we have any successful transfers
        if (!empty($results['transfers'])) {
            $complete = $this->completeBatchGroup($batchGroupId, $batchVersion);
            
            if (isset($complete['error'])) {
                $results['errors'][] = 'Failed to complete batch group: ' . $complete['error'];
                return $results;
            }
            
            // Fund the batch group
            $fund = $this->fundBatchGroup($batchGroupId);
            
            if (isset($fund['error'])) {
                $results['errors'][] = 'Failed to fund batch group: ' . $fund['error'];
                return $results;
            }
            
            $results['success'] = true;
        }
        
        return $results;
    }
}
?>
