<?php
/**
 * Message Queue System for Asynchronous Email/SMS Processing
 * Provides queue-based messaging to improve user experience
 */
class MessageQueue {
    
    private static $con;
    
    /**
     * Initialize message queue with database connection
     * 
     * @param mysqli $connection Database connection
     */
    public static function init($connection) {
        self::$con = $connection;
    }
    
    /**
     * Add an email to the queue
     * 
     * @param string $recipient Email address
     * @param string $subject Email subject
     * @param string $message Email body (HTML)
     * @param int $priority Priority (1=highest, 10=lowest)
     * @param string $scheduled_at When to send (default: now)
     * @return int|false Queue ID or false on failure
     */
    public static function queueEmail($recipient, $subject, $message, $priority = 5, $scheduled_at = null) {
        if (!self::$con) {
            throw new Exception("Message Queue not initialized");
        }
        
        if ($scheduled_at === null) {
            $scheduled_at = date('Y-m-d H:i:s');
        }
        
        return SecureDatabase::insert(
            "INSERT INTO message_queue (type, recipient, subject, message, priority, scheduled_at) VALUES (?, ?, ?, ?, ?, ?)",
            ['email', $recipient, $subject, $message, $priority, $scheduled_at],
            'ssssss'
        );
    }
    
    /**
     * Add an SMS to the queue
     * 
     * @param string $recipient Mobile number
     * @param string $message SMS message
     * @param int $priority Priority (1=highest, 10=lowest)
     * @param string $scheduled_at When to send (default: now)
     * @return int|false Queue ID or false on failure
     */
    public static function queueSMS($recipient, $message, $priority = 5, $scheduled_at = null) {
        if (!self::$con) {
            throw new Exception("Message Queue not initialized");
        }
        
        if ($scheduled_at === null) {
            $scheduled_at = date('Y-m-d H:i:s');
        }
        
        return SecureDatabase::insert(
            "INSERT INTO message_queue (type, recipient, message, priority, scheduled_at) VALUES (?, ?, ?, ?, ?)",
            ['sms', $recipient, $message, $priority, $scheduled_at],
            'sssss'
        );
    }
    
    /**
     * Add a WhatsApp message to the queue
     * 
     * @param string $recipient Mobile number
     * @param string $message WhatsApp message
     * @param int $priority Priority (1=highest, 10=lowest)
     * @param string $scheduled_at When to send (default: now)
     * @return int|false Queue ID or false on failure
     */
    public static function queueWhatsApp($recipient, $message, $priority = 5, $scheduled_at = null) {
        if (!self::$con) {
            throw new Exception("Message Queue not initialized");
        }
        
        if ($scheduled_at === null) {
            $scheduled_at = date('Y-m-d H:i:s');
        }
        
        return SecureDatabase::insert(
            "INSERT INTO message_queue (type, recipient, message, priority, scheduled_at) VALUES (?, ?, ?, ?, ?)",
            ['whatsapp', $recipient, $message, $priority, $scheduled_at],
            'sssss'
        );
    }
    
    /**
     * Get pending messages from the queue
     * 
     * @param int $limit Maximum number of messages to retrieve
     * @param string $type Message type filter (email, sms, whatsapp)
     * @return array Array of pending messages
     */
    public static function getPendingMessages($limit = 10, $type = null) {
        if (!self::$con) {
            throw new Exception("Message Queue not initialized");
        }
        
        $current_time = date('Y-m-d H:i:s');
        
        if ($type) {
            $query = "SELECT * FROM message_queue WHERE status = 'pending' AND scheduled_at <= ? AND type = ? ORDER BY priority ASC, scheduled_at ASC LIMIT ?";
            $params = [$current_time, $type, $limit];
            $types = 'ssi';
        } else {
            $query = "SELECT * FROM message_queue WHERE status = 'pending' AND scheduled_at <= ? ORDER BY priority ASC, scheduled_at ASC LIMIT ?";
            $params = [$current_time, $limit];
            $types = 'si';
        }
        
        return SecureDatabase::getRows($query, $params, $types);
    }
    
    /**
     * Mark a message as processing
     * 
     * @param int $message_id Message ID
     * @return bool True if updated successfully
     */
    public static function markAsProcessing($message_id) {
        if (!self::$con) {
            throw new Exception("Message Queue not initialized");
        }
        
        $result = SecureDatabase::update(
            "UPDATE message_queue SET status = 'processing', processed_at = ? WHERE id = ?",
            [date('Y-m-d H:i:s'), $message_id],
            'si'
        );
        
        return $result !== false;
    }
    
    /**
     * Mark a message as sent
     * 
     * @param int $message_id Message ID
     * @return bool True if updated successfully
     */
    public static function markAsSent($message_id) {
        if (!self::$con) {
            throw new Exception("Message Queue not initialized");
        }
        
        $result = SecureDatabase::update(
            "UPDATE message_queue SET status = 'sent', processed_at = ? WHERE id = ?",
            [date('Y-m-d H:i:s'), $message_id],
            'si'
        );
        
        return $result !== false;
    }
    
    /**
     * Mark a message as failed
     * 
     * @param int $message_id Message ID
     * @param string $error_message Error description
     * @return bool True if updated successfully
     */
    public static function markAsFailed($message_id, $error_message = '') {
        if (!self::$con) {
            throw new Exception("Message Queue not initialized");
        }
        
        // Get current attempts
        $message = SecureDatabase::getRow(
            "SELECT attempts, max_attempts FROM message_queue WHERE id = ?",
            [$message_id],
            'i'
        );
        
        if (!$message) {
            return false;
        }
        
        $new_attempts = $message['attempts'] + 1;
        
        if ($new_attempts >= $message['max_attempts']) {
            // Max attempts reached, mark as failed
            $result = SecureDatabase::update(
                "UPDATE message_queue SET status = 'failed', attempts = ?, error_message = ?, processed_at = ? WHERE id = ?",
                [$new_attempts, $error_message, date('Y-m-d H:i:s'), $message_id],
                'issi'
            );
        } else {
            // Retry later, reset to pending
            $retry_time = date('Y-m-d H:i:s', time() + (300 * $new_attempts)); // Exponential backoff: 5min, 10min, 15min
            $result = SecureDatabase::update(
                "UPDATE message_queue SET status = 'pending', attempts = ?, error_message = ?, scheduled_at = ? WHERE id = ?",
                [$new_attempts, $error_message, $retry_time, $message_id],
                'issi'
            );
        }
        
        return $result !== false;
    }
    
    /**
     * Cancel a message
     * 
     * @param int $message_id Message ID
     * @return bool True if updated successfully
     */
    public static function cancelMessage($message_id) {
        if (!self::$con) {
            throw new Exception("Message Queue not initialized");
        }
        
        $result = SecureDatabase::update(
            "UPDATE message_queue SET status = 'cancelled' WHERE id = ?",
            [$message_id],
            'i'
        );
        
        return $result !== false;
    }
    
    /**
     * Get queue statistics
     * 
     * @return array Queue statistics
     */
    public static function getQueueStats() {
        if (!self::$con) {
            throw new Exception("Message Queue not initialized");
        }
        
        $stats = [];
        
        // Get counts by status
        $status_counts = SecureDatabase::getRows(
            "SELECT status, COUNT(*) as count FROM message_queue GROUP BY status"
        );
        
        foreach ($status_counts as $row) {
            $stats[$row['status']] = $row['count'];
        }
        
        // Get counts by type
        $type_counts = SecureDatabase::getRows(
            "SELECT type, COUNT(*) as count FROM message_queue WHERE status = 'pending' GROUP BY type"
        );
        
        $stats['pending_by_type'] = [];
        foreach ($type_counts as $row) {
            $stats['pending_by_type'][$row['type']] = $row['count'];
        }
        
        return $stats;
    }
    
    /**
     * Clean up old processed messages
     * 
     * @param int $days_old Messages older than this many days will be deleted
     * @return int Number of messages deleted
     */
    public static function cleanup($days_old = 30) {
        if (!self::$con) {
            throw new Exception("Message Queue not initialized");
        }
        
        $cutoff_date = date('Y-m-d H:i:s', time() - ($days_old * 24 * 60 * 60));
        
        $result = SecureDatabase::delete(
            "DELETE FROM message_queue WHERE status IN ('sent', 'failed', 'cancelled') AND processed_at < ?",
            [$cutoff_date],
            's'
        );
        
        return $result !== false ? $result : 0;
    }
}
?>
