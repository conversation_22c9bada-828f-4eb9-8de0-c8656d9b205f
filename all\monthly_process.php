<?php
include('php-includes/connect.php');
include('php-includes/check-login.php');
$userid = $_SESSION['userid'];
$monthly_payment = 534;
$maintenance_fee_month1 = 900;
$maintenance_fee_regular = 2000;

// Process monthly maintenance fees and loan repayments
if(isset($_GET['process_month'])) {
    $process_month = mysqli_real_escape_string($con, $_GET['process_month']);
    $process_user = mysqli_real_escape_string($con, $_GET['process_user']);

    if($process_user != "" && $process_month > 0) {
        // Process loan repayment
        processLoanRepayment($process_user);

        // Process maintenance fee
        processMaintenanceFee($process_user, $process_month);

        echo '<script>alert("Monthly processing completed for ' . $process_user . '");</script>';
    } else {
        echo '<script>alert("Invalid user or month.");</script>';
    }
}

// Function to process loan repayment
function processLoanRepayment($userid) {
    global $con, $monthly_payment;

    // Get current loan status
    $query = mysqli_query($con, "SELECT * FROM loan WHERE userid='$userid' AND status='active'");

    if(mysqli_num_rows($query) > 0) {
        $result = mysqli_fetch_array($query);
        $remaining_amount = $result['remaining_amount'];

        // Calculate new remaining amount
        $new_remaining = $remaining_amount - $monthly_payment;
        if($new_remaining < 0) {
            $new_remaining = 0;
        }

        // Update loan status if fully paid
        $status = ($new_remaining <= 0) ? 'paid' : 'active';

        // Update loan record
        mysqli_query($con, "UPDATE loan SET remaining_amount='$new_remaining', status='$status' WHERE userid='$userid'");

        // Update income record to track loan repayment
        $query = mysqli_query($con, "SELECT loan_repaid FROM income WHERE userid='$userid'");
        $result = mysqli_fetch_array($query);
        $current_repaid = $result['loan_repaid'];
        $new_repaid = $current_repaid + $monthly_payment;

        mysqli_query($con, "UPDATE income SET loan_repaid='$new_repaid' WHERE userid='$userid'");

        return true;
    }

    return false;
}

// Function to process maintenance fee
function processMaintenanceFee($userid, $month) {
    global $con, $maintenance_fee_month1, $maintenance_fee_regular;

    // Determine fee amount based on month
    $fee_amount = ($month == 1) ? $maintenance_fee_month1 : $maintenance_fee_regular;

    // Check if fee already paid for this month
    $query = mysqli_query($con, "SELECT * FROM maintenance_fee WHERE userid='$userid' AND month='$month'");

    if(mysqli_num_rows($query) == 0) {
        // Insert new maintenance fee record
        $today = date("Y-m-d");
        mysqli_query($con, "INSERT INTO maintenance_fee (userid, month, fee_amount, date_paid, status)
                           VALUES ('$userid', '$month', '$fee_amount', '$today', 'paid')");

        // Update income record to track maintenance payments
        $query = mysqli_query($con, "SELECT maintenance_paid FROM income WHERE userid='$userid'");
        $result = mysqli_fetch_array($query);
        $current_paid = $result['maintenance_paid'];
        $new_paid = $current_paid + $fee_amount;

        mysqli_query($con, "UPDATE income SET maintenance_paid='$new_paid' WHERE userid='$userid'");

        return true;
    }

    return false;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <title>MLM Website - Monthly Processing</title>
    <!-- Bootstrap Core CSS -->
    <link href="vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- MetisMenu CSS -->
    <link href="vendor/metisMenu/metisMenu.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="dist/css/sb-admin-2.css" rel="stylesheet">
    <!-- Custom Fonts -->
    <link href="vendor/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css">
</head>
<body>
    <div id="wrapper">
        <!-- Navigation -->
        <?php include('php-includes/menu.php'); ?>

        <!-- Page Content -->
        <div id="page-wrapper">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-lg-12">
                        <h1 class="page-header">Monthly Processing</h1>
                    </div>
                    <!-- /.col-lg-12 -->
                </div>
                <!-- /.row -->
                <div class="row">
                    <div class="col-lg-6">
                        <div class="panel panel-primary">
                            <div class="panel-heading">
                                Process Monthly Fees and Loan Repayments
                            </div>
                            <div class="panel-body">
                                <form method="get">
                                    <div class="form-group">
                                        <label>User Email</label>
                                        <input type="email" name="process_user" class="form-control" required>
                                    </div>
                                    <div class="form-group">
                                        <label>Month Number</label>
                                        <select name="process_month" class="form-control" required>
                                            <option value="1">Month 1</option>
                                            <option value="2">Month 2</option>
                                            <option value="3">Month 3</option>
                                            <option value="4">Month 4</option>
                                            <option value="5">Month 5</option>
                                            <option value="6">Month 6</option>
                                            <option value="7">Month 7</option>
                                            <option value="8">Month 8</option>
                                            <option value="9">Month 9</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <input type="submit" class="btn btn-primary" value="Process Month">
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="panel panel-info">
                            <div class="panel-heading">
                                Monthly Fee Structure
                            </div>
                            <div class="panel-body">
                                <table class="table table-bordered">
                                    <tr>
                                        <th>Month</th>
                                        <th>Maintenance Fee</th>
                                        <th>Loan Repayment</th>
                                        <th>Total</th>
                                    </tr>
                                    <tr>
                                        <td>Month 1</td>
                                        <td>R <?php echo $maintenance_fee_month1; ?></td>
                                        <td>R <?php echo $monthly_payment; ?></td>
                                        <td>R <?php echo $maintenance_fee_month1 + $monthly_payment; ?></td>
                                    </tr>
                                    <tr>
                                        <td>Month 2-6</td>
                                        <td>R <?php echo $maintenance_fee_regular; ?></td>
                                        <td>R <?php echo $monthly_payment; ?></td>
                                        <td>R <?php echo $maintenance_fee_regular + $monthly_payment; ?></td>
                                    </tr>
                                    <tr>
                                        <td>Month 7+</td>
                                        <td>R <?php echo $maintenance_fee_regular; ?></td>
                                        <td>R 0 (Loan Paid)</td>
                                        <td>R <?php echo $maintenance_fee_regular; ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <div class="panel panel-success">
                            <div class="panel-heading">
                                5×5 Matrix Commission Structure
                            </div>
                            <div class="panel-body">
                                <table class="table table-bordered">
                                    <tr>
                                        <th>Level</th>
                                        <th>Members</th>
                                        <th>Commission</th>
                                    </tr>
                                    <?php
                                    $query = mysqli_query($con, "SELECT * FROM matrix_commission ORDER BY level ASC");
                                    while($row = mysqli_fetch_array($query)) {
                                        $level = $row['level'];
                                        $commission = $row['commission_amount'];

                                        // Calculate members at this level
                                        $members = 5;
                                        if($level > 1) {
                                            $members = pow(5, $level);
                                        }

                                        echo '<tr>';
                                        echo '<td>Level ' . $level . '</td>';
                                        echo '<td>' . $members . '</td>';
                                        echo '<td>R ' . number_format($commission) . '</td>';
                                        echo '</tr>';
                                    }
                                    ?>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- /.container-fluid -->
        </div>
        <!-- /#page-wrapper -->
    </div>
    <!-- /#wrapper -->

    <!-- jQuery -->
    <script src="vendor/jquery/jquery.min.js"></script>
    <!-- Bootstrap Core JavaScript -->
    <script src="vendor/bootstrap/js/bootstrap.min.js"></script>
    <!-- Metis Menu Plugin JavaScript -->
    <script src="vendor/metisMenu/metisMenu.min.js"></script>
    <!-- Custom Theme JavaScript -->
    <script src="dist/js/sb-admin-2.js"></script>
</body>
</html>
