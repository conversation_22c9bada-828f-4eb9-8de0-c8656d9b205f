-- Table structure for Wise API configuration
CREATE TABLE `wise_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `api_token` varchar(255) NOT NULL,
  `profile_id` varchar(100) NOT NULL,
  `sandbox_mode` tinyint(1) NOT NULL DEFAULT '1',
  `default_currency` varchar(3) NOT NULL DEFAULT 'USD',
  `last_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- Table structure for Wise recipient accounts
CREATE TABLE `wise_recipients` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userid` varchar(50) NOT NULL,
  `recipient_id` varchar(100) NOT NULL,
  `currency` varchar(3) NOT NULL,
  `account_details` text NOT NULL,
  `created_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `userid` (`userid`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- Table structure for Wise batch payments
CREATE TABLE `wise_batch_payments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `batch_group_id` varchar(100) NOT NULL,
  `status` enum('created','completed','failed') NOT NULL DEFAULT 'created',
  `source_currency` varchar(3) NOT NULL,
  `total_amount` decimal(10,2) NOT NULL,
  `created_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `completed_date` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- Table structure for Wise payment transfers
CREATE TABLE `wise_transfers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `batch_id` int(11) NOT NULL,
  `userid` varchar(50) NOT NULL,
  `transfer_id` varchar(100) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `status` varchar(50) NOT NULL,
  `reference` varchar(100) DEFAULT NULL,
  `created_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_date` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `batch_id` (`batch_id`),
  KEY `userid` (`userid`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- Add wise_transfer_id column to income_received table
ALTER TABLE `income_received` 
ADD COLUMN `wise_transfer_id` varchar(100) DEFAULT NULL,
ADD COLUMN `wise_payment_status` varchar(50) DEFAULT NULL;
