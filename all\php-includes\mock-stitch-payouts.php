<?php
/**
 * Mock Stitch Payouts Class
 * Simulates Stitch payment gateway for testing without real transactions
 */

class MockStitchPayouts {
    
    private $con;
    private $test_run_id;
    private $success_rate;
    private $delay_simulation;
    
    public function __construct($connection, $test_run_id = null) {
        $this->con = $connection;
        $this->test_run_id = $test_run_id;
        $this->success_rate = 0.95; // 95% success rate for realistic testing
        $this->delay_simulation = true; // Simulate API delays
    }
    
    /**
     * Mock MLM payout processing
     * 
     * @param array $payouts Array of payout data
     * @return array Mock response
     */
    public function processMLMPayouts($payouts) {
        if ($this->delay_simulation) {
            usleep(rand(500000, 2000000)); // 0.5-2 second delay
        }
        
        $batch_id = 'mock_batch_' . time() . '_' . rand(1000, 9999);
        $processed_payouts = [];
        $total_amount = 0;
        $successful_count = 0;
        $failed_count = 0;
        
        foreach ($payouts as $payout) {
            $success = (rand(1, 100) / 100) <= $this->success_rate;
            $mock_payout_id = 'mock_payout_' . time() . '_' . rand(1000, 9999);
            
            $payout_result = [
                'id' => $mock_payout_id,
                'user_id' => $payout['user_id'],
                'amount' => $payout['amount'],
                'commission_type' => $payout['commission_type'] ?? 'matrix',
                'status' => $success ? 'success' : 'failed',
                'error_message' => $success ? null : $this->generateMockError(),
                'processed_at' => date('Y-m-d H:i:s'),
                'batch_id' => $batch_id
            ];
            
            // Log to mock payouts table
            $this->logMockPayout($payout_result);
            
            $processed_payouts[] = $payout_result;
            $total_amount += $payout['amount'];
            
            if ($success) {
                $successful_count++;
            } else {
                $failed_count++;
            }
        }
        
        $response = [
            'success' => true,
            'batch_id' => $batch_id,
            'total_payouts' => count($payouts),
            'successful_payouts' => $successful_count,
            'failed_payouts' => $failed_count,
            'total_amount' => $total_amount,
            'payouts' => $processed_payouts,
            'message' => "Mock batch processed: $successful_count successful, $failed_count failed",
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        // Log batch summary
        $this->logBatchSummary($response);
        
        return $response;
    }
    
    /**
     * Get mock payout batch status
     * 
     * @param string $batch_id Batch ID
     * @return array Mock status response
     */
    public function getPayoutBatchStatus($batch_id) {
        if ($this->delay_simulation) {
            usleep(rand(100000, 500000)); // 0.1-0.5 second delay
        }
        
        // Get payouts for this batch from mock table
        $payouts = SecureDatabase::getRows(
            "SELECT * FROM mock_payouts WHERE mock_response->>'$.batch_id' = ?",
            [$batch_id]
        );
        
        $total_amount = 0;
        $successful_count = 0;
        $failed_count = 0;
        
        foreach ($payouts as $payout) {
            $total_amount += $payout['amount'];
            if ($payout['status'] === 'success') {
                $successful_count++;
            } else {
                $failed_count++;
            }
        }
        
        return [
            'success' => true,
            'batch_id' => $batch_id,
            'status' => 'completed',
            'total_payouts' => count($payouts),
            'successful_payouts' => $successful_count,
            'failed_payouts' => $failed_count,
            'total_amount' => $total_amount,
            'payouts' => $payouts,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * Mock individual payout status check
     * 
     * @param string $payout_id Payout ID
     * @return array Mock status response
     */
    public function getPayoutStatus($payout_id) {
        if ($this->delay_simulation) {
            usleep(rand(50000, 200000)); // 0.05-0.2 second delay
        }
        
        $payout = SecureDatabase::getRow(
            "SELECT * FROM mock_payouts WHERE mock_response->>'$.id' = ?",
            [$payout_id]
        );
        
        if (!$payout) {
            return [
                'success' => false,
                'error' => 'Payout not found',
                'payout_id' => $payout_id
            ];
        }
        
        return [
            'success' => true,
            'payout_id' => $payout_id,
            'status' => $payout['status'],
            'amount' => $payout['amount'],
            'user_id' => $payout['user_id'],
            'processed_at' => $payout['processed_at'],
            'error_message' => $payout['status'] === 'failed' ? 'Mock failure for testing' : null
        ];
    }
    
    /**
     * Mock account validation
     * 
     * @param string $account_number Account number
     * @return array Mock validation response
     */
    public function validateAccount($account_number) {
        if ($this->delay_simulation) {
            usleep(rand(200000, 800000)); // 0.2-0.8 second delay
        }
        
        // Mock validation logic
        $is_valid = strlen($account_number) >= 10 && is_numeric($account_number);
        
        return [
            'success' => true,
            'account_number' => $account_number,
            'is_valid' => $is_valid,
            'account_holder' => $is_valid ? 'Mock Account Holder' : null,
            'bank_name' => $is_valid ? 'Mock Bank' : null,
            'message' => $is_valid ? 'Account validated successfully' : 'Invalid account number'
        ];
    }
    
    /**
     * Get mock transaction history
     * 
     * @param array $filters Filters for transaction history
     * @return array Mock transaction history
     */
    public function getTransactionHistory($filters = []) {
        $where_conditions = [];
        $params = [];
        
        if (isset($filters['user_id'])) {
            $where_conditions[] = "user_id = ?";
            $params[] = $filters['user_id'];
        }
        
        if (isset($filters['date_from'])) {
            $where_conditions[] = "created_at >= ?";
            $params[] = $filters['date_from'];
        }
        
        if (isset($filters['date_to'])) {
            $where_conditions[] = "created_at <= ?";
            $params[] = $filters['date_to'];
        }
        
        $where_clause = empty($where_conditions) ? '' : 'WHERE ' . implode(' AND ', $where_conditions);
        
        $transactions = SecureDatabase::getRows(
            "SELECT * FROM mock_payouts $where_clause ORDER BY created_at DESC LIMIT 100",
            $params
        );
        
        return [
            'success' => true,
            'transactions' => $transactions,
            'total_count' => count($transactions),
            'filters_applied' => $filters
        ];
    }
    
    /**
     * Log mock payout to database
     */
    private function logMockPayout($payout_result) {
        SecureDatabase::insert(
            "INSERT INTO mock_payouts (test_run_id, user_id, amount, commission_type, status, mock_response, processed_at) VALUES (?, ?, ?, ?, ?, ?, ?)",
            [
                $this->test_run_id,
                $payout_result['user_id'],
                $payout_result['amount'],
                $payout_result['commission_type'],
                $payout_result['status'],
                json_encode($payout_result),
                $payout_result['processed_at']
            ],
            'isdsss'
        );
    }
    
    /**
     * Log batch summary
     */
    private function logBatchSummary($response) {
        ErrorHandler::logInfo("Mock payout batch processed", [
            'batch_id' => $response['batch_id'],
            'total_payouts' => $response['total_payouts'],
            'successful_payouts' => $response['successful_payouts'],
            'failed_payouts' => $response['failed_payouts'],
            'total_amount' => $response['total_amount'],
            'test_run_id' => $this->test_run_id
        ]);
    }
    
    /**
     * Generate mock error messages
     */
    private function generateMockError() {
        $errors = [
            'Insufficient funds in account',
            'Account temporarily blocked',
            'Invalid account details',
            'Network timeout',
            'Bank system maintenance',
            'Daily limit exceeded',
            'Account not found'
        ];
        
        return $errors[array_rand($errors)];
    }
    
    /**
     * Set success rate for testing different scenarios
     */
    public function setSuccessRate($rate) {
        $this->success_rate = max(0, min(1, $rate));
    }
    
    /**
     * Enable/disable delay simulation
     */
    public function setDelaySimulation($enabled) {
        $this->delay_simulation = $enabled;
    }
    
    /**
     * Get mock statistics
     */
    public function getMockStatistics() {
        $stats = SecureDatabase::getRow(
            "SELECT 
                COUNT(*) as total_payouts,
                SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as successful_payouts,
                SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_payouts,
                SUM(amount) as total_amount,
                AVG(amount) as average_amount
            FROM mock_payouts 
            WHERE test_run_id = ?",
            [$this->test_run_id],
            'i'
        );
        
        return $stats ?: [
            'total_payouts' => 0,
            'successful_payouts' => 0,
            'failed_payouts' => 0,
            'total_amount' => 0,
            'average_amount' => 0
        ];
    }
}
?>
