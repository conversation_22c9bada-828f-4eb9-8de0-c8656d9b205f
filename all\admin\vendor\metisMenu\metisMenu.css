/*
 * metismenu - v1.1.3
 * Easy menu jQuery plugin for Twitter Bootstrap 3
 * https://github.com/onokumus/metisMenu
 *
 * Made by <PERSON><PERSON>
 * Under MIT License
 */
.arrow {
    float: right;
    line-height: 1.42857;
}

.glyphicon.arrow:before {
    content: "\e079";
}

.active > a > .glyphicon.arrow:before {
    content: "\e114";
}


/*
 * Require Font-Awesome
 * http://fortawesome.github.io/Font-Awesome/
*/


.fa.arrow:before {
    content: "\f104";
}

.active > a > .fa.arrow:before {
    content: "\f107";
}

.plus-times {
    float: right;
}

.fa.plus-times:before {
    content: "\f067";
}

.active > a > .fa.plus-times {
    filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=1);
    -webkit-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    -o-transform: rotate(45deg);
    transform: rotate(45deg);
}

.plus-minus {
    float: right;
}

.fa.plus-minus:before {
    content: "\f067";
}

.active > a > .fa.plus-minus:before {
    content: "\f068";
}