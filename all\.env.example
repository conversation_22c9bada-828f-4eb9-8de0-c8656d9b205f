# Database Configuration
DB_HOST=localhost
DB_PORT=3308
DB_NAME=mlm
DB_USER=root
DB_PASS=

# Test Database Configuration
TEST_DB_HOST=localhost
TEST_DB_PORT=3308
TEST_DB_NAME=mlm_test
TEST_DB_USER=root
TEST_DB_PASS=

# Application Configuration
APP_ENV=development
APP_DEBUG=true
APP_URL=http://localhost

# Testing Configuration
TESTING_MODE=false
MOCK_PAYMENTS=true
MOCK_SMS=true
MOCK_EMAIL=false

# Security Configuration
CSRF_TOKEN_EXPIRY=3600
SESSION_LIFETIME=7200
PASSWORD_MIN_LENGTH=8

# Email Configuration
MAIL_DRIVER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="MLM System"

# SMS Configuration (replace with your SMS provider)
SMS_PROVIDER=twilio
SMS_API_KEY=your-sms-api-key
SMS_API_SECRET=your-sms-api-secret
SMS_FROM_NUMBER=+**********

# WhatsApp Configuration
WHATSAPP_API_URL=https://api.whatsapp.com
WHATSAPP_API_TOKEN=your-whatsapp-token

# Rate Limiting Configuration
RATE_LIMIT_OTP_REQUESTS=5
RATE_LIMIT_OTP_WINDOW=15
RATE_LIMIT_LOGIN_ATTEMPTS=5
RATE_LIMIT_LOGIN_WINDOW=15

# Queue Configuration
QUEUE_DRIVER=database
QUEUE_MAX_RETRIES=3
QUEUE_RETRY_DELAY=300

# Logging Configuration
LOG_LEVEL=info
LOG_TO_FILE=true
LOG_TO_DATABASE=true
LOG_FILE_PATH=logs/app.log

# MLM Business Configuration
LOAN_AMOUNT=2000
UMGABELO_AMOUNT=500
MONTHLY_PAYMENT=534
MAINTENANCE_FEE_MONTH1=900
MAINTENANCE_FEE_REGULAR=2000
MATRIX_TYPE="5×5"

# Commission Configuration
COMMISSION_LEVEL_1=1000
COMMISSION_LEVEL_2=5000
COMMISSION_LEVEL_3=25000
COMMISSION_LEVEL_4=125000
COMMISSION_LEVEL_5=625000
