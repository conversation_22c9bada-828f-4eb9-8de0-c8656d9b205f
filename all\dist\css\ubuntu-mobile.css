/**
 * Ubuntu Wealth Network - Mobile-First Responsive CSS
 * Optimized for South African mobile users
 */

:root {
    --sa-green: #007749;
    --sa-gold: #FFB612;
    --sa-blue: #002395;
    --sa-red: #DE3831;
    --ubuntu-orange: #E95420;
    --warm-white: #FFF8F0;
    --text-dark: #2C3E50;
    --text-light: #6C757D;
    --success-green: #28a745;
    --warning-orange: #fd7e14;
}

/* Mobile-First Base Styles */
* {
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
}

body {
    font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 16px; /* Minimum readable size on mobile */
    line-height: 1.6;
    color: var(--text-dark);
    background: var(--warm-white);
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* Touch-Friendly Interactive Elements */
.btn, .nav-link, .card, .form-control {
    min-height: 44px; /* Apple's recommended minimum touch target */
    min-width: 44px;
}

.btn {
    padding: 12px 20px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px;
    border: none;
    transition: all 0.2s ease;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn:active {
    transform: scale(0.98);
}

/* Mobile Navigation */
.mobile-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid #e9ecef;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
    padding: 8px 0;
}

.mobile-nav-item {
    flex: 1;
    text-align: center;
    padding: 8px 4px;
    color: var(--text-light);
    text-decoration: none;
    font-size: 12px;
    transition: all 0.2s ease;
}

.mobile-nav-item.active {
    color: var(--sa-green);
}

.mobile-nav-item i {
    font-size: 20px;
    display: block;
    margin-bottom: 4px;
}

/* Mobile Header */
.mobile-header {
    background: linear-gradient(90deg, var(--sa-green) 0%, var(--sa-blue) 100%);
    color: white;
    padding: 12px 16px;
    position: sticky;
    top: 0;
    z-index: 999;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.mobile-header h1 {
    font-size: 18px;
    margin: 0;
    font-weight: 600;
}

.mobile-header .user-info {
    font-size: 14px;
    opacity: 0.9;
    margin-top: 4px;
}

/* Mobile Cards */
.mobile-card {
    background: white;
    border-radius: 12px;
    padding: 16px;
    margin: 8px 16px 16px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: none;
}

.mobile-card-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
}

.mobile-card-icon {
    font-size: 24px;
    margin-right: 12px;
    color: var(--sa-green);
}

.mobile-card-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
    color: var(--text-dark);
}

/* Mobile Stats Grid */
.mobile-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    padding: 0 16px;
    margin-bottom: 16px;
}

.mobile-stat-card {
    background: white;
    border-radius: 12px;
    padding: 16px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.mobile-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--sa-green) 0%, var(--sa-gold) 100%);
}

.mobile-stat-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 4px;
}

.mobile-stat-label {
    font-size: 12px;
    color: var(--text-light);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Mobile Forms */
.mobile-form {
    padding: 0 16px;
}

.mobile-form-group {
    margin-bottom: 20px;
}

.mobile-form-label {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 8px;
}

.mobile-form-control {
    width: 100%;
    padding: 16px;
    font-size: 16px; /* Prevents zoom on iOS */
    border: 2px solid #e9ecef;
    border-radius: 8px;
    background: white;
    transition: border-color 0.2s ease;
}

.mobile-form-control:focus {
    outline: none;
    border-color: var(--sa-green);
    box-shadow: 0 0 0 3px rgba(0, 119, 73, 0.1);
}

/* Mobile Matrix Visualization */
.mobile-matrix {
    padding: 20px;
    text-align: center;
}

.mobile-matrix-position {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin: 4px;
    font-weight: 600;
    font-size: 12px;
    transition: all 0.2s ease;
}

.mobile-matrix-you {
    background: linear-gradient(135deg, var(--sa-gold) 0%, var(--ubuntu-orange) 100%);
    color: white;
    font-size: 10px;
}

.mobile-matrix-filled {
    background: linear-gradient(135deg, var(--sa-green) 0%, var(--success-green) 100%);
    color: white;
}

.mobile-matrix-empty {
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    color: var(--text-light);
}

/* Mobile Tables */
.mobile-table {
    width: 100%;
    margin: 0 16px;
}

.mobile-table-row {
    background: white;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.1);
}

.mobile-table-header {
    font-weight: 600;
    font-size: 14px;
    color: var(--text-dark);
    margin-bottom: 4px;
}

.mobile-table-content {
    font-size: 13px;
    color: var(--text-light);
}

/* Mobile Action Buttons */
.mobile-action-bar {
    position: fixed;
    bottom: 70px; /* Above mobile nav */
    left: 16px;
    right: 16px;
    z-index: 998;
}

.mobile-fab {
    background: linear-gradient(45deg, var(--sa-gold) 0%, var(--ubuntu-orange) 100%);
    color: white;
    border: none;
    border-radius: 50%;
    width: 56px;
    height: 56px;
    font-size: 24px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    position: fixed;
    bottom: 80px;
    right: 20px;
    z-index: 999;
    transition: all 0.2s ease;
}

.mobile-fab:active {
    transform: scale(0.95);
}

/* Mobile Alerts */
.mobile-alert {
    margin: 8px 16px;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 14px;
    border: none;
}

.mobile-alert-success {
    background: #d4edda;
    color: #155724;
    border-left: 4px solid var(--success-green);
}

.mobile-alert-info {
    background: #d1ecf1;
    color: #0c5460;
    border-left: 4px solid var(--sa-blue);
}

.mobile-alert-warning {
    background: #fff3cd;
    color: #856404;
    border-left: 4px solid var(--warning-orange);
}

/* Mobile Progress Bars */
.mobile-progress {
    background: #e9ecef;
    border-radius: 10px;
    height: 8px;
    overflow: hidden;
    margin: 8px 0;
}

.mobile-progress-bar {
    background: linear-gradient(90deg, var(--sa-green) 0%, var(--success-green) 100%);
    height: 100%;
    border-radius: 10px;
    transition: width 0.3s ease;
}

/* Mobile Typography */
.mobile-heading {
    font-size: 20px;
    font-weight: 700;
    color: var(--text-dark);
    margin: 16px 16px 12px;
}

.mobile-subheading {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-dark);
    margin: 12px 16px 8px;
}

.mobile-text {
    font-size: 14px;
    color: var(--text-light);
    margin: 0 16px 12px;
    line-height: 1.5;
}

/* Mobile Spacing */
.mobile-content {
    padding-bottom: 80px; /* Space for mobile nav */
}

.mobile-section {
    margin-bottom: 24px;
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .mobile-card, .mobile-stat-card {
        border: 2px solid var(--text-dark);
    }
    
    .mobile-form-control {
        border: 2px solid var(--text-dark);
    }
}

/* Large Text Support */
@media (min-resolution: 2dppx) {
    body {
        font-size: 17px;
    }
    
    .mobile-stat-value {
        font-size: 26px;
    }
}

/* Tablet Adjustments */
@media (min-width: 768px) {
    .mobile-stats {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .mobile-card {
        margin: 8px 24px 16px;
    }
    
    .mobile-matrix-position {
        width: 60px;
        height: 60px;
        font-size: 14px;
        margin: 8px;
    }
}

/* Desktop Adjustments */
@media (min-width: 1024px) {
    .mobile-nav {
        display: none;
    }
    
    .mobile-content {
        padding-bottom: 20px;
    }
    
    .mobile-action-bar {
        position: static;
        margin: 20px;
    }
    
    .mobile-fab {
        position: static;
        display: inline-flex;
        border-radius: 8px;
        width: auto;
        height: auto;
        padding: 12px 20px;
    }
}
