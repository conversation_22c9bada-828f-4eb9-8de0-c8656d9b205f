<?php
require('php-includes/connect.php');
include('php-includes/check-login.php');
include('php-includes/wise-api.php');
$userid = $_SESSION['userid'];

// Initialize Wise API
$wise = new WiseAPI();
?>

<!DOCTYPE html>
<html lang="en">

<head>

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>Mlml Website  - Payment Received History</title>

    <!-- Bootstrap Core CSS -->
    <link href="vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">

    <!-- MetisMenu CSS -->
    <link href="vendor/metisMenu/metisMenu.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="dist/css/sb-admin-2.css" rel="stylesheet">

    <!-- Custom Fonts -->
    <link href="vendor/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css">



</head>

<body>

    <div id="wrapper">

        <!-- Navigation -->
        <?php include('php-includes/menu.php'); ?>

        <!-- Page Content -->
        <div id="page-wrapper">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-lg-12">
                        <h1 class="page-header">Payment Received History</h1>
                    </div>
                    <!-- /.col-lg-12 -->
                </div>
                <!-- /.row -->
                <div class="row">
                	<div class="col-lg-12">
                    	<br><br>
                    	<table class="table table-bordered table-striped">
                        	<tr>
                            	<th>S.n.</th>
                                <th>Amount</th>
                                <th>Date</th>
                                <th>Payment Method</th>
                                <th>Status</th>
                            </tr>
                            <?php
							$i=1;
							$query = mysqli_query($con,"select * from income_received where userid='$userid' order by id desc");
							if(mysqli_num_rows($query)>0){
								while($row=mysqli_fetch_array($query)){
									$amount = $row['amount'];
									$date = $row['date'];
									$wise_transfer_id = isset($row['wise_transfer_id']) ? $row['wise_transfer_id'] : null;
									$wise_payment_status = isset($row['wise_payment_status']) ? $row['wise_payment_status'] : null;

									// Determine payment method and status
									$payment_method = $wise_transfer_id ? 'Wise Transfer' : 'Manual';

									// Set status with appropriate label class
									$status_class = 'default';
									$status_text = 'Completed';

									if($wise_transfer_id) {
										if($wise_payment_status == 'processing') {
											$status_class = 'warning';
											$status_text = 'Processing';
										} elseif($wise_payment_status == 'completed') {
											$status_class = 'success';
											$status_text = 'Completed';
										} elseif($wise_payment_status == 'failed') {
											$status_class = 'danger';
											$status_text = 'Failed';
										}
									}
								?>
                                	<tr>
                                    	<td><?php echo $i; ?></td>
                                        <td><?php echo $amount; ?></td>
                                        <td><?php echo $date; ?></td>
                                        <td><?php echo $payment_method; ?></td>
                                        <td>
                                        	<span class="label label-<?php echo $status_class; ?>">
                                        		<?php echo $status_text; ?>
                                        	</span>
                                        	<?php if($wise_transfer_id): ?>
                                        	<br><small>ID: <?php echo $wise_transfer_id; ?></small>
                                        	<?php endif; ?>
                                        </td>
                                    </tr>
                                <?php
									$i++;
								}
							}
							else{
							?>
                            	<tr>
                                	<td colspan="5">You haven't received any payment yet.</td>
                                </tr>
                            <?php
							}
							?>
                        </table>
                    </div>
                </div>
            </div>
            <!-- /.container-fluid -->
        </div>
        <!-- /#page-wrapper -->

    </div>
    <!-- /#wrapper -->

    <!-- jQuery -->
    <script src="vendor/jquery/jquery.min.js"></script>

    <!-- Bootstrap Core JavaScript -->
    <script src="vendor/bootstrap/js/bootstrap.min.js"></script>

    <!-- Metis Menu Plugin JavaScript -->
    <script src="vendor/metisMenu/metisMenu.min.js"></script>

    <!-- Custom Theme JavaScript -->
    <script src="dist/js/sb-admin-2.js"></script>

</body>

</html>
