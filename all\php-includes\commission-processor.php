<?php
/**
 * Commission Processor for MLM Network
 * Handles automated commission calculations and payout processing
 */

require_once 'stitch-payouts.php';

class CommissionProcessor {
    private $con;
    private $stitchPayouts;
    
    // Commission rates for 5x5 matrix
    private $matrixCommissions = [
        1 => 500,   // Level 1: R500 per person (5 people = R2,500)
        2 => 200,   // Level 2: R200 per person (25 people = R5,000)
        3 => 100,   // Level 3: R100 per person (125 people = R12,500)
        4 => 50,    // Level 4: R50 per person (625 people = R31,250)
        5 => 25     // Level 5: R25 per person (3125 people = R78,125)
    ];
    
    public function __construct($database_connection) {
        $this->con = $database_connection;
        $this->stitchPayouts = new StitchPayouts();
    }
    
    /**
     * Process commissions when a new member joins
     * 
     * @param string $newUserId New member's user ID
     * @param string $sponsorId Sponsor's user ID
     * @return array Processing result
     */
    public function processNewMemberCommissions($newUserId, $sponsorId) {
        $results = [
            'success' => false,
            'commissions_added' => 0,
            'total_amount' => 0,
            'errors' => []
        ];
        
        // Get the sponsor's upline chain
        $uplineChain = $this->getUplineChain($sponsorId, 5); // Get 5 levels up
        
        foreach ($uplineChain as $level => $uplineUserId) {
            if (empty($uplineUserId)) continue;
            
            $commissionAmount = $this->matrixCommissions[$level + 1] ?? 0;
            
            if ($commissionAmount > 0) {
                // Add commission to queue
                $added = $this->addCommissionToQueue(
                    $uplineUserId,
                    'matrix',
                    $level + 1,
                    $commissionAmount,
                    $newUserId
                );
                
                if ($added) {
                    $results['commissions_added']++;
                    $results['total_amount'] += $commissionAmount;
                    
                    // Update user's income immediately
                    $this->updateUserIncome($uplineUserId, $commissionAmount, $level + 1);
                }
            }
        }
        
        $results['success'] = $results['commissions_added'] > 0;
        
        return $results;
    }
    
    /**
     * Get upline chain for a user
     * 
     * @param string $userId User ID to start from
     * @param int $levels Number of levels to retrieve
     * @return array Array of upline user IDs indexed by level
     */
    private function getUplineChain($userId, $levels = 5) {
        $upline = [];
        $currentUserId = $userId;
        
        for ($i = 0; $i < $levels; $i++) {
            $query = mysqli_query($this->con, "SELECT sponsor_id FROM tree WHERE userid='$currentUserId'");
            
            if (mysqli_num_rows($query) > 0) {
                $result = mysqli_fetch_assoc($query);
                $sponsorId = $result['sponsor_id'];
                
                if (!empty($sponsorId) && $sponsorId != $currentUserId) {
                    $upline[$i] = $sponsorId;
                    $currentUserId = $sponsorId;
                } else {
                    break; // No more upline
                }
            } else {
                break;
            }
        }
        
        return $upline;
    }
    
    /**
     * Add commission to processing queue
     * 
     * @param string $userId User to receive commission
     * @param string $type Commission type
     * @param int $level Matrix level
     * @param float $amount Commission amount
     * @param string $triggeredBy User who triggered the commission
     * @return bool Success status
     */
    private function addCommissionToQueue($userId, $type, $level, $amount, $triggeredBy) {
        $query = "INSERT INTO commission_queue 
                  (userid, commission_type, level, amount, triggered_by, status, created_date) 
                  VALUES ('$userId', '$type', '$level', '$amount', '$triggeredBy', 'pending', NOW())";
        
        return mysqli_query($this->con, $query);
    }
    
    /**
     * Update user's income record
     * 
     * @param string $userId User ID
     * @param float $amount Commission amount
     * @param int $level Matrix level
     * @return bool Success status
     */
    private function updateUserIncome($userId, $amount, $level) {
        // Get current income data
        $query = mysqli_query($this->con, "SELECT * FROM income WHERE userid='$userId'");
        
        if (mysqli_num_rows($query) > 0) {
            $income = mysqli_fetch_assoc($query);
            
            $newMatrixEarnings = $income['matrix_earnings'] + $amount;
            $newMonthBal = $income['month_bal'] + $amount;
            $newCurrentBal = $income['current_bal'] + $amount;
            $newTotalBal = $income['total_bal'] + $amount;
            $newMatrixLevel = max($income['matrix_level'], $level);
            
            $updateQuery = "UPDATE income SET 
                           matrix_earnings = '$newMatrixEarnings',
                           month_bal = '$newMonthBal',
                           current_bal = '$newCurrentBal',
                           total_bal = '$newTotalBal',
                           matrix_level = '$newMatrixLevel'
                           WHERE userid='$userId'";
            
            mysqli_query($this->con, $updateQuery);
            
            // Record the income transaction
            $today = date("Y-m-d");
            mysqli_query($this->con, "INSERT INTO income_received (userid, amount, date, type) 
                         VALUES ('$userId', '$amount', '$today', 'matrix_level_$level')");
            
            return true;
        }
        
        return false;
    }
    
    /**
     * Process pending commissions and create Stitch payouts
     * 
     * @param int $batchSize Maximum number of commissions to process
     * @param float $minimumAmount Minimum amount to process
     * @return array Processing result
     */
    public function processPendingCommissions($batchSize = 100, $minimumAmount = 50.00) {
        $results = [
            'success' => false,
            'processed_count' => 0,
            'total_amount' => 0,
            'batch_id' => null,
            'errors' => []
        ];
        
        // Get pending commissions grouped by user
        $query = "SELECT userid, SUM(amount) as total_amount, COUNT(*) as commission_count,
                         GROUP_CONCAT(id) as commission_ids
                  FROM commission_queue 
                  WHERE status = 'pending' 
                  GROUP BY userid 
                  HAVING total_amount >= $minimumAmount
                  ORDER BY total_amount DESC 
                  LIMIT $batchSize";
        
        $result = mysqli_query($this->con, $query);
        
        if (mysqli_num_rows($result) == 0) {
            $results['errors'][] = 'No pending commissions found above minimum amount';
            return $results;
        }
        
        $commissions = [];
        $allCommissionIds = [];
        
        while ($row = mysqli_fetch_assoc($result)) {
            $userId = $row['userid'];
            
            // Check if user has Stitch recipient setup
            $recipientQuery = mysqli_query($this->con, "SELECT * FROM stitch_recipients WHERE userid='$userId' AND status='active'");
            
            if (mysqli_num_rows($recipientQuery) == 0) {
                $results['errors'][] = "User $userId does not have active Stitch recipient account";
                continue;
            }
            
            $commissions[] = [
                'userid' => $userId,
                'amount' => $row['total_amount'],
                'commission_count' => $row['commission_count'],
                'commission_ids' => explode(',', $row['commission_ids'])
            ];
            
            $allCommissionIds = array_merge($allCommissionIds, explode(',', $row['commission_ids']));
            $results['processed_count'] += $row['commission_count'];
            $results['total_amount'] += $row['total_amount'];
        }
        
        if (empty($commissions)) {
            $results['errors'][] = 'No valid commissions to process';
            return $results;
        }
        
        // Mark commissions as processing
        $commissionIdsList = implode(',', $allCommissionIds);
        mysqli_query($this->con, "UPDATE commission_queue SET status='processing', processed_date=NOW() WHERE id IN ($commissionIdsList)");
        
        // Process payouts through Stitch
        $payoutResult = $this->stitchPayouts->processMLMPayouts($commissions);
        
        if ($payoutResult['success']) {
            $results['success'] = true;
            $results['batch_id'] = $payoutResult['batch_id'];
            
            // Update commission queue with batch information
            $batchId = $this->getBatchDbId($payoutResult['batch_id']);
            
            if ($batchId) {
                mysqli_query($this->con, "UPDATE commission_queue SET batch_id='$batchId', status='paid' WHERE id IN ($commissionIdsList)");
            }
        } else {
            // Mark commissions as failed and revert to pending
            mysqli_query($this->con, "UPDATE commission_queue SET status='pending', processed_date=NULL WHERE id IN ($commissionIdsList)");
            $results['errors'] = array_merge($results['errors'], $payoutResult['errors']);
        }
        
        return $results;
    }
    
    /**
     * Get database batch ID from Stitch batch ID
     * 
     * @param string $stitchBatchId Stitch batch ID
     * @return int|null Database batch ID
     */
    private function getBatchDbId($stitchBatchId) {
        $query = mysqli_query($this->con, "SELECT id FROM stitch_payout_batches WHERE batch_id='$stitchBatchId'");
        
        if (mysqli_num_rows($query) > 0) {
            $result = mysqli_fetch_assoc($query);
            return $result['id'];
        }
        
        return null;
    }
    
    /**
     * Check and update payout statuses from Stitch
     * 
     * @param string $batchId Stitch batch ID
     * @return array Update result
     */
    public function updatePayoutStatuses($batchId) {
        $results = [
            'success' => false,
            'updated_count' => 0,
            'errors' => []
        ];
        
        $statusResult = $this->stitchPayouts->getPayoutBatchStatus($batchId);
        
        if (isset($statusResult['error'])) {
            $results['errors'][] = 'Failed to get batch status: ' . $statusResult['error'];
            return $results;
        }
        
        if (isset($statusResult['data']['data']['payoutBatch'])) {
            $batch = $statusResult['data']['data']['payoutBatch'];
            
            // Update batch status
            $batchStatus = $batch['status'];
            mysqli_query($this->con, "UPDATE stitch_payout_batches SET status='$batchStatus' WHERE batch_id='$batchId'");
            
            // Update individual payout statuses
            foreach ($batch['payouts'] as $payout) {
                $payoutId = $payout['id'];
                $status = $payout['status'];
                $statusReason = $payout['statusReason'] ?? null;
                
                $updateQuery = "UPDATE stitch_payouts SET status='$status'";
                if ($statusReason) {
                    $statusReason = mysqli_real_escape_string($this->con, $statusReason);
                    $updateQuery .= ", status_reason='$statusReason'";
                }
                $updateQuery .= " WHERE payout_id='$payoutId'";
                
                if (mysqli_query($this->con, $updateQuery)) {
                    $results['updated_count']++;
                }
            }
            
            $results['success'] = true;
        }
        
        return $results;
    }
    
    /**
     * Get commission statistics for a user
     * 
     * @param string $userId User ID
     * @return array Commission statistics
     */
    public function getUserCommissionStats($userId) {
        $stats = [
            'pending_amount' => 0,
            'pending_count' => 0,
            'paid_amount' => 0,
            'paid_count' => 0,
            'total_earned' => 0
        ];
        
        // Get pending commissions
        $pendingQuery = mysqli_query($this->con, "SELECT SUM(amount) as total, COUNT(*) as count FROM commission_queue WHERE userid='$userId' AND status='pending'");
        if ($pendingResult = mysqli_fetch_assoc($pendingQuery)) {
            $stats['pending_amount'] = $pendingResult['total'] ?? 0;
            $stats['pending_count'] = $pendingResult['count'] ?? 0;
        }
        
        // Get paid commissions
        $paidQuery = mysqli_query($this->con, "SELECT SUM(amount) as total, COUNT(*) as count FROM commission_queue WHERE userid='$userId' AND status='paid'");
        if ($paidResult = mysqli_fetch_assoc($paidQuery)) {
            $stats['paid_amount'] = $paidResult['total'] ?? 0;
            $stats['paid_count'] = $paidResult['count'] ?? 0;
        }
        
        $stats['total_earned'] = $stats['pending_amount'] + $stats['paid_amount'];
        
        return $stats;
    }
}
?>
