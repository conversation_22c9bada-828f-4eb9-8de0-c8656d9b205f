<?php
/**
 * Accessibility Settings Handler
 * Handles AJAX requests to change accessibility preferences
 */

session_start();
require_once 'accessibility.php';

// Check if request is POST and contains JSON
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (isset($input['feature'])) {
        $feature = $input['feature'];
        
        // Handle different types of accessibility settings
        if (isset($input['enabled'])) {
            // Boolean settings (high_contrast, large_text, reduced_motion)
            $enabled = (bool)$input['enabled'];
            Accessibility::setPreference($feature, $enabled);
            
            http_response_code(200);
            echo json_encode(['success' => true, 'feature' => $feature, 'enabled' => $enabled]);
            
        } elseif (isset($input['value'])) {
            // Value settings (font_family)
            $value = $input['value'];
            Accessibility::setPreference($feature, $value);
            
            http_response_code(200);
            echo json_encode(['success' => true, 'feature' => $feature, 'value' => $value]);
            
        } else {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Setting value not specified']);
        }
    } else {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Feature not specified']);
    }
} else {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
}
?>
