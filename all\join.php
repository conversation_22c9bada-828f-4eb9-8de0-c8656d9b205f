<?php
include('php-includes/connect.php');
include('php-includes/check-login.php');
$userid = $_SESSION['userid'];

// Redirect to the new join-sponsor.php page
header("Location: join-sponsor.php");
exit;

// The code below is kept for reference but will not be executed
$loan_amount = 2000;
$umgabelo_amount = 500;
$monthly_payment = 534;
$maintenance_fee_month1 = 900;
$maintenance_fee_regular = 2000;
$matrix_type = "5×5"; // Define the matrix type
?>
<?php
//User clicked on join
if(isset($_GET['join_user'])){
	$pin = mysqli_real_escape_string($con,$_GET['pin']);
	$email = mysqli_real_escape_string($con,$_GET['email']);
	$mobile = mysqli_real_escape_string($con,$_GET['mobile']);
	$address = mysqli_real_escape_string($con,$_GET['address']);
	$account = mysqli_real_escape_string($con,$_GET['account']);
	$under_userid = mysqli_real_escape_string($con,$_GET['under_userid']);
	$matrix_position = mysqli_real_escape_string($con,$_GET['matrix_position']);
	$password = "123456";

	$flag = 0;

	if($pin!='' && $email!='' && $mobile!='' && $address!='' && $account!='' && $under_userid!='' && $matrix_position!=''){
		//User filled all the fields.
		if(pin_check($pin)){
			//Pin is ok
			if(email_check($email)){
				//Email is ok
				if(!email_check($under_userid)){
					//Under userid is ok
					if(matrix_position_check($under_userid, $matrix_position)){
						//Matrix position check
						$flag=1;
					}
					else{
						echo '<script>alert("The matrix position you selected is not available.");</script>';
					}
				}
				else{
					//check under userid
					echo '<script>alert("Invalid Under userid.");</script>';
				}
			}
			else{
				//check email
				echo '<script>alert("This user id already available.");</script>';
			}
		}
		else{
			//check pin
			echo '<script>alert("Invalid pin");</script>';
		}
	}
	else{
		//check all fields are fill
		echo '<script>alert("Please fill all the fields.");</script>';
	}

	//Now we are here
	//It means all the information is correct
	//Now we will save all the information
	if($flag==1){
		$today = date("Y-m-d");

		//Insert into User profile
		$query = mysqli_query($con,"insert into user(`email`,`password`,`mobile`,`address`,`account`,`under_userid`,`matrix_position`,`join_date`) values('$email','$password','$mobile','$address','$account','$under_userid','$matrix_position','$today')");

		//Insert into Tree
		//So that later on we can view tree.
		$query = mysqli_query($con,"insert into tree(`userid`,`sponsor_id`) values('$email','$under_userid')");

		//Update matrix position
		$position_field = "position" . $matrix_position;
		$query = mysqli_query($con,"update tree set `$position_field`='$email' where userid='$under_userid'");

		//Update pin status to close
		$query = mysqli_query($con,"update pin_list set status='close' where pin='$pin'");

		//Insert into Income
		$query = mysqli_query($con,"insert into income (`userid`) values('$email')");

		//Insert into Loan table
		$query = mysqli_query($con,"insert into loan (`userid`,`loan_amount`,`remaining_amount`,`monthly_payment`,`start_date`) values('$email','$loan_amount','$loan_amount','$monthly_payment','$today')");

		//Insert first month maintenance fee
		$query = mysqli_query($con,"insert into maintenance_fee (`userid`,`month`,`fee_amount`,`date_paid`,`status`) values('$email','1','$maintenance_fee_month1','$today','paid')");

		//This is the main part to join a user
		//If you will do any mistake here, the site will not work.

		//Update matrix count and calculate commissions
		updateMatrixCount($under_userid, $email);

		echo mysqli_error($con);

		echo '<script>alert("User joined successfully. Loan of R' . $loan_amount . ' advanced.");</script>';
	}

}
?><!--/join user-->
<?php
//functions
function pin_check($pin){
	global $con,$userid;

	$query =mysqli_query($con,"select * from pin_list where pin='$pin' and userid='$userid' and status='open'");
	if(mysqli_num_rows($query)>0){
		return true;
	}
	else{
		return false;
	}
}
function email_check($email){
	global $con;

	$query =mysqli_query($con,"select * from user where email='$email'");
	if(mysqli_num_rows($query)>0){
		return false;
	}
	else{
		return true;
	}
}
function matrix_position_check($email, $position){
	global $con;

	$position_field = "position" . $position;
	$query = mysqli_query($con,"select * from tree where userid='$email'");
	$result = mysqli_fetch_array($query);
	$position_value = $result[$position_field];
	if($position_value == '' || $position_value == NULL){
		return true;
	}
	else{
		return false;
	}
}

function income($userid){
	global $con;
	$data = array();
	$query = mysqli_query($con,"select * from income where userid='$userid'");
	$result = mysqli_fetch_array($query);
	$data['month_bal'] = $result['month_bal'];
	$data['current_bal'] = $result['current_bal'];
	$data['total_bal'] = $result['total_bal'];
	$data['matrix_level'] = $result['matrix_level'];
	$data['matrix_earnings'] = $result['matrix_earnings'];

	return $data;
}

function tree($userid){
	global $con;
	$data = array();
	$query = mysqli_query($con,"select * from tree where userid='$userid'");
	$result = mysqli_fetch_array($query);
	$data['position1'] = $result['position1'];
	$data['position2'] = $result['position2'];
	$data['position3'] = $result['position3'];
	$data['position4'] = $result['position4'];
	$data['position5'] = $result['position5'];
	$data['level'] = $result['level'];
	$data['matrix_count'] = $result['matrix_count'];
	$data['sponsor_id'] = $result['sponsor_id'];

	return $data;
}

function getUnderId($userid){
	global $con;
	$query = mysqli_query($con,"select * from user where email='$userid'");
	$result = mysqli_fetch_array($query);
	return $result['under_userid'];
}

function getMatrixPosition($userid){
	global $con;
	$query = mysqli_query($con,"select * from user where email='$userid'");
	$result = mysqli_fetch_array($query);
	return $result['matrix_position'];
}

function updateMatrixCount($sponsor_id, $new_user_id) {
	global $con;

	// Get the current matrix count for the sponsor
	$query = mysqli_query($con, "SELECT matrix_count FROM tree WHERE userid='$sponsor_id'");
	$result = mysqli_fetch_array($query);
	$current_count = $result['matrix_count'];

	// Increment the matrix count
	$new_count = $current_count + 1;
	mysqli_query($con, "UPDATE tree SET matrix_count='$new_count' WHERE userid='$sponsor_id'");

	// Check if we need to update the level and calculate commissions
	calculateMatrixLevel($sponsor_id);

	// Recursively update upline
	$upline_sponsor = getSponsorId($sponsor_id);
	if($upline_sponsor != "" && $upline_sponsor != NULL) {
		updateMatrixCount($upline_sponsor, $sponsor_id);
	}
}

function getSponsorId($userid) {
	global $con;
	$query = mysqli_query($con, "SELECT sponsor_id FROM tree WHERE userid='$userid'");
	$result = mysqli_fetch_array($query);
	return $result['sponsor_id'];
}

function calculateMatrixLevel($userid) {
	global $con;

	// Get current matrix count
	$query = mysqli_query($con, "SELECT matrix_count FROM tree WHERE userid='$userid'");
	$result = mysqli_fetch_array($query);
	$count = $result['matrix_count'];

	// Calculate level based on matrix count for 5×5 matrix
	$level = 0;
	if($count >= 5) { // Level 1: 5 direct recruits
		$level = 1;
	}
	if($count >= 30) { // Level 2: 5 + 25 = 30 total in network
		$level = 2;
	}
	if($count >= 155) { // Level 3: 5 + 25 + 125 = 155 total
		$level = 3;
	}
	if($count >= 780) { // Level 4: 5 + 25 + 125 + 625 = 780 total
		$level = 4;
	}
	if($count >= 3905) { // Level 5: 5 + 25 + 125 + 625 + 3125 = 3905 total
		$level = 5;
	}

	// Update level in tree table
	mysqli_query($con, "UPDATE tree SET level='$level' WHERE userid='$userid'");

	// Get current income data
	$income_data = income($userid);
	$current_matrix_level = $income_data['matrix_level'];

	// If level increased, calculate and add commission
	if($level > $current_matrix_level) {
		// Get commission amount for this level
		$query = mysqli_query($con, "SELECT commission_amount FROM matrix_commission WHERE level='$level'");
		$result = mysqli_fetch_array($query);
		$commission = $result['commission_amount'];

		// Update income with new commission
		$new_matrix_earnings = $income_data['matrix_earnings'] + $commission;
		$new_month_bal = $income_data['month_bal'] + $commission;
		$new_current_bal = $income_data['current_bal'] + $commission;
		$new_total_bal = $income_data['total_bal'] + $commission;

		mysqli_query($con, "UPDATE income SET
			matrix_level='$level',
			matrix_earnings='$new_matrix_earnings',
			month_bal='$new_month_bal',
			current_bal='$new_current_bal',
			total_bal='$new_total_bal'
			WHERE userid='$userid'");

		// Record the commission payment
		$today = date("Y-m-d");
		mysqli_query($con, "INSERT INTO income_received (userid, amount, date)
			VALUES ('$userid', '$commission', '$today')");
	}
}

?>
<!DOCTYPE html>
<html lang="en">

<head>

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>Mlml Website  - Join</title>

    <!-- Bootstrap Core CSS -->
    <link href="vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">

    <!-- MetisMenu CSS -->
    <link href="vendor/metisMenu/metisMenu.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="dist/css/sb-admin-2.css" rel="stylesheet">

    <!-- Custom Fonts -->
    <link href="vendor/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css">



</head>

<body>

    <div id="wrapper">

        <!-- Navigation -->
        <?php include('php-includes/menu.php'); ?>

        <!-- Page Content -->
        <div id="page-wrapper">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-lg-12">
                        <h1 class="page-header">Join</h1>
                    </div>
                    <!-- /.col-lg-12 -->
                </div>
                <!-- /.row -->
                <div class="row">
                	<div class="col-lg-4">
                    	<form method="get">
                            <div class="form-group">
                                <label>Pin</label>
                                <input type="text" name="pin" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label>Email</label>
                                <input type="email" name="email" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label>Mobile</label>
                                <input type="text" name="mobile" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label>Address</label>
                                <input type="text" name="address" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label>Account</label>
                                <input type="text" name="account" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label>Under Userid</label>
                                <input type="text" name="under_userid" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label>Matrix Position (1-5)</label><br>
                                <select name="matrix_position" class="form-control" required>
                                    <option value="1">Position 1</option>
                                    <option value="2">Position 2</option>
                                    <option value="3">Position 3</option>
                                    <option value="4">Position 4</option>
                                    <option value="5">Position 5</option>
                                </select>
                            </div>

                            <div class="form-group">
                        	<input type="submit" name="join_user" class="btn btn-primary" value="Join">
                        </div>
                        </form>
                    </div>
                </div><!--/.row-->
            </div>
            <!-- /.container-fluid -->
        </div>
        <!-- /#page-wrapper -->

    </div>
    <!-- /#wrapper -->

    <!-- jQuery -->
    <script src="vendor/jquery/jquery.min.js"></script>

    <!-- Bootstrap Core JavaScript -->
    <script src="vendor/bootstrap/js/bootstrap.min.js"></script>

    <!-- Metis Menu Plugin JavaScript -->
    <script src="vendor/metisMenu/metisMenu.min.js"></script>

    <!-- Custom Theme JavaScript -->
    <script src="dist/js/sb-admin-2.js"></script>

</body>

</html>
