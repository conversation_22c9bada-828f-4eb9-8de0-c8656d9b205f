<?php
include('php-includes/check-login.php');
require('php-includes/connect.php');
include('../php-includes/wise-api.php');

// Initialize Wise API
$wise = new WiseAPI();

// Get batch ID from URL
$batch_id = isset($_GET['batch_id']) ? intval($_GET['batch_id']) : 0;

if($batch_id <= 0) {
    header('Location: wise-payments.php');
    exit;
}

// Get batch details
$batch_query = mysqli_query($con, "SELECT * FROM wise_batch_payments WHERE id = $batch_id");

if(mysqli_num_rows($batch_query) == 0) {
    header('Location: wise-payments.php');
    exit;
}

$batch = mysqli_fetch_assoc($batch_query);

// Handle status refresh
if(isset($_POST['refresh_status'])) {
    // In a real implementation, you would call the Wise API to get the latest status
    // For now, we'll just update the status to 'completed' for demonstration
    
    mysqli_query($con, "UPDATE wise_transfers SET status = 'completed', updated_date = NOW() WHERE batch_id = $batch_id");
    mysqli_query($con, "UPDATE income_received SET wise_payment_status = 'completed' WHERE wise_transfer_id IN (SELECT transfer_id FROM wise_transfers WHERE batch_id = $batch_id)");
    
    $success_message = "Payment statuses refreshed successfully.";
}

// Get transfers for this batch
$transfers_query = mysqli_query($con, "SELECT t.*, u.mobile 
                                      FROM wise_transfers t 
                                      JOIN user u ON t.userid = u.email 
                                      WHERE t.batch_id = $batch_id 
                                      ORDER BY t.id");
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>MLM Admin - Wise Payment Details</title>

    <!-- Bootstrap Core CSS -->
    <link href="vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">

    <!-- MetisMenu CSS -->
    <link href="vendor/metisMenu/metisMenu.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="dist/css/sb-admin-2.css" rel="stylesheet">

    <!-- Custom Fonts -->
    <link href="vendor/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css">
</head>

<body>

    <div id="wrapper">

        <!-- Navigation -->
        <?php include('php-includes/menu.php'); ?>

        <!-- Page Content -->
        <div id="page-wrapper">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-lg-12">
                        <h1 class="page-header">Wise Payment Details</h1>
                    </div>
                    <!-- /.col-lg-12 -->
                </div>
                <!-- /.row -->
                
                <?php if(isset($success_message)): ?>
                <div class="alert alert-success">
                    <?php echo $success_message; ?>
                </div>
                <?php endif; ?>
                
                <?php if(isset($error_message)): ?>
                <div class="alert alert-danger">
                    <?php echo $error_message; ?>
                </div>
                <?php endif; ?>
                
                <div class="row">
                    <div class="col-lg-6">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                Batch Payment Information
                            </div>
                            <div class="panel-body">
                                <table class="table table-bordered">
                                    <tr>
                                        <th>Batch ID</th>
                                        <td><?php echo $batch['id']; ?></td>
                                    </tr>
                                    <tr>
                                        <th>Wise Batch Group ID</th>
                                        <td><?php echo $batch['batch_group_id']; ?></td>
                                    </tr>
                                    <tr>
                                        <th>Status</th>
                                        <td>
                                            <span class="label label-<?php echo $batch['status'] == 'completed' ? 'success' : 'warning'; ?>">
                                                <?php echo ucfirst($batch['status']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Currency</th>
                                        <td><?php echo $batch['source_currency']; ?></td>
                                    </tr>
                                    <tr>
                                        <th>Total Amount</th>
                                        <td><?php echo $batch['total_amount']; ?></td>
                                    </tr>
                                    <tr>
                                        <th>Created Date</th>
                                        <td><?php echo $batch['created_date']; ?></td>
                                    </tr>
                                    <tr>
                                        <th>Completed Date</th>
                                        <td><?php echo $batch['completed_date'] ? $batch['completed_date'] : 'N/A'; ?></td>
                                    </tr>
                                </table>
                                
                                <form method="post" action="">
                                    <button type="submit" name="refresh_status" class="btn btn-primary">
                                        <i class="fa fa-refresh"></i> Refresh Payment Statuses
                                    </button>
                                    
                                    <a href="wise-payments.php" class="btn btn-default">
                                        <i class="fa fa-arrow-left"></i> Back to Payments
                                    </a>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-6">
                        <div class="panel panel-info">
                            <div class="panel-heading">
                                Batch Payment Summary
                            </div>
                            <div class="panel-body">
                                <?php
                                $total_transfers = mysqli_num_rows($transfers_query);
                                
                                // Get status counts
                                $status_query = mysqli_query($con, "SELECT status, COUNT(*) as count 
                                                                  FROM wise_transfers 
                                                                  WHERE batch_id = $batch_id 
                                                                  GROUP BY status");
                                
                                $status_counts = [];
                                while($status = mysqli_fetch_assoc($status_query)) {
                                    $status_counts[$status['status']] = $status['count'];
                                }
                                
                                // Reset the transfers query
                                mysqli_data_seek($transfers_query, 0);
                                ?>
                                
                                <div class="row">
                                    <div class="col-lg-6">
                                        <div class="panel panel-green">
                                            <div class="panel-heading">
                                                Total Transfers
                                            </div>
                                            <div class="panel-body">
                                                <h1 class="text-center"><?php echo $total_transfers; ?></h1>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-lg-6">
                                        <div class="panel panel-primary">
                                            <div class="panel-heading">
                                                Total Amount
                                            </div>
                                            <div class="panel-body">
                                                <h1 class="text-center"><?php echo $batch['source_currency'] . ' ' . $batch['total_amount']; ?></h1>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-lg-4">
                                        <div class="panel panel-success">
                                            <div class="panel-heading">
                                                Completed
                                            </div>
                                            <div class="panel-body">
                                                <h3 class="text-center"><?php echo isset($status_counts['completed']) ? $status_counts['completed'] : 0; ?></h3>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-lg-4">
                                        <div class="panel panel-warning">
                                            <div class="panel-heading">
                                                Processing
                                            </div>
                                            <div class="panel-body">
                                                <h3 class="text-center"><?php echo isset($status_counts['processing']) ? $status_counts['processing'] : 0; ?></h3>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-lg-4">
                                        <div class="panel panel-danger">
                                            <div class="panel-heading">
                                                Failed
                                            </div>
                                            <div class="panel-body">
                                                <h3 class="text-center"><?php echo isset($status_counts['failed']) ? $status_counts['failed'] : 0; ?></h3>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-lg-12">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                Individual Transfers
                            </div>
                            <div class="panel-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>User ID</th>
                                                <th>Mobile</th>
                                                <th>Transfer ID</th>
                                                <th>Amount</th>
                                                <th>Status</th>
                                                <th>Created Date</th>
                                                <th>Updated Date</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                            if(mysqli_num_rows($transfers_query) > 0) {
                                                while($transfer = mysqli_fetch_assoc($transfers_query)) {
                                                    echo '<tr>';
                                                    echo '<td>' . $transfer['id'] . '</td>';
                                                    echo '<td>' . $transfer['userid'] . '</td>';
                                                    echo '<td>' . $transfer['mobile'] . '</td>';
                                                    echo '<td>' . $transfer['transfer_id'] . '</td>';
                                                    echo '<td>' . $batch['source_currency'] . ' ' . $transfer['amount'] . '</td>';
                                                    
                                                    // Status with appropriate label
                                                    $status_class = '';
                                                    switch($transfer['status']) {
                                                        case 'completed':
                                                            $status_class = 'success';
                                                            break;
                                                        case 'processing':
                                                            $status_class = 'warning';
                                                            break;
                                                        case 'failed':
                                                            $status_class = 'danger';
                                                            break;
                                                        default:
                                                            $status_class = 'default';
                                                    }
                                                    
                                                    echo '<td><span class="label label-' . $status_class . '">' . ucfirst($transfer['status']) . '</span></td>';
                                                    
                                                    echo '<td>' . $transfer['created_date'] . '</td>';
                                                    echo '<td>' . ($transfer['updated_date'] ? $transfer['updated_date'] : 'N/A') . '</td>';
                                                    echo '</tr>';
                                                }
                                            } else {
                                                echo '<tr><td colspan="8" class="text-center">No transfers found</td></tr>';
                                            }
                                            ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- /.row -->
            </div>
            <!-- /.container-fluid -->
        </div>
        <!-- /#page-wrapper -->

    </div>
    <!-- /#wrapper -->

    <!-- jQuery -->
    <script src="vendor/jquery/jquery.min.js"></script>

    <!-- Bootstrap Core JavaScript -->
    <script src="vendor/bootstrap/js/bootstrap.min.js"></script>

    <!-- Metis Menu Plugin JavaScript -->
    <script src="vendor/metisMenu/metisMenu.min.js"></script>

    <!-- Custom Theme JavaScript -->
    <script src="dist/js/sb-admin-2.js"></script>

</body>

</html>
