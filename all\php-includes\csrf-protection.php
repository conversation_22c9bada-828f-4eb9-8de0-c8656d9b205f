<?php
/**
 * CSRF Protection Utility Class
 * Provides Cross-Site Request Forgery protection for forms
 */
class CSRFProtection {
    
    private static $con;
    
    /**
     * Initialize CSRF protection with database connection
     * 
     * @param mysqli $connection Database connection
     */
    public static function init($connection) {
        self::$con = $connection;
        
        // Clean up expired tokens periodically
        if (rand(1, 100) <= 5) { // 5% chance
            self::cleanupExpiredTokens();
        }
    }
    
    /**
     * Generate a new CSRF token
     * 
     * @param int $expiry_minutes Token expiry time in minutes (default: 60)
     * @return string The generated token
     */
    public static function generateToken($expiry_minutes = 60) {
        if (!self::$con) {
            throw new Exception("CSRF Protection not initialized");
        }
        
        $token = bin2hex(random_bytes(32));
        $user_session = session_id();
        $expires_at = date('Y-m-d H:i:s', time() + ($expiry_minutes * 60));
        
        // Store token in database
        $stmt = self::$con->prepare("INSERT INTO csrf_tokens (token, user_session, expires_at) VALUES (?, ?, ?)");
        $stmt->bind_param("sss", $token, $user_session, $expires_at);
        $stmt->execute();
        $stmt->close();
        
        return $token;
    }
    
    /**
     * Validate a CSRF token
     * 
     * @param string $token The token to validate
     * @param bool $single_use Whether to delete the token after validation (default: true)
     * @return bool True if token is valid, false otherwise
     */
    public static function validateToken($token, $single_use = true) {
        if (!self::$con) {
            throw new Exception("CSRF Protection not initialized");
        }
        
        if (empty($token)) {
            return false;
        }
        
        $user_session = session_id();
        $current_time = date('Y-m-d H:i:s');
        
        // Check if token exists and is not expired
        $stmt = self::$con->prepare("SELECT id FROM csrf_tokens WHERE token = ? AND user_session = ? AND expires_at > ?");
        $stmt->bind_param("sss", $token, $user_session, $current_time);
        $stmt->execute();
        $result = $stmt->get_result();
        $stmt->close();
        
        if ($result->num_rows === 0) {
            return false;
        }
        
        // If single use, delete the token
        if ($single_use) {
            $stmt = self::$con->prepare("DELETE FROM csrf_tokens WHERE token = ?");
            $stmt->bind_param("s", $token);
            $stmt->execute();
            $stmt->close();
        }
        
        return true;
    }
    
    /**
     * Get CSRF token from POST data and validate it
     * 
     * @param bool $single_use Whether to delete the token after validation
     * @return bool True if token is valid, false otherwise
     */
    public static function validatePostToken($single_use = true) {
        $token = $_POST['csrf_token'] ?? '';
        return self::validateToken($token, $single_use);
    }
    
    /**
     * Generate HTML input field for CSRF token
     * 
     * @param int $expiry_minutes Token expiry time in minutes
     * @return string HTML input field
     */
    public static function getTokenField($expiry_minutes = 60) {
        $token = self::generateToken($expiry_minutes);
        return '<input type="hidden" name="csrf_token" value="' . htmlspecialchars($token, ENT_QUOTES, 'UTF-8') . '">';
    }
    
    /**
     * Clean up expired CSRF tokens
     */
    private static function cleanupExpiredTokens() {
        if (!self::$con) {
            return;
        }
        
        $current_time = date('Y-m-d H:i:s');
        $stmt = self::$con->prepare("DELETE FROM csrf_tokens WHERE expires_at <= ?");
        $stmt->bind_param("s", $current_time);
        $stmt->execute();
        $stmt->close();
    }
    
    /**
     * Clean up all tokens for a specific session
     * 
     * @param string $session_id Session ID to clean up (default: current session)
     */
    public static function cleanupSessionTokens($session_id = null) {
        if (!self::$con) {
            return;
        }
        
        if ($session_id === null) {
            $session_id = session_id();
        }
        
        $stmt = self::$con->prepare("DELETE FROM csrf_tokens WHERE user_session = ?");
        $stmt->bind_param("s", $session_id);
        $stmt->execute();
        $stmt->close();
    }
    
    /**
     * Middleware function to check CSRF token for POST requests
     * Call this at the beginning of scripts that handle POST data
     * 
     * @param bool $die_on_failure Whether to die() if validation fails
     * @return bool True if validation passes or not needed, false if validation fails
     */
    public static function checkPostRequest($die_on_failure = true) {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            if (!self::validatePostToken()) {
                if ($die_on_failure) {
                    http_response_code(403);
                    die('CSRF token validation failed. Please refresh the page and try again.');
                }
                return false;
            }
        }
        return true;
    }
}
?>
