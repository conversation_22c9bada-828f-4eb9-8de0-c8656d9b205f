<?php
// Enhanced security and session management
session_start();
require_once 'php-includes/config.php';
require_once 'php-includes/test-connect.php';
require_once 'php-includes/check-login.php';
require_once 'php-includes/secure-database.php';

// Initialize secure database
SecureDatabase::init($con);

// Get user information
$userid = $_SESSION['userid'];
$user_info = SecureDatabase::getRow(
    "SELECT * FROM user WHERE email = ?",
    [$userid]
);

// Get income information
$income_info = SecureDatabase::getRow(
    "SELECT * FROM income WHERE userid = ?",
    [$userid]
);

// Get recent income received
$recent_income = SecureDatabase::getRows(
    "SELECT * FROM income_received WHERE userid = ? ORDER BY date_received DESC LIMIT 5",
    [$userid]
);

// Get matrix information
$matrix_info = SecureDatabase::getRow(
    "SELECT * FROM tree WHERE userid = ?",
    [$userid]
);

// Calculate total earnings
$total_earnings = SecureDatabase::getValue(
    "SELECT COALESCE(SUM(amount), 0) FROM income_received WHERE userid = ? AND status = 'paid'",
    [$userid]
) ?: 0;

// Get downline count
$downline_count = SecureDatabase::getValue(
    "SELECT COUNT(*) FROM user WHERE under_userid = ?",
    [$userid]
) ?: 0;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="Ubuntu Wealth Network - Member Dashboard">
    <title>Dashboard - Ubuntu Wealth Network</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Ubuntu:wght@300;400;500;700&family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --sa-green: #007749;
            --sa-gold: #FFB612;
            --sa-blue: #002395;
            --sa-red: #DE3831;
            --ubuntu-orange: #E95420;
            --warm-white: #FFF8F0;
            --text-dark: #2C3E50;
            --text-light: #6C757D;
            --success-green: #28a745;
            --warning-orange: #fd7e14;
        }
        
        body {
            font-family: 'Open Sans', sans-serif;
            background: linear-gradient(135deg, var(--warm-white) 0%, #ffffff 100%);
            color: var(--text-dark);
        }
        
        .ubuntu-font {
            font-family: 'Ubuntu', sans-serif;
        }
        
        /* Header */
        .header {
            background: linear-gradient(90deg, var(--sa-green) 0%, var(--sa-blue) 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-family: 'Ubuntu', sans-serif;
            font-size: 1.8rem;
            margin: 0;
        }
        
        .user-info {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        /* Dashboard Cards */
        .dashboard-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            border: none;
            height: 100%;
            transition: all 0.3s ease;
        }
        
        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }
        
        .card-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        
        .card-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .card-label {
            color: var(--text-light);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        /* Stats Cards */
        .stats-earnings {
            background: linear-gradient(135deg, var(--success-green) 0%, #20c997 100%);
            color: white;
        }
        
        .stats-downline {
            background: linear-gradient(135deg, var(--sa-blue) 0%, #6610f2 100%);
            color: white;
        }
        
        .stats-level {
            background: linear-gradient(135deg, var(--sa-gold) 0%, var(--warning-orange) 100%);
            color: white;
        }
        
        .stats-pending {
            background: linear-gradient(135deg, var(--ubuntu-orange) 0%, var(--sa-red) 100%);
            color: white;
        }
        
        /* Recent Activity */
        .activity-item {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            border-left: 4px solid var(--sa-green);
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .activity-amount {
            font-weight: 600;
            color: var(--success-green);
        }
        
        .activity-date {
            font-size: 0.8rem;
            color: var(--text-light);
        }
        
        /* Matrix Visualization */
        .matrix-container {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        
        .matrix-position {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0.5rem;
            font-weight: 600;
            font-size: 0.8rem;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .matrix-filled {
            background: linear-gradient(135deg, var(--sa-green) 0%, var(--success-green) 100%);
            color: white;
        }
        
        .matrix-empty {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            color: var(--text-light);
        }
        
        .matrix-you {
            background: linear-gradient(135deg, var(--sa-gold) 0%, var(--ubuntu-orange) 100%);
            color: white;
            font-weight: 700;
        }
        
        /* Navigation */
        .nav-pills .nav-link {
            border-radius: 25px;
            padding: 0.8rem 1.5rem;
            margin: 0 0.25rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .nav-pills .nav-link.active {
            background: linear-gradient(45deg, var(--sa-green) 0%, var(--sa-blue) 100%);
        }
        
        .nav-pills .nav-link:not(.active) {
            color: var(--text-dark);
            background: white;
            border: 1px solid #dee2e6;
        }
        
        .nav-pills .nav-link:not(.active):hover {
            background: var(--warm-white);
            border-color: var(--sa-green);
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 1.4rem;
            }
            
            .card-value {
                font-size: 1.5rem;
            }
            
            .dashboard-card {
                padding: 1rem;
                margin-bottom: 1rem;
            }
            
            .matrix-position {
                width: 45px;
                height: 45px;
                font-size: 0.7rem;
            }
        }
        
        /* Motivational Elements */
        .motivation-card {
            background: linear-gradient(135deg, var(--sa-green) 0%, var(--sa-blue) 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .motivation-quote {
            font-size: 1.2rem;
            font-style: italic;
            margin-bottom: 1rem;
        }
        
        .motivation-author {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        /* Progress Bars */
        .progress {
            height: 10px;
            border-radius: 10px;
            background: #e9ecef;
        }
        
        .progress-bar {
            background: linear-gradient(90deg, var(--sa-green) 0%, var(--success-green) 100%);
            border-radius: 10px;
        }
        
        /* Action Buttons */
        .action-btn {
            background: linear-gradient(45deg, var(--sa-gold) 0%, var(--ubuntu-orange) 100%);
            border: none;
            color: white;
            padding: 0.8rem 1.5rem;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        
        /* SA Flag Accent */
        .sa-accent {
            background: linear-gradient(90deg, 
                var(--sa-green) 0%, 
                var(--sa-gold) 25%, 
                var(--sa-blue) 50%, 
                var(--sa-red) 75%, 
                var(--sa-green) 100%);
            height: 4px;
            width: 100%;
        }
    </style>
</head>

<body>
    <!-- Header -->
    <div class="header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1>
                        <i class="fas fa-hands-helping me-2"></i>
                        Ubuntu Wealth Network
                    </h1>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="user-info">
                        <i class="fas fa-user me-1"></i>
                        Welcome, <?php echo SecureDatabase::escapeOutput($user_info['email'] ?? 'Member'); ?>
                        <a href="logout.php" class="text-white ms-3" style="text-decoration: none;">
                            <i class="fas fa-sign-out-alt me-1"></i>Logout
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- SA Flag Accent -->
    <div class="sa-accent"></div>

    <!-- Main Content -->
    <div class="container-fluid py-4">
        <!-- Motivational Quote -->
        <div class="row">
            <div class="col-12">
                <div class="motivation-card">
                    <div class="motivation-quote">
                        "Ubuntu: I am because we are. Together we build wealth, together we rise!"
                    </div>
                    <div class="motivation-author">
                        - Ubuntu Philosophy
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard Stats -->
        <div class="row g-3 mb-4">
            <div class="col-6 col-md-3">
                <div class="dashboard-card stats-earnings">
                    <div class="card-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="card-value">R<?php echo number_format($total_earnings, 2); ?></div>
                    <div class="card-label">Total Earnings</div>
                </div>
            </div>
            <div class="col-6 col-md-3">
                <div class="dashboard-card stats-downline">
                    <div class="card-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="card-value"><?php echo $downline_count; ?></div>
                    <div class="card-label">Team Members</div>
                </div>
            </div>
            <div class="col-6 col-md-3">
                <div class="dashboard-card stats-level">
                    <div class="card-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <div class="card-value">Level <?php echo $matrix_info['level'] ?? 1; ?></div>
                    <div class="card-label">Current Level</div>
                </div>
            </div>
            <div class="col-6 col-md-3">
                <div class="dashboard-card stats-pending">
                    <div class="card-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="card-value">
                        <?php
                        $pending_count = SecureDatabase::getValue(
                            "SELECT COUNT(*) FROM income_received WHERE userid = ? AND status = 'pending'",
                            [$userid]
                        ) ?: 0;
                        echo $pending_count;
                        ?>
                    </div>
                    <div class="card-label">Pending Payments</div>
                </div>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <div class="row mb-4">
            <div class="col-12">
                <ul class="nav nav-pills justify-content-center" id="dashboardTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="overview-tab" data-bs-toggle="pill" data-bs-target="#overview" type="button" role="tab">
                            <i class="fas fa-chart-pie me-2"></i>Overview
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="matrix-tab" data-bs-toggle="pill" data-bs-target="#matrix" type="button" role="tab">
                            <i class="fas fa-sitemap me-2"></i>My Matrix
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="income-tab" data-bs-toggle="pill" data-bs-target="#income" type="button" role="tab">
                            <i class="fas fa-money-bill-wave me-2"></i>Income
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="team-tab" data-bs-toggle="pill" data-bs-target="#team" type="button" role="tab">
                            <i class="fas fa-users me-2"></i>My Team
                        </button>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Tab Content -->
        <div class="tab-content" id="dashboardTabsContent">
            <!-- Overview Tab -->
            <div class="tab-pane fade show active" id="overview" role="tabpanel">
                <div class="row g-4">
                    <!-- Quick Actions -->
                    <div class="col-md-6">
                        <div class="dashboard-card">
                            <h5 class="ubuntu-font mb-3">
                                <i class="fas fa-rocket me-2" style="color: var(--sa-green);"></i>
                                Quick Actions
                            </h5>
                            <div class="d-grid gap-2">
                                <a href="join-sponsor.php" class="action-btn">
                                    <i class="fas fa-user-plus me-2"></i>
                                    Invite New Member
                                </a>
                                <a href="income.php" class="btn btn-outline-primary">
                                    <i class="fas fa-chart-line me-2"></i>
                                    View Income Report
                                </a>
                                <a href="tree.php" class="btn btn-outline-success">
                                    <i class="fas fa-sitemap me-2"></i>
                                    View Full Matrix
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="col-md-6">
                        <div class="dashboard-card">
                            <h5 class="ubuntu-font mb-3">
                                <i class="fas fa-history me-2" style="color: var(--sa-green);"></i>
                                Recent Income
                            </h5>
                            <div style="max-height: 300px; overflow-y: auto;">
                                <?php if (!empty($recent_income)): ?>
                                    <?php foreach ($recent_income as $income): ?>
                                        <div class="activity-item">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <div class="activity-amount">
                                                        +R<?php echo number_format($income['amount'], 2); ?>
                                                    </div>
                                                    <div class="activity-date">
                                                        <?php echo date('M j, Y', strtotime($income['date_received'])); ?>
                                                    </div>
                                                </div>
                                                <div>
                                                    <span class="badge" style="background: var(--success-green);">
                                                        Level <?php echo $income['commission_level']; ?>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <div class="text-center text-muted py-4">
                                        <i class="fas fa-info-circle mb-2" style="font-size: 2rem;"></i>
                                        <p>No income received yet. Start building your team!</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Matrix Tab -->
            <div class="tab-pane fade" id="matrix" role="tabpanel">
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="matrix-container">
                            <h5 class="ubuntu-font text-center mb-4">
                                <i class="fas fa-sitemap me-2" style="color: var(--sa-green);"></i>
                                Your 5×5 Matrix
                            </h5>

                            <!-- Matrix Visualization -->
                            <div class="text-center">
                                <!-- You (Center) -->
                                <div class="d-flex justify-content-center mb-3">
                                    <div class="matrix-position matrix-you">
                                        <div>YOU</div>
                                    </div>
                                </div>

                                <!-- Level 1 (5 positions) -->
                                <div class="d-flex justify-content-center mb-3">
                                    <?php
                                    $positions = ['position1', 'position2', 'position3', 'position4', 'position5'];
                                    foreach ($positions as $i => $position):
                                        $member = $matrix_info[$position] ?? null;
                                        $class = $member ? 'matrix-filled' : 'matrix-empty';
                                        $content = $member ? ($i + 1) : '?';
                                    ?>
                                        <div class="matrix-position <?php echo $class; ?>">
                                            <div><?php echo $content; ?></div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>

                                <!-- Matrix Progress -->
                                <div class="mt-4">
                                    <?php
                                    $filled_positions = 0;
                                    foreach ($positions as $position) {
                                        if (!empty($matrix_info[$position])) {
                                            $filled_positions++;
                                        }
                                    }
                                    $progress_percentage = ($filled_positions / 5) * 100;
                                    ?>
                                    <div class="mb-2">
                                        <small class="text-muted">Matrix Progress: <?php echo $filled_positions; ?>/5 positions filled</small>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar" role="progressbar" style="width: <?php echo $progress_percentage; ?>%"></div>
                                    </div>
                                </div>

                                <!-- Next Level Preview -->
                                <?php if ($filled_positions === 5): ?>
                                    <div class="alert alert-success mt-4" role="alert">
                                        <i class="fas fa-trophy me-2"></i>
                                        <strong>Congratulations!</strong> Your matrix is complete! You're ready for the next level.
                                    </div>
                                <?php else: ?>
                                    <div class="alert alert-info mt-4" role="alert">
                                        <i class="fas fa-info-circle me-2"></i>
                                        You need <?php echo (5 - $filled_positions); ?> more team members to complete this level.
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Income Tab -->
            <div class="tab-pane fade" id="income" role="tabpanel">
                <div class="row g-4">
                    <div class="col-md-6">
                        <div class="dashboard-card">
                            <h5 class="ubuntu-font mb-3">
                                <i class="fas fa-chart-bar me-2" style="color: var(--sa-green);"></i>
                                Income Breakdown
                            </h5>
                            <?php
                            $income_by_level = SecureDatabase::getRows(
                                "SELECT commission_level, SUM(amount) as total, COUNT(*) as count
                                 FROM income_received
                                 WHERE userid = ? AND status = 'paid'
                                 GROUP BY commission_level
                                 ORDER BY commission_level",
                                [$userid]
                            );
                            ?>

                            <?php if (!empty($income_by_level)): ?>
                                <?php foreach ($income_by_level as $level): ?>
                                    <div class="d-flex justify-content-between align-items-center mb-3 p-3"
                                         style="background: var(--warm-white); border-radius: 10px;">
                                        <div>
                                            <strong>Level <?php echo $level['commission_level']; ?></strong>
                                            <br><small class="text-muted"><?php echo $level['count']; ?> payments</small>
                                        </div>
                                        <div class="text-end">
                                            <strong style="color: var(--success-green);">
                                                R<?php echo number_format($level['total'], 2); ?>
                                            </strong>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-chart-bar mb-2" style="font-size: 3rem; opacity: 0.3;"></i>
                                    <p>Start building your team to see income breakdown</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="dashboard-card">
                            <h5 class="ubuntu-font mb-3">
                                <i class="fas fa-target me-2" style="color: var(--sa-green);"></i>
                                Income Goals
                            </h5>

                            <!-- Goal Progress -->
                            <div class="mb-4">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Monthly Goal: R10,000</span>
                                    <span><?php echo min(100, ($total_earnings / 10000) * 100); ?>%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar" style="width: <?php echo min(100, ($total_earnings / 10000) * 100); ?>%"></div>
                                </div>
                            </div>

                            <!-- Potential Earnings -->
                            <div class="p-3" style="background: linear-gradient(135deg, var(--sa-green) 0%, var(--sa-blue) 100%);
                                 border-radius: 10px; color: white;">
                                <h6 class="mb-2">Potential Monthly Income</h6>
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div style="font-size: 1.2rem; font-weight: 600;">R25,000</div>
                                        <small>With 5 members</small>
                                    </div>
                                    <div class="col-6">
                                        <div style="font-size: 1.2rem; font-weight: 600;">R125,000</div>
                                        <small>Full matrix</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Team Tab -->
            <div class="tab-pane fade" id="team" role="tabpanel">
                <div class="row">
                    <div class="col-12">
                        <div class="dashboard-card">
                            <h5 class="ubuntu-font mb-3">
                                <i class="fas fa-users me-2" style="color: var(--sa-green);"></i>
                                My Team Members
                            </h5>

                            <?php
                            $team_members = SecureDatabase::getRows(
                                "SELECT email, join_date, matrix_position FROM user WHERE under_userid = ? ORDER BY join_date DESC",
                                [$userid]
                            );
                            ?>

                            <?php if (!empty($team_members)): ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead style="background: var(--warm-white);">
                                            <tr>
                                                <th>Member</th>
                                                <th>Position</th>
                                                <th>Joined</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($team_members as $member): ?>
                                                <tr>
                                                    <td>
                                                        <i class="fas fa-user-circle me-2" style="color: var(--sa-green);"></i>
                                                        <?php echo SecureDatabase::escapeOutput($member['email']); ?>
                                                    </td>
                                                    <td>
                                                        <span class="badge" style="background: var(--sa-blue);">
                                                            Position <?php echo $member['matrix_position']; ?>
                                                        </span>
                                                    </td>
                                                    <td><?php echo date('M j, Y', strtotime($member['join_date'])); ?></td>
                                                    <td>
                                                        <span class="badge" style="background: var(--success-green);">
                                                            Active
                                                        </span>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-users" style="font-size: 4rem; color: var(--text-light); opacity: 0.3;"></i>
                                    <h6 class="mt-3 mb-2">No Team Members Yet</h6>
                                    <p class="text-muted mb-4">Start building your team to see members here</p>
                                    <a href="join-sponsor.php" class="action-btn">
                                        <i class="fas fa-user-plus me-2"></i>
                                        Invite Your First Member
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Auto-refresh dashboard data every 5 minutes
        setInterval(function() {
            location.reload();
        }, 300000);

        // Add click animations to cards
        document.querySelectorAll('.dashboard-card').forEach(card => {
            card.addEventListener('click', function() {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });

        // Show success message for completed actions
        if (window.location.search.includes('success=1')) {
            const alert = document.createElement('div');
            alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
            alert.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 300px;';
            alert.innerHTML = `
                <i class="fas fa-check-circle me-2"></i>
                Action completed successfully!
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alert);

            setTimeout(() => {
                alert.remove();
            }, 5000);
        }
    </script>
</body>
</html>
