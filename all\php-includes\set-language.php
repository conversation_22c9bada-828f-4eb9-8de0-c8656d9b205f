<?php
/**
 * Language Setting Handler
 * Handles AJAX requests to change language
 */

session_start();
require_once 'localization.php';

// Check if request is POST and contains JSON
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (isset($input['language'])) {
        $language = $input['language'];
        
        // Validate language
        $availableLanguages = array_keys(Localization::getAvailableLanguages());
        if (in_array($language, $availableLanguages)) {
            Localization::setLanguage($language);
            
            http_response_code(200);
            echo json_encode(['success' => true, 'language' => $language]);
        } else {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Invalid language']);
        }
    } else {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Language not specified']);
    }
} else {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
}
?>
