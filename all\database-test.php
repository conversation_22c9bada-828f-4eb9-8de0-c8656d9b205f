<?php
/**
 * Database Connection Diagnostic Tool
 * This script helps diagnose database connection issues
 */

echo "<h2>Database Connection Diagnostic</h2>";

// Test different common ports (3308 first since 3306 is occupied)
$test_configs = [
    ['host' => 'localhost', 'port' => 3308, 'user' => 'root', 'pass' => '', 'db' => 'mlm'],
    ['host' => 'localhost', 'port' => 3306, 'user' => 'root', 'pass' => '', 'db' => 'mlm'],
    ['host' => '127.0.0.1', 'port' => 3308, 'user' => 'root', 'pass' => '', 'db' => 'mlm'],
    ['host' => '127.0.0.1', 'port' => 3306, 'user' => 'root', 'pass' => '', 'db' => 'mlm'],
];

echo "<h3>Testing Database Connections:</h3>";

foreach ($test_configs as $index => $config) {
    echo "<div style='margin: 10px; padding: 10px; border: 1px solid #ccc;'>";
    echo "<strong>Test " . ($index + 1) . ":</strong> ";
    echo "Host: {$config['host']}, Port: {$config['port']}, Database: {$config['db']}<br>";
    
    // Test connection
    $connection = @mysqli_connect(
        $config['host'], 
        $config['user'], 
        $config['pass'], 
        $config['db'], 
        $config['port']
    );
    
    if ($connection) {
        echo "<span style='color: green;'>✅ SUCCESS - Connection established!</span><br>";
        echo "MySQL Version: " . mysqli_get_server_info($connection) . "<br>";
        
        // Test if MLM database exists
        $db_check = mysqli_select_db($connection, $config['db']);
        if ($db_check) {
            echo "<span style='color: green;'>✅ Database '{$config['db']}' exists</span><br>";
            
            // Check for some tables
            $tables_to_check = ['user', 'tree', 'income', 'pin_list'];
            foreach ($tables_to_check as $table) {
                $table_check = mysqli_query($connection, "SHOW TABLES LIKE '$table'");
                if (mysqli_num_rows($table_check) > 0) {
                    echo "<span style='color: green;'>✅ Table '$table' exists</span><br>";
                } else {
                    echo "<span style='color: orange;'>⚠️ Table '$table' missing</span><br>";
                }
            }
        } else {
            echo "<span style='color: red;'>❌ Database '{$config['db']}' does not exist</span><br>";
        }
        
        mysqli_close($connection);
        echo "<hr><strong>🎉 Use this configuration in your connect.php file!</strong>";
        break; // Stop testing once we find a working connection
    } else {
        echo "<span style='color: red;'>❌ FAILED - " . mysqli_connect_error() . "</span>";
    }
    echo "</div>";
}

echo "<h3>XAMPP Status Check:</h3>";
echo "<div style='margin: 10px; padding: 10px; border: 1px solid #ccc;'>";

// Check if we can connect to any MySQL instance
$mysql_running = false;
foreach ([3306, 3308, 3307] as $port) {
    $test_connection = @mysqli_connect('localhost', 'root', '', '', $port);
    if ($test_connection) {
        echo "<span style='color: green;'>✅ MySQL is running on port $port</span><br>";
        $mysql_running = true;
        mysqli_close($test_connection);
    }
}

if (!$mysql_running) {
    echo "<span style='color: red;'>❌ MySQL is not running on any common ports</span><br>";
    echo "<strong>Solutions:</strong><br>";
    echo "1. Open XAMPP Control Panel<br>";
    echo "2. Click 'Start' next to MySQL<br>";
    echo "3. Wait for it to show 'Running' status<br>";
    echo "4. Refresh this page<br>";
}

echo "</div>";

echo "<h3>Database Creation Script:</h3>";
echo "<div style='margin: 10px; padding: 10px; border: 1px solid #ccc; background: #f5f5f5;'>";
echo "<p>If the MLM database doesn't exist, you can create it using phpMyAdmin or run this SQL:</p>";
echo "<code>CREATE DATABASE mlm CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;</code>";
echo "</div>";

echo "<h3>Next Steps:</h3>";
echo "<div style='margin: 10px; padding: 10px; border: 1px solid #ccc;'>";
echo "<ol>";
echo "<li>Ensure XAMPP MySQL is running</li>";
echo "<li>Create the 'mlm' database if it doesn't exist</li>";
echo "<li>Import your database schema</li>";
echo "<li>Update connect.php with the working configuration</li>";
echo "<li>Run the Stitch tables SQL: <code>database/stitch_tables.sql</code></li>";
echo "</ol>";
echo "</div>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
code { background: #f0f0f0; padding: 2px 4px; border-radius: 3px; }
</style>
