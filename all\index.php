<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="Ubuntu Wealth Network - Building Financial Freedom Together in South Africa">
    <meta name="author" content="Ubuntu Wealth Network">
    <meta name="keywords" content="MLM, South Africa, financial freedom, Ubuntu, community, income opportunity">

    <title>Ubuntu Wealth Network - Building Financial Freedom Together</title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Ubuntu:wght@300;400;500;700&family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Legacy CSS for compatibility -->
    <link href="vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="vendor/metisMenu/metisMenu.min.css" rel="stylesheet">
    <link href="dist/css/sb-admin-2.css" rel="stylesheet">
    <link href="vendor/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css">

    <!-- Mobile-First Ubuntu CSS -->
    <link href="dist/css/ubuntu-mobile.css" rel="stylesheet">

    <style>
        :root {
            --sa-green: #007749;
            --sa-gold: #FFB612;
            --sa-blue: #002395;
            --sa-red: #DE3831;
            --ubuntu-orange: #E95420;
            --warm-white: #FFF8F0;
            --text-dark: #2C3E50;
            --text-light: #6C757D;
        }

        body {
            font-family: 'Open Sans', sans-serif;
            background: linear-gradient(135deg, var(--warm-white) 0%, #ffffff 100%);
            min-height: 100vh;
        }

        .ubuntu-font {
            font-family: 'Ubuntu', sans-serif;
        }

        /* SA Flag Accent */
        .sa-accent {
            background: linear-gradient(90deg,
                var(--sa-green) 0%,
                var(--sa-gold) 25%,
                var(--sa-blue) 50%,
                var(--sa-red) 75%,
                var(--sa-green) 100%);
            height: 4px;
            width: 100%;
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, var(--sa-green) 0%, var(--sa-blue) 100%);
            color: white;
            padding: 3rem 0;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-title {
            font-family: 'Ubuntu', sans-serif;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .hero-subtitle {
            font-size: 1.1rem;
            margin-bottom: 1.5rem;
            opacity: 0.95;
        }

        /* Login Panel */
        .login-container {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
            margin-top: -50px;
            position: relative;
            z-index: 3;
        }

        .login-title {
            font-family: 'Ubuntu', sans-serif;
            font-size: 1.8rem;
            font-weight: 600;
            text-align: center;
            margin-bottom: 1.5rem;
            color: var(--sa-green);
        }

        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.8rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            min-height: 44px;
        }

        .form-control:focus {
            border-color: var(--sa-green);
            box-shadow: 0 0 0 0.2rem rgba(0, 119, 73, 0.25);
        }

        .btn-login {
            background: linear-gradient(45deg, var(--sa-green) 0%, var(--sa-blue) 100%);
            border: none;
            padding: 0.8rem 2rem;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 10px;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            min-height: 44px;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }

        /* Features Section */
        .features-section {
            padding: 3rem 0;
            background: var(--warm-white);
        }

        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            height: 100%;
            border: 2px solid transparent;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            border-color: var(--sa-gold);
        }

        .feature-icon {
            font-size: 2.5rem;
            color: var(--sa-green);
            margin-bottom: 1rem;
        }

        .feature-title {
            font-family: 'Ubuntu', sans-serif;
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-dark);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }

            .hero-subtitle {
                font-size: 1rem;
            }

            .login-container {
                padding: 1.5rem;
                margin: 1rem;
                margin-top: -30px;
            }

            .feature-card {
                margin-bottom: 1rem;
            }
        }
    </style>
</head>

<body>
    <!-- SA Flag Accent -->
    <div class="sa-accent"></div>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6 hero-content">
                    <h1 class="hero-title">
                        <i class="fas fa-hands-helping me-2"></i>
                        Ubuntu Wealth Network
                    </h1>
                    <p class="hero-subtitle">
                        Building Financial Freedom Together in South Africa<br>
                        <em>"Ubuntu: I am because we are"</em>
                    </p>
                </div>
                <div class="col-lg-6 text-center">
                    <div class="hero-image">
                        <i class="fas fa-users" style="font-size: 8rem; opacity: 0.3;"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Login Section -->
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="login-container">
                    <h2 class="login-title">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        Member Login
                    </h2>

                    <!-- Preserve original form action and method -->
                    <form method="post" action="login.php">
                        <div class="mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope me-2"></i>Email Address
                            </label>
                            <input class="form-control" placeholder="Enter your email" name="email" type="email" id="email" autofocus required>
                        </div>
                        <div class="mb-4">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock me-2"></i>Password
                            </label>
                            <input class="form-control" placeholder="Enter your password" name="password" type="password" id="password" required>
                        </div>
                        <button type="submit" class="btn btn-login">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            Login to Dashboard
                        </button>
                    </form>

                    <div class="text-center mt-4">
                        <p class="mb-2">
                            <a href="forgot-password.php" style="color: var(--sa-green); text-decoration: none;">
                                <i class="fas fa-key me-1"></i>Forgot Password?
                            </a>
                        </p>
                        <hr style="margin: 1.5rem 0;">
                        <p class="mb-3" style="color: var(--text-light);">New to Ubuntu Wealth Network?</p>
                        <a href="register.php" class="btn" style="background: linear-gradient(45deg, var(--sa-gold) 0%, var(--ubuntu-orange) 100%);
                           color: white; border: none; padding: 0.8rem 2rem; border-radius: 10px; text-decoration: none; font-weight: 600;">
                            <i class="fas fa-user-plus me-2"></i>
                            Join Our Community
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Section -->
    <section class="features-section">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-4">
                    <h2 class="ubuntu-font" style="font-size: 2rem; color: var(--sa-green); margin-bottom: 1rem;">
                        Your Path to Financial Freedom
                    </h2>
                    <p class="lead" style="color: var(--text-light);">
                        Join thousands of South Africans building wealth through our 5×5 matrix system
                    </p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-md-6 col-lg-3">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-seedling"></i>
                        </div>
                        <h3 class="feature-title">Start Small, Grow Big</h3>
                        <p>Begin with just R2,000 and watch your investment grow through our structured matrix system.</p>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="feature-title">Community Support</h3>
                        <p>Join a network of like-minded South Africans all working together towards financial independence.</p>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3 class="feature-title">Multiple Income Levels</h3>
                        <p>Earn from R1,000 to R625,000 as you progress through our 5-level commission structure.</p>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h3 class="feature-title">Mobile Friendly</h3>
                        <p>Manage your business from anywhere using your smartphone. No expensive equipment needed.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer style="background: var(--text-dark); color: white; padding: 2rem 0; text-align: center;">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="sa-accent" style="margin: 0 auto 1rem; width: 200px;"></div>
                    <p>&copy; 2024 Ubuntu Wealth Network. Building Financial Freedom Together.</p>
                    <p style="color: var(--sa-gold); font-style: italic; margin-top: 1rem;">
                        "Ubuntu: I am because we are"
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Legacy JavaScript for compatibility -->
    <script src="vendor/jquery/jquery.min.js"></script>
    <script src="vendor/bootstrap/js/bootstrap.min.js"></script>
    <script src="vendor/metisMenu/metisMenu.min.js"></script>
    <script src="dist/js/sb-admin-2.js"></script>

    <!-- Custom JavaScript -->
    <script>
        // Form validation and enhancement
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.querySelector('form[action="login.php"]');
            const submitBtn = loginForm.querySelector('button[type="submit"]');

            loginForm.addEventListener('submit', function(e) {
                const email = this.querySelector('input[name="email"]').value.trim();
                const password = this.querySelector('input[name="password"]').value.trim();

                if (!email || !password) {
                    e.preventDefault();
                    alert('Please fill in all fields');
                    return false;
                }

                // Add loading state
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Logging in...';
                submitBtn.disabled = true;
            });

            // Smooth scrolling for any anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Add animation on scroll for feature cards
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // Initially hide feature cards for animation
            document.querySelectorAll('.feature-card').forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(card);
            });
        });
    </script>

</body>

</html>
