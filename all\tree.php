<?php
include('php-includes/connect.php');
include('php-includes/check-login.php');
$userid = $_SESSION['userid'];
$search = $userid;
?>
<?php
function tree_data($userid){
global $con;
$data = array();
$query = mysqli_query($con,"select * from tree where userid='$userid'");
$result = mysqli_fetch_array($query);
$data['position1'] = $result['position1'];
$data['position2'] = $result['position2'];
$data['position3'] = $result['position3'];
$data['position4'] = $result['position4'];
$data['position5'] = $result['position5'];
$data['level'] = $result['level'];
$data['matrix_count'] = $result['matrix_count'];
$data['sponsor_id'] = $result['sponsor_id'];
return $data;
}

function get_user_info($userid){
global $con;
$data = array();
if($userid != "" && $userid != NULL) {
    $query = mysqli_query($con,"select * from user where email='$userid'");
    if(mysqli_num_rows($query) > 0) {
        $result = mysqli_fetch_array($query);
        $data['matrix_position'] = $result['matrix_position'];
        $data['join_date'] = $result['join_date'];

        // Get income data
        $income_query = mysqli_query($con,"select * from income where userid='$userid'");
        $income_result = mysqli_fetch_array($income_query);
        $data['matrix_level'] = $income_result['matrix_level'];
        $data['matrix_earnings'] = $income_result['matrix_earnings'];

        // Get loan data
        $loan_query = mysqli_query($con,"select * from loan where userid='$userid'");
        $loan_result = mysqli_fetch_array($loan_query);
        $data['loan_remaining'] = $loan_result['remaining_amount'];
    }
}
return $data;
}
?>
<?php
if(isset($_GET['search-id'])){
$search_id = mysqli_real_escape_string($con,$_GET['search-id']);
if($search_id!=""){
$query_check = mysqli_query($con,"select * from user where email='$search_id'");
if(mysqli_num_rows($query_check)>0){
$search = $search_id;
}
else{
echo '<script>alert("Access Denied");window.location.assign("tree.php");</script>';
}
}
else{
echo '<script>alert("Access Denied");window.location.assign("tree.php");</script>';
}
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta name="description" content="">
<meta name="author" content="">
<title>Mlml Website - Tree</title>
<!-- Bootstrap Core CSS -->
<link href="vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
<!-- MetisMenu CSS -->
<link href="vendor/metisMenu/metisMenu.min.css" rel="stylesheet">
<!-- Custom CSS -->
<link href="dist/css/sb-admin-2.css" rel="stylesheet">
<!-- Custom Fonts -->
<link href="vendor/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css">
</head>
<body>
<div id="wrapper">
<!-- Navigation -->
<?php include('php-includes/menu.php'); ?>
<!-- Page Content -->
<div id="page-wrapper">
<div class="container-fluid">
<div class="row">
<div class="col-lg-12">
<h1 class="page-header">Tree</h1>
</div>
<!-- /.col-lg-12 -->
</div>
<!-- /.row -->
<div class="row">
<div class="col-lg-12">
<div class="panel panel-primary">
    <div class="panel-heading">
        <h3 class="panel-title">5×5 Matrix Details</h3>
    </div>
    <div class="panel-body">
        <?php
        $data = tree_data($search);
        $user_info = get_user_info($search);
        ?>
        <div class="row">
            <div class="col-md-6">
                <h4>User: <?php echo $search; ?></h4>
                <p>Matrix Level: <?php echo $user_info['matrix_level']; ?></p>
                <p>Total Downline: <?php echo $data['matrix_count']; ?></p>
                <p>Matrix Earnings: R<?php echo $user_info['matrix_earnings']; ?></p>
                <?php if($user_info['loan_remaining'] > 0): ?>
                <p>Loan Remaining: R<?php echo $user_info['loan_remaining']; ?></p>
                <?php else: ?>
                <p>Loan Status: Fully Paid</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<div class="table-responsive">
<h3>Your 5×5 Matrix</h3>
<table class="table table-bordered" align="center" style="text-align:center">
<tr>
    <th colspan="5" class="text-center">Your Direct Recruits (Level 1)</th>
</tr>
<tr height="100">
<?php
// Get the 5 direct positions
$position1 = $data['position1'];
$position2 = $data['position2'];
$position3 = $data['position3'];
$position4 = $data['position4'];
$position5 = $data['position5'];

// Display each position
for($i = 1; $i <= 5; $i++) {
    $position_var = "position" . $i;
    $position_user = $data[$position_var];

    echo '<td width="20%">';
    if($position_user != "" && $position_user != NULL) {
        $pos_user_info = get_user_info($position_user);
        echo '<a href="tree.php?search-id=' . $position_user . '">';
        echo '<i class="fa fa-user fa-3x" style="color:#1430B1"></i>';
        echo '<p>' . $position_user . '</p>';
        echo '<small>Level: ' . $pos_user_info['matrix_level'] . '</small>';
        echo '</a>';
    } else {
        echo '<i class="fa fa-user fa-3x" style="color:#D3D3D3"></i>';
        echo '<p>Empty Position</p>';
    }
    echo '</td>';
}
?>
</tr>
</table>

<?php
// If this user has any direct recruits, show their downlines too
if($position1 != "" || $position2 != "" || $position3 != "" || $position4 != "" || $position5 != "") {
?>
<h4>Your Extended Network</h4>
<div class="row">
<?php
    // For each direct recruit that exists, show their direct recruits
    for($i = 1; $i <= 5; $i++) {
        $position_var = "position" . $i;
        $position_user = $data[$position_var];

        if($position_user != "" && $position_user != NULL) {
            $downline_data = tree_data($position_user);

            echo '<div class="col-md-6 mb-4">';
            echo '<div class="panel panel-default">';
            echo '<div class="panel-heading">' . $position_user . '\'s Recruits</div>';
            echo '<div class="panel-body">';
            echo '<table class="table table-bordered" style="text-align:center">';
            echo '<tr>';

            // Show this user's direct recruits
            for($j = 1; $j <= 5; $j++) {
                $downline_position_var = "position" . $j;
                $downline_user = $downline_data[$downline_position_var];

                echo '<td width="20%">';
                if($downline_user != "" && $downline_user != NULL) {
                    echo '<a href="tree.php?search-id=' . $downline_user . '">';
                    echo '<i class="fa fa-user fa-2x" style="color:#D520BE"></i>';
                    echo '<p>' . $downline_user . '</p>';
                    echo '</a>';
                } else {
                    echo '<i class="fa fa-user fa-2x" style="color:#D3D3D3"></i>';
                    echo '<p>Empty</p>';
                }
                echo '</td>';
            }

            echo '</tr>';
            echo '</table>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
        }
    }
?>
</div>
<?php
}
?>
</table>
</div>
</div>
</div>
</div>
<!-- /.container-fluid -->
</div>
<!-- /#page-wrapper -->
</div>
<!-- /#wrapper -->
<!-- jQuery -->
<script src="vendor/jquery/jquery.min.js"></script>
<!-- Bootstrap Core JavaScript -->
<script src="vendor/bootstrap/js/bootstrap.min.js"></script>
<!-- Metis Menu Plugin JavaScript -->
<script src="vendor/metisMenu/metisMenu.min.js"></script>
<!-- Custom Theme JavaScript -->
<script src="dist/js/sb-admin-2.js"></script>
</body>
</html>