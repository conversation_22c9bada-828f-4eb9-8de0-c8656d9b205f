<?php
/**
 * Stitch Configuration Setup
 * This page allows administrators to configure Stitch Payouts settings
 */

include('php-includes/connect.php');
include('php-includes/check-login.php');

// Check if user is admin (you may need to implement admin check)
$userid = $_SESSION['userid'];

// Handle form submission
if(isset($_POST['save_config'])) {
    $client_id = mysqli_real_escape_string($con, $_POST['client_id']);
    $client_secret = mysqli_real_escape_string($con, $_POST['client_secret']);
    $sandbox_mode = isset($_POST['sandbox_mode']) ? 1 : 0;
    
    if($client_id != '' && $client_secret != '') {
        // Check if config exists
        $check_query = mysqli_query($con, "SELECT * FROM stitch_config WHERE id = 1");
        
        if(mysqli_num_rows($check_query) > 0) {
            // Update existing config
            $update_query = "UPDATE stitch_config SET 
                           client_id='$client_id',
                           client_secret='$client_secret',
                           sandbox_mode='$sandbox_mode',
                           updated_date=NOW()
                           WHERE id=1";
            
            if(mysqli_query($con, $update_query)) {
                $success_message = "Stitch configuration updated successfully!";
            } else {
                $error_message = "Failed to update configuration. Please try again.";
            }
        } else {
            // Insert new config
            $insert_query = "INSERT INTO stitch_config (client_id, client_secret, sandbox_mode) 
                           VALUES ('$client_id', '$client_secret', '$sandbox_mode')";
            
            if(mysqli_query($con, $insert_query)) {
                $success_message = "Stitch configuration saved successfully!";
            } else {
                $error_message = "Failed to save configuration. Please try again.";
            }
        }
    } else {
        $error_message = "Please fill in all required fields.";
    }
}

// Get current configuration
$config_query = mysqli_query($con, "SELECT * FROM stitch_config WHERE id = 1");
$current_config = null;
if(mysqli_num_rows($config_query) > 0) {
    $current_config = mysqli_fetch_assoc($config_query);
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>MLM Website - Stitch Configuration</title>

    <!-- Bootstrap Core CSS -->
    <link href="vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">

    <!-- MetisMenu CSS -->
    <link href="vendor/metisMenu/metisMenu.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="dist/css/sb-admin-2.css" rel="stylesheet">

    <!-- Custom Fonts -->
    <link href="vendor/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css">
</head>

<body>
    <div id="wrapper">
        <!-- Navigation -->
        <?php include('php-includes/menu.php'); ?>

        <!-- Page Content -->
        <div id="page-wrapper">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-lg-12">
                        <h1 class="page-header">Stitch Payouts Configuration</h1>
                    </div>
                </div>

                <?php if(isset($success_message)): ?>
                <div class="alert alert-success">
                    <i class="fa fa-check-circle"></i> <?php echo $success_message; ?>
                </div>
                <?php endif; ?>

                <?php if(isset($error_message)): ?>
                <div class="alert alert-danger">
                    <i class="fa fa-exclamation-circle"></i> <?php echo $error_message; ?>
                </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-lg-8">
                        <div class="panel panel-primary">
                            <div class="panel-heading">
                                <h4><i class="fa fa-cog"></i> Stitch API Configuration</h4>
                            </div>
                            <div class="panel-body">
                                <form method="post">
                                    <div class="form-group">
                                        <label>Client ID</label>
                                        <input type="text" name="client_id" class="form-control" 
                                               value="<?php echo $current_config['client_id'] ?? ''; ?>" required>
                                        <p class="help-block">Your Stitch client ID from the developer dashboard</p>
                                    </div>

                                    <div class="form-group">
                                        <label>Client Secret</label>
                                        <input type="password" name="client_secret" class="form-control" 
                                               value="<?php echo $current_config['client_secret'] ?? ''; ?>" required>
                                        <p class="help-block">Your Stitch client secret (keep this secure)</p>
                                    </div>

                                    <div class="form-group">
                                        <div class="checkbox">
                                            <label>
                                                <input type="checkbox" name="sandbox_mode" 
                                                       <?php echo ($current_config && $current_config['sandbox_mode']) ? 'checked' : ''; ?>>
                                                Enable Sandbox Mode
                                            </label>
                                        </div>
                                        <p class="help-block">Use sandbox environment for testing (uncheck for production)</p>
                                    </div>

                                    <div class="form-group">
                                        <button type="submit" name="save_config" class="btn btn-primary btn-lg">
                                            <i class="fa fa-save"></i> Save Configuration
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h4><i class="fa fa-info-circle"></i> Setup Instructions</h4>
                            </div>
                            <div class="panel-body">
                                <h5>1. Create Stitch Account</h5>
                                <p>Sign up for a Stitch developer account at <a href="https://stitch.money" target="_blank">stitch.money</a></p>

                                <h5>2. Get API Credentials</h5>
                                <p>Create a new application in your Stitch dashboard and copy the Client ID and Client Secret.</p>

                                <h5>3. Configure Webhooks</h5>
                                <p>Set up webhooks in your Stitch dashboard to receive payout status updates.</p>

                                <h5>4. Test in Sandbox</h5>
                                <p>Always test your integration in sandbox mode before going live.</p>

                                <div class="alert alert-warning">
                                    <i class="fa fa-shield"></i> <strong>Security:</strong> Keep your client secret secure and never share it publicly.
                                </div>
                            </div>
                        </div>

                        <?php if($current_config): ?>
                        <div class="panel panel-success">
                            <div class="panel-heading">
                                <h4><i class="fa fa-check"></i> Current Configuration</h4>
                            </div>
                            <div class="panel-body">
                                <table class="table table-bordered">
                                    <tr>
                                        <th>Client ID:</th>
                                        <td><?php echo substr($current_config['client_id'], 0, 8) . '...'; ?></td>
                                    </tr>
                                    <tr>
                                        <th>Environment:</th>
                                        <td>
                                            <span class="label label-<?php echo $current_config['sandbox_mode'] ? 'warning' : 'success'; ?>">
                                                <?php echo $current_config['sandbox_mode'] ? 'Sandbox' : 'Production'; ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Last Updated:</th>
                                        <td><?php echo date('M j, Y H:i', strtotime($current_config['updated_date'])); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-12">
                        <div class="panel panel-info">
                            <div class="panel-heading">
                                <h4><i class="fa fa-cogs"></i> System Integration Status</h4>
                            </div>
                            <div class="panel-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <i class="fa fa-database fa-3x text-success"></i>
                                            <h5>Database Tables</h5>
                                            <p class="text-success">Ready</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <i class="fa fa-code fa-3x <?php echo $current_config ? 'text-success' : 'text-warning'; ?>"></i>
                                            <h5>API Configuration</h5>
                                            <p class="<?php echo $current_config ? 'text-success' : 'text-warning'; ?>">
                                                <?php echo $current_config ? 'Configured' : 'Pending'; ?>
                                            </p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <i class="fa fa-users fa-3x text-info"></i>
                                            <h5>User Accounts</h5>
                                            <p class="text-info">Ready for Setup</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <i class="fa fa-money fa-3x text-info"></i>
                                            <h5>Automated Payouts</h5>
                                            <p class="text-info">Ready to Process</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="vendor/jquery/jquery.min.js"></script>

    <!-- Bootstrap Core JavaScript -->
    <script src="vendor/bootstrap/js/bootstrap.min.js"></script>

    <!-- Metis Menu Plugin JavaScript -->
    <script src="vendor/metisMenu/metisMenu.min.js"></script>

    <!-- Custom Theme JavaScript -->
    <script src="dist/js/sb-admin-2.js"></script>
</body>
</html>
