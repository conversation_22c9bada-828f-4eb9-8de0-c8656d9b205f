<?php
/**
 * Password Security Utility Class
 * Provides secure password hashing, verification, and validation
 */
class PasswordSecurity {
    
    // Password requirements
    const MIN_LENGTH = 8;
    const MAX_LENGTH = 128;
    const REQUIRE_UPPERCASE = true;
    const REQUIRE_LOWERCASE = true;
    const REQUIRE_NUMBERS = true;
    const REQUIRE_SPECIAL_CHARS = true;
    
    /**
     * Hash a password securely using P<PERSON>'s password_hash()
     * 
     * @param string $password The plain text password
     * @return string The hashed password
     */
    public static function hashPassword($password) {
        return password_hash($password, PASSWORD_DEFAULT);
    }
    
    /**
     * Verify a password against its hash
     * 
     * @param string $password The plain text password
     * @param string $hash The stored password hash
     * @return bool True if password matches, false otherwise
     */
    public static function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    /**
     * Check if a password needs to be rehashed (for security updates)
     * 
     * @param string $hash The stored password hash
     * @return bool True if rehashing is needed
     */
    public static function needsRehash($hash) {
        return password_needs_rehash($hash, PASSWORD_DEFAULT);
    }
    
    /**
     * Validate password strength according to security requirements
     * 
     * @param string $password The password to validate
     * @return array Array with 'valid' boolean and 'errors' array
     */
    public static function validatePasswordStrength($password) {
        $errors = [];
        
        // Check length
        if (strlen($password) < self::MIN_LENGTH) {
            $errors[] = "Password must be at least " . self::MIN_LENGTH . " characters long";
        }
        
        if (strlen($password) > self::MAX_LENGTH) {
            $errors[] = "Password must not exceed " . self::MAX_LENGTH . " characters";
        }
        
        // Check for uppercase letters
        if (self::REQUIRE_UPPERCASE && !preg_match('/[A-Z]/', $password)) {
            $errors[] = "Password must contain at least one uppercase letter";
        }
        
        // Check for lowercase letters
        if (self::REQUIRE_LOWERCASE && !preg_match('/[a-z]/', $password)) {
            $errors[] = "Password must contain at least one lowercase letter";
        }
        
        // Check for numbers
        if (self::REQUIRE_NUMBERS && !preg_match('/[0-9]/', $password)) {
            $errors[] = "Password must contain at least one number";
        }
        
        // Check for special characters
        if (self::REQUIRE_SPECIAL_CHARS && !preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = "Password must contain at least one special character";
        }
        
        // Check for common weak passwords
        $weakPasswords = [
            'password', '123456', '123456789', 'qwerty', 'abc123', 
            'password123', 'admin', 'letmein', 'welcome', 'monkey'
        ];
        
        if (in_array(strtolower($password), $weakPasswords)) {
            $errors[] = "Password is too common and easily guessable";
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
    
    /**
     * Generate a secure random password
     * 
     * @param int $length Password length (default: 12)
     * @return string Generated password
     */
    public static function generateSecurePassword($length = 12) {
        $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $lowercase = 'abcdefghijklmnopqrstuvwxyz';
        $numbers = '0123456789';
        $special = '!@#$%^&*()_+-=[]{}|;:,.<>?';
        
        $all = $uppercase . $lowercase . $numbers . $special;
        
        $password = '';
        
        // Ensure at least one character from each required set
        if (self::REQUIRE_UPPERCASE) {
            $password .= $uppercase[random_int(0, strlen($uppercase) - 1)];
        }
        if (self::REQUIRE_LOWERCASE) {
            $password .= $lowercase[random_int(0, strlen($lowercase) - 1)];
        }
        if (self::REQUIRE_NUMBERS) {
            $password .= $numbers[random_int(0, strlen($numbers) - 1)];
        }
        if (self::REQUIRE_SPECIAL_CHARS) {
            $password .= $special[random_int(0, strlen($special) - 1)];
        }
        
        // Fill the rest randomly
        for ($i = strlen($password); $i < $length; $i++) {
            $password .= $all[random_int(0, strlen($all) - 1)];
        }
        
        // Shuffle the password to randomize character positions
        return str_shuffle($password);
    }
    
    /**
     * Generate password strength score (0-100)
     * 
     * @param string $password The password to score
     * @return int Score from 0 (weakest) to 100 (strongest)
     */
    public static function getPasswordStrengthScore($password) {
        $score = 0;
        $length = strlen($password);
        
        // Length scoring
        if ($length >= 8) $score += 25;
        if ($length >= 12) $score += 25;
        if ($length >= 16) $score += 10;
        
        // Character variety scoring
        if (preg_match('/[a-z]/', $password)) $score += 10;
        if (preg_match('/[A-Z]/', $password)) $score += 10;
        if (preg_match('/[0-9]/', $password)) $score += 10;
        if (preg_match('/[^A-Za-z0-9]/', $password)) $score += 10;
        
        return min(100, $score);
    }
}
?>
