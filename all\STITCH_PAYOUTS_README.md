# Stitch Payouts Integration for MLM System

This implementation integrates Stitch Payouts for automated, high-volume commission payouts to MLM network members, while using Wise for incoming sponsor payments.

## Architecture Overview

### Payment Flow
1. **Incoming Payments**: Sponsors use Wise to fund the loan pool
2. **Commission Calculation**: Automated commission calculation based on 5×5 matrix
3. **Payout Processing**: Stitch Payouts handles automated payouts to members
4. **Status Tracking**: Real-time tracking of payout status and history

### Key Components

#### 1. Stitch Payouts Integration (`php-includes/stitch-payouts.php`)
- GraphQL API integration with Stitch
- Batch payout processing for efficiency
- Recipient management
- Status tracking and updates

#### 2. Commission Processor (`php-includes/commission-processor.php`)
- Automated commission calculation
- Queue management for pending payouts
- Upline chain processing
- Minimum payout thresholds

#### 3. Database Schema (`database/stitch_tables.sql`)
- Configuration storage
- Recipient management
- Batch and payout tracking
- Commission queue system

## Setup Instructions

### 1. Database Setup
```sql
-- Run the SQL file to create necessary tables
mysql -u username -p database_name < database/stitch_tables.sql
```

### 2. Stitch Account Setup
1. Create account at [stitch.money](https://stitch.money)
2. Create a new application in the developer dashboard
3. Enable "Payouts" scope for your application
4. Copy Client ID and Client Secret

### 3. Configuration
1. Visit `setup-stitch-config.php` in your browser
2. Enter your Stitch API credentials
3. Enable sandbox mode for testing
4. Save configuration

### 4. User Account Setup
Users need to setup their payout accounts:
1. Visit `setup-payout-account.php`
2. Enter bank account details
3. Select their bank from the list
4. Account is validated and stored securely

## Commission Structure

### 5×5 Matrix Levels
- **Level 1**: R500 per person (5 people = R2,500 total)
- **Level 2**: R200 per person (25 people = R5,000 total)
- **Level 3**: R100 per person (125 people = R12,500 total)
- **Level 4**: R50 per person (625 people = R31,250 total)
- **Level 5**: R25 per person (3,125 people = R78,125 total)

### Payout Processing
- **Minimum Payout**: R100.00
- **Processing**: Automatic when threshold is reached
- **Frequency**: Can be run hourly, daily, or on-demand
- **Batch Size**: Configurable (default: 50 users per batch)

## Automated Processing

### Cron Job Setup
Add to your crontab for automated processing:

```bash
# Process payouts every hour
0 * * * * /usr/bin/php /path/to/your/site/process-payouts.php

# Or process daily at 2 AM
0 2 * * * /usr/bin/php /path/to/your/site/process-payouts.php
```

### Manual Processing
Visit `process-payouts.php?manual_run=1` to run processing manually.

## Security Features

### Data Protection
- No sensitive banking data stored locally
- All banking details processed through Stitch
- Encrypted API communications
- Secure token-based authentication

### Access Control
- User-specific payout accounts
- Admin-only configuration access
- Audit trails for all transactions
- Status verification and updates

## Monitoring and Logging

### Dashboard Features (`payout-dashboard.php`)
- Real-time commission statistics
- Pending and paid amounts
- Payout history
- Account status monitoring

### Logging System
- Comprehensive payout processing logs
- Error tracking and notifications
- Status update history
- Performance monitoring

## API Integration Details

### Stitch GraphQL Endpoints
- **Authentication**: Client credentials flow
- **Recipient Creation**: Bank account validation
- **Batch Processing**: Efficient bulk payouts
- **Status Tracking**: Real-time updates

### Error Handling
- Automatic retry mechanisms
- Fallback processing
- Detailed error logging
- Admin notifications

## Testing

### Sandbox Environment
1. Enable sandbox mode in configuration
2. Use test bank accounts provided by Stitch
3. Process test payouts
4. Verify status updates

### Production Deployment
1. Disable sandbox mode
2. Update to production API credentials
3. Test with small amounts first
4. Monitor initial batches closely

## Troubleshooting

### Common Issues
1. **Authentication Failures**: Check API credentials
2. **Invalid Recipients**: Verify bank account details
3. **Batch Failures**: Check minimum amounts and recipient status
4. **Status Updates**: Ensure webhook configuration

### Support Resources
- Stitch API Documentation: [docs.stitch.money](https://docs.stitch.money)
- Developer Support: Available through Stitch dashboard
- System Logs: Check `logs/payout_processing.log`

## Performance Optimization

### Batch Processing
- Configurable batch sizes
- Efficient database queries
- Minimal API calls
- Parallel processing support

### Scalability
- Queue-based processing
- Horizontal scaling support
- Database optimization
- Caching strategies

## Compliance and Regulations

### South African Banking
- Compliant with SARB regulations
- Secure payment processing
- Audit trail maintenance
- KYC/AML compliance through Stitch

### Data Privacy
- POPIA compliance
- Minimal data retention
- Secure data transmission
- User consent management

## Cost Optimization

### Stitch Pricing
- Per-transaction fees
- Volume discounts available
- Transparent pricing model
- No hidden fees

### Efficiency Features
- Batch processing reduces costs
- Minimum thresholds prevent micro-transactions
- Automated processing reduces manual overhead
- Real-time status updates minimize support queries

## Future Enhancements

### Planned Features
- Multi-currency support
- Advanced reporting
- Mobile app integration
- Enhanced fraud detection

### Integration Possibilities
- Accounting system integration
- Tax reporting automation
- Advanced analytics
- Machine learning optimization

## Support and Maintenance

### Regular Tasks
- Monitor payout processing logs
- Update API credentials as needed
- Review and optimize batch sizes
- Monitor system performance

### Emergency Procedures
- Manual payout processing
- Status verification procedures
- Error recovery protocols
- User communication templates
