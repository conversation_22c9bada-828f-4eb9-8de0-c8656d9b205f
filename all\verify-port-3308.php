<?php
/**
 * Verify Port 3308 Configuration
 * This script confirms that all files are configured for port 3308
 */

echo "<h2>Port 3308 Configuration Verification</h2>";

// Check configuration files
$config_files = [
    'php-includes/connect.php' => 'Main application database connection',
    'admin/php-includes/connect.php' => 'Admin panel database connection'
];

echo "<h3>Configuration Files Check:</h3>";

$all_configured = true;

foreach ($config_files as $file => $description) {
    echo "<div style='margin: 10px; padding: 10px; border: 1px solid #ccc; border-radius: 5px;'>";
    echo "<strong>$description</strong><br>";
    echo "<code>$file</code><br>";
    
    if (file_exists($file)) {
        $content = file_get_contents($file);
        
        if (strpos($content, '$db_port = "3308"') !== false) {
            echo "<span style='color: green;'>✅ Correctly configured for port 3308</span>";
        } elseif (strpos($content, '$db_port = "3306"') !== false) {
            echo "<span style='color: red;'>❌ Still configured for port 3306</span>";
            $all_configured = false;
        } else {
            echo "<span style='color: orange;'>⚠️ Port configuration not found</span>";
            $all_configured = false;
        }
    } else {
        echo "<span style='color: red;'>❌ File not found</span>";
        $all_configured = false;
    }
    echo "</div>";
}

echo "<h3>Connection Test to Port 3308:</h3>";

$connection = @mysqli_connect('localhost', 'root', '', '', 3308);

if ($connection) {
    echo "<div style='background: #d4edda; border: 1px solid #28a745; padding: 15px; border-radius: 5px;'>";
    echo "<h4 style='color: #155724;'>✅ MySQL Connection Successful on Port 3308!</h4>";
    echo "<p>MySQL Version: " . mysqli_get_server_info($connection) . "</p>";
    
    // Test MLM database
    $mlm_test = @mysqli_connect('localhost', 'root', '', 'mlm', 3308);
    if ($mlm_test) {
        echo "<p style='color: green;'>✅ MLM database is accessible</p>";
        mysqli_close($mlm_test);
    } else {
        echo "<p style='color: orange;'>⚠️ MLM database needs to be created</p>";
        
        // Try to create MLM database
        if (mysqli_query($connection, "CREATE DATABASE IF NOT EXISTS mlm")) {
            echo "<p style='color: green;'>✅ MLM database created successfully</p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to create MLM database</p>";
        }
    }
    
    mysqli_close($connection);
    echo "</div>";
} else {
    echo "<div style='background: #ffebee; border: 1px solid #f44336; padding: 15px; border-radius: 5px;'>";
    echo "<h4 style='color: #d32f2f;'>❌ Cannot Connect to MySQL on Port 3308</h4>";
    echo "<p>Please ensure:</p>";
    echo "<ol>";
    echo "<li>XAMPP is running</li>";
    echo "<li>MySQL is started in XAMPP Control Panel</li>";
    echo "<li>MySQL is configured to use port 3308</li>";
    echo "</ol>";
    echo "</div>";
}

echo "<h3>XAMPP MySQL Port Configuration:</h3>";
echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px;'>";
echo "<p>To configure XAMPP MySQL to use port 3308:</p>";
echo "<ol>";
echo "<li>Open <strong>XAMPP Control Panel</strong></li>";
echo "<li>Click <strong>'Config'</strong> next to MySQL</li>";
echo "<li>Select <strong>'my.ini'</strong></li>";
echo "<li>Find the line: <code>port = 3306</code></li>";
echo "<li>Change it to: <code>port = 3308</code></li>";
echo "<li>Save the file</li>";
echo "<li>Restart MySQL in XAMPP</li>";
echo "</ol>";
echo "</div>";

if ($all_configured && $connection) {
    echo "<div style='background: #d1ecf1; border: 1px solid #17a2b8; padding: 15px; border-radius: 5px; margin-top: 20px;'>";
    echo "<h4 style='color: #0c5460;'>🎉 Configuration Complete!</h4>";
    echo "<p>All files are configured for port 3308 and MySQL is accessible.</p>";
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ul>";
    echo "<li><a href='setup-database.php'>Setup Database Tables</a></li>";
    echo "<li><a href='index.php'>Access Main Application</a></li>";
    echo "<li><a href='admin/login.php'>Access Admin Panel</a></li>";
    echo "</ul>";
    echo "</div>";
}

echo "<h3>Port Status Check:</h3>";
echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px;'>";
echo "<p>You can verify which ports are in use with this command in Command Prompt:</p>";
echo "<code>netstat -ano | findstr :330</code>";
echo "<p>This will show both port 3306 (other server) and 3308 (XAMPP) if they're running.</p>";
echo "</div>";
?>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    background: #f8f9fa;
}
h2, h3, h4 { 
    color: #333; 
    border-bottom: 2px solid #007bff;
    padding-bottom: 5px;
}
code {
    background: #f0f0f0;
    padding: 2px 5px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}
a {
    color: #007bff;
    text-decoration: none;
    font-weight: bold;
}
a:hover {
    text-decoration: underline;
}
</style>
