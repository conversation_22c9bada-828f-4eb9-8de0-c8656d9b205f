<?php
/**
 * WhatsApp Helper Functions
 *
 * This file contains functions for sending WhatsApp messages using Twilio's WhatsApp API
 * Note: This requires a Twilio account with WhatsApp capabilities enabled
 */

// Twilio configuration - store these in a separate config file in production
$WHATSAPP_CONFIG = [
    'account_sid' => 'YOUR_TWILIO_ACCOUNT_SID',
    'auth_token' => 'YOUR_TWILIO_AUTH_TOKEN',
    'whatsapp_number' => 'whatsapp:+***********', // Your Twilio WhatsApp number with "whatsapp:" prefix
    'sandbox_mode' => true // Set to false in production
];

/**
 * Send a WhatsApp message using Twilio API
 *
 * @param string $to Recipient phone number (format: +**********)
 * @param string $message Message to send
 * @return bool True if message sent successfully, false otherwise
 */
function sendWhatsAppMessage($to, $message) {
    global $WHATSAPP_CONFIG;

    // Format the recipient number for WhatsApp
    $to = 'whatsapp:' . $to;

    // In a real implementation, we would use the Twilio API
    // For now, we'll just log the message for testing purposes
    $log_message = date('Y-m-d H:i:s') . " - WhatsApp To: $to, Message: $message\n";
    file_put_contents('whatsapp_log.txt', $log_message, FILE_APPEND);

    // For testing purposes, always return true
    return true;

    /* Real implementation would look like this:
    // Prepare the request data
    $data = [
        'From' => $WHATSAPP_CONFIG['whatsapp_number'],
        'To' => $to,
        'Body' => $message
    ];

    // Set up cURL request to Twilio API
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "https://api.twilio.com/2010-04-01/Accounts/{$WHATSAPP_CONFIG['account_sid']}/Messages.json");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_USERPWD, "{$WHATSAPP_CONFIG['account_sid']}:{$WHATSAPP_CONFIG['auth_token']}");

    // Execute the request
    $response = curl_exec($ch);
    $http_status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    // Check if the request was successful
    if ($http_status >= 200 && $http_status < 300) {
        return true;
    } else {
        // Log the error
        error_log("WhatsApp Error: " . $response);
        return false;
    }
    */
}

/**
 * Send OTP via WhatsApp
 *
 * @param string $to Recipient phone number (format: +**********)
 * @param string $otp One-time password
 * @return bool True if message sent successfully, false otherwise
 */
function sendOTPviaWhatsApp($to, $otp) {
    global $WHATSAPP_CONFIG;

    // If in sandbox mode, check if the user has opted in
    if ($WHATSAPP_CONFIG['sandbox_mode']) {
        // In sandbox mode, the user must have sent a specific message to your WhatsApp number
        // You might want to add a check here or inform the user about this requirement
    }

    $message = "Your MLM registration OTP is: $otp. Please share this with your sponsor to complete your registration. This code will expire in 15 minutes.";

    return sendWhatsAppMessage($to, $message);
}

/**
 * Fallback function that tries WhatsApp first, then SMS if WhatsApp fails
 *
 * @param string $mobile Recipient phone number (format: +**********)
 * @param string $otp One-time password
 * @return bool True if message sent successfully via either method, false otherwise
 */
function sendOTPWithFallback($mobile, $otp) {
    // Try WhatsApp first
    $whatsapp_success = sendOTPviaWhatsApp($mobile, $otp);

    if ($whatsapp_success) {
        return true;
    }

    // If WhatsApp fails, log it and try SMS as fallback
    error_log("WhatsApp delivery failed for $mobile, falling back to SMS");

    // Implement SMS fallback here if needed
    // For now, we'll just simulate SMS sending
    $sms_message = "Your MLM registration OTP is: $otp";

    // Log the SMS for testing purposes
    file_put_contents('sms_log.txt', date('Y-m-d H:i:s') . " - To: $mobile, Message: $sms_message\n", FILE_APPEND);

    return true; // Assuming SMS was sent successfully
}
?>
