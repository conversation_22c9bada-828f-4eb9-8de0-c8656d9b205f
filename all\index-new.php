<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="Ubuntu Wealth Network - Building Financial Freedom Together in South Africa">
    <meta name="author" content="Ubuntu Wealth Network">
    <meta name="keywords" content="MLM, South Africa, financial freedom, Ubuntu, community, income opportunity">
    
    <title>Ubuntu Wealth Network - Building Financial Freedom Together</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Ubuntu:wght@300;400;500;700&family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --sa-green: #007749;
            --sa-gold: #FFB612;
            --sa-blue: #002395;
            --sa-red: #DE3831;
            --ubuntu-orange: #E95420;
            --warm-white: #FFF8F0;
            --text-dark: #2C3E50;
            --text-light: #6C757D;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Open Sans', sans-serif;
            line-height: 1.6;
            color: var(--text-dark);
            background: linear-gradient(135deg, var(--warm-white) 0%, #ffffff 100%);
        }
        
        .ubuntu-font {
            font-family: 'Ubuntu', sans-serif;
        }
        
        /* Header Styles */
        .navbar {
            background: linear-gradient(90deg, var(--sa-green) 0%, var(--sa-blue) 100%);
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-family: 'Ubuntu', sans-serif;
            font-weight: 700;
            font-size: 1.5rem;
            color: white !important;
        }
        
        .navbar-nav .nav-link {
            color: white !important;
            font-weight: 500;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }
        
        .navbar-nav .nav-link:hover {
            color: var(--sa-gold) !important;
            transform: translateY(-2px);
        }
        
        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, var(--sa-green) 0%, var(--sa-blue) 100%);
            color: white;
            padding: 4rem 0;
            position: relative;
            overflow: hidden;
        }
        
        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        
        .hero-content {
            position: relative;
            z-index: 2;
        }
        
        .hero-title {
            font-family: 'Ubuntu', sans-serif;
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .hero-subtitle {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0.95;
        }
        
        .cta-button {
            background: linear-gradient(45deg, var(--sa-gold) 0%, var(--ubuntu-orange) 100%);
            border: none;
            padding: 1rem 2.5rem;
            font-size: 1.2rem;
            font-weight: 600;
            border-radius: 50px;
            color: white;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
            color: white;
        }
        
        /* Features Section */
        .features-section {
            padding: 4rem 0;
            background: var(--warm-white);
        }
        
        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            height: 100%;
            border: 2px solid transparent;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            border-color: var(--sa-gold);
        }
        
        .feature-icon {
            font-size: 3rem;
            color: var(--sa-green);
            margin-bottom: 1.5rem;
        }
        
        .feature-title {
            font-family: 'Ubuntu', sans-serif;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--text-dark);
        }
        
        /* Success Stories */
        .success-section {
            padding: 4rem 0;
            background: linear-gradient(135deg, #f8f9fa 0%, white 100%);
        }
        
        .testimonial-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            border-left: 5px solid var(--sa-gold);
        }
        
        .testimonial-text {
            font-style: italic;
            font-size: 1.1rem;
            margin-bottom: 1.5rem;
            color: var(--text-dark);
        }
        
        .testimonial-author {
            font-weight: 600;
            color: var(--sa-green);
        }
        
        /* Login Section */
        .login-section {
            padding: 3rem 0;
            background: white;
        }
        
        .login-card {
            background: white;
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }
        
        .login-title {
            font-family: 'Ubuntu', sans-serif;
            font-size: 2rem;
            font-weight: 600;
            text-align: center;
            margin-bottom: 2rem;
            color: var(--sa-green);
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.8rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: var(--sa-green);
            box-shadow: 0 0 0 0.2rem rgba(0, 119, 73, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(45deg, var(--sa-green) 0%, var(--sa-blue) 100%);
            border: none;
            padding: 0.8rem 2rem;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 10px;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        /* Footer */
        .footer {
            background: var(--text-dark);
            color: white;
            padding: 2rem 0;
            text-align: center;
        }
        
        /* Mobile Responsive */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .hero-subtitle {
                font-size: 1.1rem;
            }
            
            .cta-button {
                padding: 0.8rem 2rem;
                font-size: 1rem;
            }
            
            .login-card {
                padding: 2rem;
                margin: 1rem;
            }
        }
        
        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .animate-fade-in {
            animation: fadeInUp 0.8s ease-out;
        }
        
        /* South African Flag Colors Accent */
        .sa-accent {
            background: linear-gradient(90deg, 
                var(--sa-green) 0%, 
                var(--sa-gold) 25%, 
                var(--sa-blue) 50%, 
                var(--sa-red) 75%, 
                var(--sa-green) 100%);
            height: 4px;
            width: 100%;
        }
    </style>
</head>

<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand ubuntu-font" href="#home">
                <i class="fas fa-hands-helping me-2"></i>
                Ubuntu Wealth Network
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#home">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#opportunity">Opportunity</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#success">Success Stories</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#login">Login</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">Contact</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- SA Flag Accent -->
    <div class="sa-accent"></div>

    <!-- Hero Section -->
    <section id="home" class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6 hero-content animate-fade-in">
                    <h1 class="hero-title">Build Your Financial Future with Ubuntu</h1>
                    <p class="hero-subtitle">
                        Join thousands of South Africans who are creating sustainable income through our community-driven 5×5 matrix system. 
                        Together, we rise. Together, we prosper.
                    </p>
                    <a href="#opportunity" class="cta-button">
                        <i class="fas fa-rocket me-2"></i>
                        Start Your Journey
                    </a>
                </div>
                <div class="col-lg-6 text-center">
                    <div class="hero-image">
                        <i class="fas fa-users" style="font-size: 15rem; opacity: 0.3;"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Opportunity Section -->
    <section id="opportunity" class="features-section">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="ubuntu-font" style="font-size: 2.5rem; color: var(--sa-green); margin-bottom: 1rem;">
                        Your Path to Financial Freedom
                    </h2>
                    <p class="lead" style="color: var(--text-light); max-width: 600px; margin: 0 auto;">
                        Our proven 5×5 matrix system creates multiple income streams while building a supportive community
                    </p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-md-6 col-lg-3">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-seedling"></i>
                        </div>
                        <h3 class="feature-title">Start Small, Grow Big</h3>
                        <p>Begin with just R2,000 and watch your investment grow through our structured matrix system.</p>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="feature-title">Community Support</h3>
                        <p>Join a network of like-minded South Africans all working together towards financial independence.</p>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3 class="feature-title">Multiple Income Levels</h3>
                        <p>Earn from R1,000 to R625,000 as you progress through our 5-level commission structure.</p>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h3 class="feature-title">Mobile Friendly</h3>
                        <p>Manage your business from anywhere using your smartphone. No expensive equipment needed.</p>
                    </div>
                </div>
            </div>

            <!-- Income Breakdown -->
            <div class="row mt-5">
                <div class="col-12">
                    <div class="card" style="border-radius: 20px; border: none; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
                        <div class="card-body p-4">
                            <h3 class="ubuntu-font text-center mb-4" style="color: var(--sa-green);">
                                <i class="fas fa-coins me-2"></i>
                                Commission Structure
                            </h3>
                            <div class="row text-center">
                                <div class="col-6 col-md-2 mb-3">
                                    <div class="p-3" style="background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%); border-radius: 15px;">
                                        <h4 style="color: var(--sa-green); margin-bottom: 0.5rem;">Level 1</h4>
                                        <p style="font-size: 1.2rem; font-weight: 600; color: var(--text-dark); margin: 0;">R1,000</p>
                                    </div>
                                </div>
                                <div class="col-6 col-md-2 mb-3">
                                    <div class="p-3" style="background: linear-gradient(135deg, #fff3cd 0%, #fef8e1 100%); border-radius: 15px;">
                                        <h4 style="color: var(--sa-gold); margin-bottom: 0.5rem;">Level 2</h4>
                                        <p style="font-size: 1.2rem; font-weight: 600; color: var(--text-dark); margin: 0;">R5,000</p>
                                    </div>
                                </div>
                                <div class="col-6 col-md-2 mb-3">
                                    <div class="p-3" style="background: linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 100%); border-radius: 15px;">
                                        <h4 style="color: var(--sa-blue); margin-bottom: 0.5rem;">Level 3</h4>
                                        <p style="font-size: 1.2rem; font-weight: 600; color: var(--text-dark); margin: 0;">R25,000</p>
                                    </div>
                                </div>
                                <div class="col-6 col-md-2 mb-3">
                                    <div class="p-3" style="background: linear-gradient(135deg, #fce4ec 0%, #fdf2f8 100%); border-radius: 15px;">
                                        <h4 style="color: var(--sa-red); margin-bottom: 0.5rem;">Level 4</h4>
                                        <p style="font-size: 1.2rem; font-weight: 600; color: var(--text-dark); margin: 0;">R125,000</p>
                                    </div>
                                </div>
                                <div class="col-6 col-md-2 mb-3">
                                    <div class="p-3" style="background: linear-gradient(135deg, #f3e5f5 0%, #faf5ff 100%); border-radius: 15px;">
                                        <h4 style="color: #9c27b0; margin-bottom: 0.5rem;">Level 5</h4>
                                        <p style="font-size: 1.2rem; font-weight: 600; color: var(--text-dark); margin: 0;">R625,000</p>
                                    </div>
                                </div>
                                <div class="col-12 col-md-2 mb-3">
                                    <div class="p-3" style="background: linear-gradient(135deg, var(--sa-green) 0%, var(--sa-blue) 100%); border-radius: 15px; color: white;">
                                        <h4 style="margin-bottom: 0.5rem;">Total Potential</h4>
                                        <p style="font-size: 1.2rem; font-weight: 600; margin: 0;">R781,000</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Success Stories Section -->
    <section id="success" class="success-section">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="ubuntu-font" style="font-size: 2.5rem; color: var(--sa-green); margin-bottom: 1rem;">
                        <i class="fas fa-star me-2"></i>
                        Success Stories from Our Community
                    </h2>
                    <p class="lead" style="color: var(--text-light);">
                        Real people, real results, real hope for a better future
                    </p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6 col-lg-4">
                    <div class="testimonial-card">
                        <p class="testimonial-text">
                            "After being unemployed for 8 months, Ubuntu Wealth Network gave me hope again.
                            In just 3 months, I've earned enough to support my family and even help my neighbors join too."
                        </p>
                        <div class="testimonial-author">
                            <strong>Nomsa M.</strong> - Johannesburg
                            <br><small style="color: var(--text-light);">Earned R15,000 in 3 months</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-4">
                    <div class="testimonial-card">
                        <p class="testimonial-text">
                            "As a pensioner, I thought my earning days were over. This platform proved me wrong!
                            The community support and simple system made it easy for someone my age."
                        </p>
                        <div class="testimonial-author">
                            <strong>Pieter V.</strong> - Cape Town
                            <br><small style="color: var(--text-light);">Age 67, Earned R8,500</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-4">
                    <div class="testimonial-card">
                        <p class="testimonial-text">
                            "I started with my last R2,000. Today, I'm helping 20 families in my township
                            build their own financial freedom. Ubuntu means we rise together!"
                        </p>
                        <div class="testimonial-author">
                            <strong>Thabo K.</strong> - Durban
                            <br><small style="color: var(--text-light);">Level 4 Achiever</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Login Section -->
    <section id="login" class="login-section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6">
                    <div class="login-card">
                        <h2 class="login-title">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            Member Login
                        </h2>
                        <form action="login-check.php" method="post" id="loginForm">
                            <div class="mb-3">
                                <label for="userid" class="form-label">
                                    <i class="fas fa-user me-2"></i>User ID / Email
                                </label>
                                <input type="text" class="form-control" id="userid" name="userid" required
                                       placeholder="Enter your User ID or Email">
                            </div>
                            <div class="mb-4">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-2"></i>Password
                                </label>
                                <input type="password" class="form-control" id="password" name="password" required
                                       placeholder="Enter your password">
                            </div>
                            <button type="submit" class="btn btn-login">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                Login to Dashboard
                            </button>
                        </form>

                        <div class="text-center mt-4">
                            <p class="mb-2">
                                <a href="forgot-password.php" style="color: var(--sa-green); text-decoration: none;">
                                    <i class="fas fa-key me-1"></i>Forgot Password?
                                </a>
                            </p>
                            <hr style="margin: 1.5rem 0;">
                            <p class="mb-3" style="color: var(--text-light);">New to Ubuntu Wealth Network?</p>
                            <a href="register.php" class="btn" style="background: linear-gradient(45deg, var(--sa-gold) 0%, var(--ubuntu-orange) 100%);
                               color: white; border: none; padding: 0.8rem 2rem; border-radius: 10px; text-decoration: none; font-weight: 600;">
                                <i class="fas fa-user-plus me-2"></i>
                                Join Our Community
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" style="padding: 3rem 0; background: var(--warm-white);">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-4">
                    <h2 class="ubuntu-font" style="color: var(--sa-green); margin-bottom: 1rem;">
                        <i class="fas fa-phone me-2"></i>
                        Get Support
                    </h2>
                </div>
            </div>
            <div class="row justify-content-center">
                <div class="col-md-4 text-center mb-3">
                    <div class="p-3">
                        <i class="fas fa-phone-alt" style="font-size: 2rem; color: var(--sa-green); margin-bottom: 1rem;"></i>
                        <h5>Call Us</h5>
                        <p style="color: var(--text-light);">+27 11 123 4567</p>
                    </div>
                </div>
                <div class="col-md-4 text-center mb-3">
                    <div class="p-3">
                        <i class="fas fa-envelope" style="font-size: 2rem; color: var(--sa-green); margin-bottom: 1rem;"></i>
                        <h5>Email Us</h5>
                        <p style="color: var(--text-light);"><EMAIL></p>
                    </div>
                </div>
                <div class="col-md-4 text-center mb-3">
                    <div class="p-3">
                        <i class="fab fa-whatsapp" style="font-size: 2rem; color: var(--sa-green); margin-bottom: 1rem;"></i>
                        <h5>WhatsApp</h5>
                        <p style="color: var(--text-light);">+27 82 123 4567</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6 text-center text-md-start">
                    <p>&copy; 2024 Ubuntu Wealth Network. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-center text-md-end">
                    <p>Building Financial Freedom Together |
                        <a href="privacy.php" style="color: var(--sa-gold);">Privacy Policy</a> |
                        <a href="terms.php" style="color: var(--sa-gold);">Terms of Service</a>
                    </p>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12 text-center">
                    <div class="sa-accent" style="margin: 1rem auto; width: 200px;"></div>
                    <p style="color: var(--sa-gold); font-style: italic;">
                        "Ubuntu: I am because we are"
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in');
                }
            });
        }, observerOptions);

        // Observe all feature cards and testimonials
        document.querySelectorAll('.feature-card, .testimonial-card').forEach(el => {
            observer.observe(el);
        });

        // Form validation
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            const userid = document.getElementById('userid').value.trim();
            const password = document.getElementById('password').value.trim();

            if (!userid || !password) {
                e.preventDefault();
                alert('Please fill in all fields');
                return false;
            }
        });

        // Add loading state to login button
        document.getElementById('loginForm').addEventListener('submit', function() {
            const button = this.querySelector('button[type="submit"]');
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Logging in...';
            button.disabled = true;
        });
    </script>
</body>
</html>
