<?php
include('php-includes/connect.php');
include('php-includes/check-login.php');
include('php-includes/commission-processor.php');

$userid = $_SESSION['userid'];
$processor = new CommissionProcessor($con);

// Get user's commission statistics
$userStats = $processor->getUserCommissionStats($userid);

// Get user's recent commissions
$recentCommissions = mysqli_query($con, "
    SELECT cq.*, u.email as triggered_by_email 
    FROM commission_queue cq 
    LEFT JOIN user u ON cq.triggered_by = u.email 
    WHERE cq.userid='$userid' 
    ORDER BY cq.created_date DESC 
    LIMIT 20
");

// Get user's payout history
$payoutHistory = mysqli_query($con, "
    SELECT sp.*, spb.reference as batch_reference, spb.created_date as batch_date
    FROM stitch_payouts sp
    JOIN stitch_payout_batches spb ON sp.batch_id = spb.id
    WHERE sp.userid='$userid'
    ORDER BY sp.created_date DESC
    LIMIT 10
");

// Check if user has payout account setup
$recipientQuery = mysqli_query($con, "SELECT * FROM stitch_recipients WHERE userid='$userid'");
$hasPayoutAccount = mysqli_num_rows($recipientQuery) > 0;
$recipient = $hasPayoutAccount ? mysqli_fetch_assoc($recipientQuery) : null;
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>MLM Website - Payout Dashboard</title>

    <!-- Bootstrap Core CSS -->
    <link href="vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">

    <!-- MetisMenu CSS -->
    <link href="vendor/metisMenu/metisMenu.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="dist/css/sb-admin-2.css" rel="stylesheet">

    <!-- Custom Fonts -->
    <link href="vendor/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css">
</head>

<body>
    <div id="wrapper">
        <!-- Navigation -->
        <?php include('php-includes/menu.php'); ?>

        <!-- Page Content -->
        <div id="page-wrapper">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-lg-12">
                        <h1 class="page-header">Payout Dashboard</h1>
                    </div>
                </div>

                <!-- Statistics Row -->
                <div class="row">
                    <div class="col-lg-3 col-md-6">
                        <div class="panel panel-primary">
                            <div class="panel-heading">
                                <div class="row">
                                    <div class="col-xs-3">
                                        <i class="fa fa-clock-o fa-5x"></i>
                                    </div>
                                    <div class="col-xs-9 text-right">
                                        <div class="huge">R<?php echo number_format($userStats['pending_amount'], 2); ?></div>
                                        <div>Pending Payouts</div>
                                    </div>
                                </div>
                            </div>
                            <div class="panel-footer">
                                <span class="pull-left"><?php echo $userStats['pending_count']; ?> commissions</span>
                                <div class="clearfix"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="panel panel-green">
                            <div class="panel-heading">
                                <div class="row">
                                    <div class="col-xs-3">
                                        <i class="fa fa-check fa-5x"></i>
                                    </div>
                                    <div class="col-xs-9 text-right">
                                        <div class="huge">R<?php echo number_format($userStats['paid_amount'], 2); ?></div>
                                        <div>Total Paid</div>
                                    </div>
                                </div>
                            </div>
                            <div class="panel-footer">
                                <span class="pull-left"><?php echo $userStats['paid_count']; ?> payments</span>
                                <div class="clearfix"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="panel panel-yellow">
                            <div class="panel-heading">
                                <div class="row">
                                    <div class="col-xs-3">
                                        <i class="fa fa-money fa-5x"></i>
                                    </div>
                                    <div class="col-xs-9 text-right">
                                        <div class="huge">R<?php echo number_format($userStats['total_earned'], 2); ?></div>
                                        <div>Total Earned</div>
                                    </div>
                                </div>
                            </div>
                            <div class="panel-footer">
                                <span class="pull-left">All time earnings</span>
                                <div class="clearfix"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="panel panel-<?php echo $hasPayoutAccount ? 'green' : 'red'; ?>">
                            <div class="panel-heading">
                                <div class="row">
                                    <div class="col-xs-3">
                                        <i class="fa fa-bank fa-5x"></i>
                                    </div>
                                    <div class="col-xs-9 text-right">
                                        <div class="huge"><?php echo $hasPayoutAccount ? 'Active' : 'Setup'; ?></div>
                                        <div>Payout Account</div>
                                    </div>
                                </div>
                            </div>
                            <div class="panel-footer">
                                <?php if($hasPayoutAccount): ?>
                                <span class="pull-left">Account configured</span>
                                <?php else: ?>
                                <a href="setup-payout-account.php" class="pull-left">Setup now</a>
                                <?php endif; ?>
                                <div class="clearfix"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if(!$hasPayoutAccount): ?>
                <div class="row">
                    <div class="col-lg-12">
                        <div class="alert alert-warning">
                            <i class="fa fa-exclamation-triangle"></i> 
                            <strong>Action Required:</strong> You need to setup your payout account to receive commission payments. 
                            <a href="setup-payout-account.php" class="btn btn-warning btn-sm">Setup Payout Account</a>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <div class="row">
                    <!-- Recent Commissions -->
                    <div class="col-lg-8">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <i class="fa fa-list fa-fw"></i> Recent Commissions
                            </div>
                            <div class="panel-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Date</th>
                                                <th>Type</th>
                                                <th>Level</th>
                                                <th>Amount</th>
                                                <th>Triggered By</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php while($commission = mysqli_fetch_assoc($recentCommissions)): ?>
                                            <tr>
                                                <td><?php echo date('M j, Y', strtotime($commission['created_date'])); ?></td>
                                                <td>
                                                    <span class="label label-info">
                                                        <?php echo ucfirst($commission['commission_type']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo $commission['level'] ? 'Level ' . $commission['level'] : '-'; ?></td>
                                                <td>R<?php echo number_format($commission['amount'], 2); ?></td>
                                                <td><?php echo $commission['triggered_by_email'] ?? $commission['triggered_by']; ?></td>
                                                <td>
                                                    <?php
                                                    $statusClass = '';
                                                    switch($commission['status']) {
                                                        case 'pending': $statusClass = 'warning'; break;
                                                        case 'processing': $statusClass = 'info'; break;
                                                        case 'paid': $statusClass = 'success'; break;
                                                        case 'failed': $statusClass = 'danger'; break;
                                                    }
                                                    ?>
                                                    <span class="label label-<?php echo $statusClass; ?>">
                                                        <?php echo ucfirst($commission['status']); ?>
                                                    </span>
                                                </td>
                                            </tr>
                                            <?php endwhile; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payout Account Info -->
                    <div class="col-lg-4">
                        <?php if($hasPayoutAccount): ?>
                        <div class="panel panel-success">
                            <div class="panel-heading">
                                <i class="fa fa-bank fa-fw"></i> Payout Account
                            </div>
                            <div class="panel-body">
                                <table class="table table-bordered">
                                    <tr>
                                        <th>Account Holder:</th>
                                        <td><?php echo $recipient['recipient_name']; ?></td>
                                    </tr>
                                    <tr>
                                        <th>Bank:</th>
                                        <td><?php echo $recipient['bank_name']; ?></td>
                                    </tr>
                                    <tr>
                                        <th>Account Number:</th>
                                        <td>****<?php echo substr($recipient['account_number'], -4); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Status:</th>
                                        <td>
                                            <span class="label label-success">
                                                <?php echo ucfirst($recipient['status']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                </table>
                                <a href="setup-payout-account.php" class="btn btn-primary btn-sm">
                                    <i class="fa fa-edit"></i> Update Account
                                </a>
                            </div>
                        </div>
                        <?php endif; ?>

                        <div class="panel panel-info">
                            <div class="panel-heading">
                                <i class="fa fa-info-circle fa-fw"></i> Payout Information
                            </div>
                            <div class="panel-body">
                                <h5>Processing Schedule</h5>
                                <p>Payouts are processed automatically when your pending balance reaches R100 or more.</p>
                                
                                <h5>Processing Time</h5>
                                <p>Payments typically arrive in your bank account within 1-2 business days.</p>
                                
                                <h5>Minimum Payout</h5>
                                <p>R100.00 minimum balance required for automatic processing.</p>
                                
                                <h5>Commission Structure</h5>
                                <ul>
                                    <li>Level 1: R500 per person</li>
                                    <li>Level 2: R200 per person</li>
                                    <li>Level 3: R100 per person</li>
                                    <li>Level 4: R50 per person</li>
                                    <li>Level 5: R25 per person</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payout History -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <i class="fa fa-history fa-fw"></i> Payout History
                            </div>
                            <div class="panel-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Date</th>
                                                <th>Batch Reference</th>
                                                <th>Amount</th>
                                                <th>Currency</th>
                                                <th>Status</th>
                                                <th>Reason</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if(mysqli_num_rows($payoutHistory) > 0): ?>
                                                <?php while($payout = mysqli_fetch_assoc($payoutHistory)): ?>
                                                <tr>
                                                    <td><?php echo date('M j, Y H:i', strtotime($payout['created_date'])); ?></td>
                                                    <td><?php echo $payout['batch_reference']; ?></td>
                                                    <td>R<?php echo number_format($payout['amount'], 2); ?></td>
                                                    <td><?php echo $payout['currency']; ?></td>
                                                    <td>
                                                        <?php
                                                        $statusClass = '';
                                                        switch($payout['status']) {
                                                            case 'created': $statusClass = 'info'; break;
                                                            case 'processing': $statusClass = 'warning'; break;
                                                            case 'completed': $statusClass = 'success'; break;
                                                            case 'failed': $statusClass = 'danger'; break;
                                                            case 'cancelled': $statusClass = 'default'; break;
                                                        }
                                                        ?>
                                                        <span class="label label-<?php echo $statusClass; ?>">
                                                            <?php echo ucfirst($payout['status']); ?>
                                                        </span>
                                                    </td>
                                                    <td><?php echo $payout['status_reason'] ?? '-'; ?></td>
                                                </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                            <tr>
                                                <td colspan="6" class="text-center">No payout history found</td>
                                            </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="vendor/jquery/jquery.min.js"></script>

    <!-- Bootstrap Core JavaScript -->
    <script src="vendor/bootstrap/js/bootstrap.min.js"></script>

    <!-- Metis Menu Plugin JavaScript -->
    <script src="vendor/metisMenu/metisMenu.min.js"></script>

    <!-- Custom Theme JavaScript -->
    <script src="dist/js/sb-admin-2.js"></script>
</body>
</html>
