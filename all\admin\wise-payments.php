<?php
include('php-includes/check-login.php');
require('php-includes/connect.php');
include('../php-includes/wise-api.php');

// Initialize Wise API
$wise = new WiseAPI();

// Get Wise config
$config_query = mysqli_query($con, "SELECT * FROM wise_config WHERE id = 1");
$config = mysqli_num_rows($config_query) > 0 ? mysqli_fetch_assoc($config_query) : null;

// Handle batch payment processing
if(isset($_POST['process_payments'])) {
    $selected_users = isset($_POST['selected_users']) ? $_POST['selected_users'] : [];
    
    if(empty($selected_users)) {
        $error_message = "No users selected for payment.";
    } else {
        // Get payment details for selected users
        $payments = [];
        
        foreach($selected_users as $userid) {
            $userid = mysqli_real_escape_string($con, $userid);
            
            // Get user's current balance
            $balance_query = mysqli_query($con, "SELECT current_bal FROM income WHERE userid='$userid'");
            
            if(mysqli_num_rows($balance_query) > 0) {
                $balance = mysqli_fetch_assoc($balance_query);
                $amount = $balance['current_bal'];
                
                if($amount > 0) {
                    // Check if user has a Wise recipient account
                    $recipient_query = mysqli_query($con, "SELECT * FROM wise_recipients WHERE userid='$userid'");
                    
                    if(mysqli_num_rows($recipient_query) > 0) {
                        $payments[] = [
                            'userid' => $userid,
                            'amount' => $amount
                        ];
                    }
                }
            }
        }
        
        if(empty($payments)) {
            $error_message = "No eligible payments found for selected users.";
        } else {
            // Process batch payments
            $currency = $config ? $config['default_currency'] : 'USD';
            $result = $wise->processBatchPayments($payments, $currency);
            
            if($result['success']) {
                // Record batch payment
                $batch_group_id = $result['batch_group_id'];
                $total_amount = array_sum(array_column($payments, 'amount'));
                
                $batch_insert = mysqli_query($con, "INSERT INTO wise_batch_payments 
                    (batch_group_id, status, source_currency, total_amount, completed_date) 
                    VALUES ('$batch_group_id', 'completed', '$currency', $total_amount, NOW())");
                
                $batch_id = mysqli_insert_id($con);
                
                // Record individual transfers
                foreach($result['transfers'] as $transfer) {
                    $userid = mysqli_real_escape_string($con, $transfer['userid']);
                    $amount = $transfer['amount'];
                    $transfer_id = $transfer['transfer_id'];
                    
                    // Insert transfer record
                    mysqli_query($con, "INSERT INTO wise_transfers 
                        (batch_id, userid, transfer_id, amount, status, updated_date) 
                        VALUES ($batch_id, '$userid', '$transfer_id', $amount, 'processing', NOW())");
                    
                    // Insert income received record
                    $date = date("Y-m-d");
                    mysqli_query($con, "INSERT INTO income_received 
                        (userid, amount, date, wise_transfer_id, wise_payment_status) 
                        VALUES ('$userid', $amount, '$date', '$transfer_id', 'processing')");
                    
                    // Update user's balance
                    mysqli_query($con, "UPDATE income SET current_bal=0 WHERE userid='$userid'");
                }
                
                $success_message = "Batch payment processed successfully. " . count($result['transfers']) . " payments initiated.";
                
                if(!empty($result['errors'])) {
                    $warning_message = "Some payments could not be processed: " . implode(", ", $result['errors']);
                }
            } else {
                $error_message = "Failed to process batch payment: " . implode(", ", $result['errors']);
            }
        }
    }
}

// Get users with balances
$users_query = mysqli_query($con, "SELECT i.userid, i.current_bal, i.total_bal, u.mobile 
                                  FROM income i 
                                  JOIN user u ON i.userid = u.email 
                                  WHERE i.current_bal > 0 
                                  ORDER BY i.current_bal DESC");

// Get recent batch payments
$batches_query = mysqli_query($con, "SELECT * FROM wise_batch_payments ORDER BY created_date DESC LIMIT 10");
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>MLM Admin - Wise Batch Payments</title>

    <!-- Bootstrap Core CSS -->
    <link href="vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">

    <!-- MetisMenu CSS -->
    <link href="vendor/metisMenu/metisMenu.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="dist/css/sb-admin-2.css" rel="stylesheet">

    <!-- Custom Fonts -->
    <link href="vendor/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css">
</head>

<body>

    <div id="wrapper">

        <!-- Navigation -->
        <?php include('php-includes/menu.php'); ?>

        <!-- Page Content -->
        <div id="page-wrapper">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-lg-12">
                        <h1 class="page-header">Wise Batch Payments</h1>
                    </div>
                    <!-- /.col-lg-12 -->
                </div>
                <!-- /.row -->
                
                <?php if(!$config): ?>
                <div class="alert alert-warning">
                    <strong>Warning!</strong> Wise API is not configured. Please <a href="wise-config.php">configure Wise API</a> before processing payments.
                </div>
                <?php endif; ?>
                
                <?php if(isset($success_message)): ?>
                <div class="alert alert-success">
                    <?php echo $success_message; ?>
                </div>
                <?php endif; ?>
                
                <?php if(isset($warning_message)): ?>
                <div class="alert alert-warning">
                    <?php echo $warning_message; ?>
                </div>
                <?php endif; ?>
                
                <?php if(isset($error_message)): ?>
                <div class="alert alert-danger">
                    <?php echo $error_message; ?>
                </div>
                <?php endif; ?>
                
                <div class="row">
                    <div class="col-lg-12">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                Users with Available Balance
                            </div>
                            <div class="panel-body">
                                <form method="post" action="">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-bordered table-hover">
                                            <thead>
                                                <tr>
                                                    <th><input type="checkbox" id="select-all"></th>
                                                    <th>User ID</th>
                                                    <th>Mobile</th>
                                                    <th>Current Balance</th>
                                                    <th>Total Balance</th>
                                                    <th>Recipient Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php
                                                if(mysqli_num_rows($users_query) > 0) {
                                                    while($user = mysqli_fetch_assoc($users_query)) {
                                                        // Check if user has a Wise recipient account
                                                        $recipient_query = mysqli_query($con, "SELECT * FROM wise_recipients WHERE userid='" . $user['userid'] . "'");
                                                        $has_recipient = mysqli_num_rows($recipient_query) > 0;
                                                        
                                                        echo '<tr>';
                                                        echo '<td><input type="checkbox" name="selected_users[]" value="' . $user['userid'] . '" ' . ($has_recipient ? '' : 'disabled') . '></td>';
                                                        echo '<td>' . $user['userid'] . '</td>';
                                                        echo '<td>' . $user['mobile'] . '</td>';
                                                        echo '<td>' . $user['current_bal'] . '</td>';
                                                        echo '<td>' . $user['total_bal'] . '</td>';
                                                        echo '<td>' . ($has_recipient ? 
                                                            '<span class="label label-success">Ready</span>' : 
                                                            '<span class="label label-warning">No Recipient Account</span>') . '</td>';
                                                        echo '</tr>';
                                                    }
                                                } else {
                                                    echo '<tr><td colspan="6" class="text-center">No users with available balance</td></tr>';
                                                }
                                                ?>
                                            </tbody>
                                        </table>
                                    </div>
                                    
                                    <button type="submit" name="process_payments" class="btn btn-primary" <?php echo $config ? '' : 'disabled'; ?>>
                                        Process Payments for Selected Users
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-lg-12">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                Recent Batch Payments
                            </div>
                            <div class="panel-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th>Batch ID</th>
                                                <th>Wise Batch Group ID</th>
                                                <th>Status</th>
                                                <th>Currency</th>
                                                <th>Total Amount</th>
                                                <th>Created Date</th>
                                                <th>Completed Date</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                            if(mysqli_num_rows($batches_query) > 0) {
                                                while($batch = mysqli_fetch_assoc($batches_query)) {
                                                    echo '<tr>';
                                                    echo '<td>' . $batch['id'] . '</td>';
                                                    echo '<td>' . $batch['batch_group_id'] . '</td>';
                                                    echo '<td>' . $batch['status'] . '</td>';
                                                    echo '<td>' . $batch['source_currency'] . '</td>';
                                                    echo '<td>' . $batch['total_amount'] . '</td>';
                                                    echo '<td>' . $batch['created_date'] . '</td>';
                                                    echo '<td>' . ($batch['completed_date'] ? $batch['completed_date'] : 'N/A') . '</td>';
                                                    echo '<td>
                                                        <a href="wise-payment-details.php?batch_id=' . $batch['id'] . '" class="btn btn-info btn-xs">
                                                            <i class="fa fa-search"></i> View Details
                                                        </a>
                                                    </td>';
                                                    echo '</tr>';
                                                }
                                            } else {
                                                echo '<tr><td colspan="8" class="text-center">No batch payments found</td></tr>';
                                            }
                                            ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- /.row -->
            </div>
            <!-- /.container-fluid -->
        </div>
        <!-- /#page-wrapper -->

    </div>
    <!-- /#wrapper -->

    <!-- jQuery -->
    <script src="vendor/jquery/jquery.min.js"></script>

    <!-- Bootstrap Core JavaScript -->
    <script src="vendor/bootstrap/js/bootstrap.min.js"></script>

    <!-- Metis Menu Plugin JavaScript -->
    <script src="vendor/metisMenu/metisMenu.min.js"></script>

    <!-- Custom Theme JavaScript -->
    <script src="dist/js/sb-admin-2.js"></script>
    
    <script>
        // Select all checkbox functionality
        document.getElementById('select-all').addEventListener('change', function() {
            var checkboxes = document.querySelectorAll('input[name="selected_users[]"]');
            for (var i = 0; i < checkboxes.length; i++) {
                if (!checkboxes[i].disabled) {
                    checkboxes[i].checked = this.checked;
                }
            }
        });
    </script>

</body>

</html>
